{"name": "bin-version-check", "version": "5.1.0", "description": "Check whether a binary version satisfies a semver range", "license": "MIT", "repository": "sindresorhus/bin-version-check", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["cli", "binary", "executable", "version", "semver", "semantic", "range", "satisfy", "check", "validate"], "dependencies": {"bin-version": "^6.0.0", "semver": "^7.5.3", "semver-truncate": "^3.0.0"}, "devDependencies": {"ava": "^4.3.3", "xo": "^0.45.0"}}