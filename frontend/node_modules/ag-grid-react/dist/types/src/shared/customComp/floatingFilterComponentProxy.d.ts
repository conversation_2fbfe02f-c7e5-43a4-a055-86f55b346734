import type { IFloatingFilter, IFloatingFilterParams } from 'ag-grid-community';
import type { CustomFloatingFilterCallbacks, CustomFloatingFilterProps } from './interfaces';
export declare function updateFloatingFilterParent(params: IFloatingFilterParams, model: any): void;
export declare class FloatingFilterComponentProxy implements IFloatingFilter {
    private floatingFilterParams;
    private readonly refreshProps;
    private model;
    private readonly onModelChange;
    constructor(floatingFilterParams: IFloatingFilterParams, refreshProps: () => void);
    getProps(): CustomFloatingFilterProps;
    onParentModelChanged(parentModel: any): void;
    refresh(params: IFloatingFilterParams): void;
    setMethods(methods: CustomFloatingFilterCallbacks): void;
    private getOptionalMethods;
    private updateModel;
}
