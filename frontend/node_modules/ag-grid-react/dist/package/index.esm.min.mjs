import Qt,{Component as Yt}from"react";import{BaseComponentWrapper as Zt,GridCoreCreator as Xt,ModuleRegistry as es,VanillaFrameworkOverrides as ts,_combineAttributesAndGridOptions as ss,_getGlobalGridOption as rs,_isClientSideRowModel as ns,_isServerSideRowModel as os,_processOnChange as as,_warnOnce as cs}from"ag-grid-community";import $e,{forwardRef as is,useCallback as Je,useContext as ls,useEffect as us,useImperativeHandle as ds,useMemo as Ke,useRef as ee,useState as Ee}from"react";import{_escapeString as ps}from"ag-grid-community";import ue,{forwardRef as ms,useCallback as hs,useContext as Cs,useImperativeHandle as fs,useLayoutEffect as gs,useMemo as qe,useRef as Pe,useState as Ce}from"react";import ys from"react";var q=ys.createContext({}),Ie=(e,t,s,n)=>{if(!e||e.componentFromFramework||t.isDestroyed())return;const d=e.newAgStackInstance();if(d==null)return;let c,o,u=!1;return d.then(a=>{if(u){t.destroyBean(a);return}c=a,o=c.getGui(),s.appendChild(o),bt(n,c)}),()=>{u=!0,c&&(o?.parentElement?.removeChild(o),t.destroyBean(c),n&&bt(n,void 0))}},bt=(e,t)=>{if(e)if(e instanceof Function)e(t);else{const s=e;s.current=t}};import vs from"react";import Rs from"react-dom";var se=(...e)=>e.filter(s=>s!=null&&s!=="").join(" "),te=class $t{constructor(...t){this.classesMap={},t.forEach(s=>{this.classesMap[s]=!0})}setClass(t,s){if(!!this.classesMap[t]==s)return this;const r=new $t;return r.classesMap={...this.classesMap},r.classesMap[t]=s,r}toString(){return Object.keys(this.classesMap).filter(s=>this.classesMap[s]).join(" ")}},be=e=>{const t=()=>typeof Symbol=="function"&&Symbol.for,s=()=>t()?Symbol.for("react.memo"):60115;return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===s()},at=vs.version?.split(".")[0],ws=at==="16"||at==="17";function Es(){return at==="19"}var Qe=!1;function Ps(e){return Qe||setTimeout(()=>Qe=!1,0),Qe=!0,e()}var ct=(e,t)=>{!ws&&e&&!Qe?Rs.flushSync(t):t()};function it(e,t,s){if(t==null||e==null)return t;if(e===t||t.length===0&&e.length===0)return e;if(s||e.length===0&&t.length>0||e.length>0&&t.length===0)return t;const n=[],r=[],d=new Map,c=new Map;for(let o=0;o<t.length;o++){const u=t[o];c.set(u.instanceId,u)}for(let o=0;o<e.length;o++){const u=e[o];d.set(u.instanceId,u),c.has(u.instanceId)&&n.push(u)}for(let o=0;o<t.length;o++){const u=t[o],a=u.instanceId;d.has(a)||r.push(u)}return n.length===e.length&&r.length===0?e:n.length===0&&r.length===t.length?t:n.length===0?r:r.length===0?n:[...n,...r]}var St=ms((e,t)=>{const{ctrlsFactory:s,context:n}=Cs(q),r=Pe(null),d=Pe(null),c=Pe(null),o=Pe(null),u=Pe(null),a=Pe(),[l,f]=Ce(),[R,y]=Ce(),[m,h]=Ce(),[i,C]=Ce(()=>new te),[w,v]=Ce(()=>new te("ag-hidden")),[E,P]=Ce(()=>new te("ag-hidden")),[O,b]=Ce(()=>new te("ag-invisible"));fs(t,()=>({refresh(){return!1}})),gs(()=>Ie(l,n,d.current),[l]);const k=hs(x=>{if(r.current=x,!x){a.current=n.destroyBean(a.current);return}const G={setInnerRenderer:(A,T)=>{f(A),h(T)},setChildCount:A=>y(A),addOrRemoveCssClass:(A,T)=>C(W=>W.setClass(A,T)),setContractedDisplayed:A=>P(T=>T.setClass("ag-hidden",!A)),setExpandedDisplayed:A=>v(T=>T.setClass("ag-hidden",!A)),setCheckboxVisible:A=>b(T=>T.setClass("ag-invisible",!A))},U=s.getInstance("groupCellRendererCtrl");U&&(a.current=n.createBean(U),a.current.init(G,x,c.current,o.current,u.current,St,e))},[]),M=qe(()=>`ag-cell-wrapper ${i.toString()}`,[i]),p=qe(()=>`ag-group-expanded ${w.toString()}`,[w]),g=qe(()=>`ag-group-contracted ${E.toString()}`,[E]),F=qe(()=>`ag-group-checkbox ${O.toString()}`,[O]),J=l&&l.componentFromFramework,I=J?l.componentClass:void 0,_=l==null&&m!=null,B=ps(m,!0);return ue.createElement("span",{className:M,ref:k,...e.colDef?{}:{role:a.current?.getCellAriaRole()}},ue.createElement("span",{className:p,ref:o}),ue.createElement("span",{className:g,ref:u}),ue.createElement("span",{className:F,ref:c}),ue.createElement("span",{className:"ag-group-value",ref:d},_&&ue.createElement(ue.Fragment,null,B),J&&ue.createElement(I,{...l.params})),ue.createElement("span",{className:"ag-group-child-count"},R))}),kt=St;import{AgPromise as lt}from"ag-grid-community";import Ft,{memo as bs,useEffect as Ss,useState as ks}from"react";import{createContext as Fs}from"react";var Oe=Fs({setMethods:()=>{}}),Ms=e=>{const{initialProps:t,addUpdateCallback:s,CustomComponentClass:n,setMethods:r}=e,[{key:d,...c},o]=ks(t);return Ss(()=>{s(u=>o(u))},[]),Ft.createElement(Oe.Provider,{value:{setMethods:r}},Ft.createElement(n,{key:d,...c}))},Bs=bs(Ms);import{AgPromise as ut,_warnOnce as Mt}from"ag-grid-community";import{createElement as xs}from"react";import{createPortal as Gs}from"react-dom";var As=0;function Bt(){return`agPortalKey_${++As}`}var xt=class{constructor(e,t,s,n){this.portal=null,this.oldPortal=null,this.reactComponent=e,this.portalManager=t,this.componentType=s,this.suppressFallbackMethods=!!n,this.statelessComponent=this.isStateless(this.reactComponent),this.key=Bt(),this.portalKey=Bt(),this.instanceCreated=this.isStatelessComponent()?ut.resolve(!1):new ut(r=>{this.resolveInstanceCreated=r})}getGui(){return this.eParentElement}getRootElement(){return this.eParentElement.firstChild}destroy(){this.componentInstance&&typeof this.componentInstance.destroy=="function"&&this.componentInstance.destroy();const e=this.portal;e&&this.portalManager.destroyPortal(e)}createParentElement(e){const t=this.portalManager.getComponentWrappingElement(),s=document.createElement(t||"div");return s.classList.add("ag-react-container"),e.reactContainer=s,s}addParentContainerStyleAndClasses(){this.componentInstance&&(this.componentInstance.getReactContainerStyle&&this.componentInstance.getReactContainerStyle()&&(Mt('Since v31.1 "getReactContainerStyle" is deprecated. Apply styling directly to ".ag-react-container" if needed.'),Object.assign(this.eParentElement.style,this.componentInstance.getReactContainerStyle())),this.componentInstance.getReactContainerClasses&&this.componentInstance.getReactContainerClasses()&&(Mt('Since v31.1 "getReactContainerClasses" is deprecated. Apply styling directly to ".ag-react-container" if needed.'),this.componentInstance.getReactContainerClasses().forEach(t=>this.eParentElement.classList.add(t))))}statelessComponentRendered(){return this.eParentElement.childElementCount>0||this.eParentElement.childNodes.length>0}getFrameworkComponentInstance(){return this.componentInstance}isStatelessComponent(){return this.statelessComponent}getReactComponentName(){return this.reactComponent.name}getMemoType(){return this.hasSymbol()?Symbol.for("react.memo"):60115}hasSymbol(){return typeof Symbol=="function"&&Symbol.for}isStateless(e){return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===this.getMemoType()}hasMethod(e){const t=this.getFrameworkComponentInstance();return!!t&&t[e]!=null||this.fallbackMethodAvailable(e)}callMethod(e,t){const s=this.getFrameworkComponentInstance();if(this.isStatelessComponent())return this.fallbackMethod(e,t&&t[0]?t[0]:{});if(!s){setTimeout(()=>this.callMethod(e,t));return}const n=s[e];if(n)return n.apply(s,t);if(this.fallbackMethodAvailable(e))return this.fallbackMethod(e,t&&t[0]?t[0]:{})}addMethod(e,t){this[e]=t}init(e){return this.eParentElement=this.createParentElement(e),this.createOrUpdatePortal(e),new ut(t=>this.createReactComponent(t))}createOrUpdatePortal(e){this.isStatelessComponent()||(this.ref=t=>{this.componentInstance=t,this.addParentContainerStyleAndClasses(),this.resolveInstanceCreated?.(!0),this.resolveInstanceCreated=void 0},e.ref=this.ref),this.reactElement=this.createElement(this.reactComponent,{...e,key:this.key}),this.portal=Gs(this.reactElement,this.eParentElement,this.portalKey)}createElement(e,t){return xs(e,t)}createReactComponent(e){this.portalManager.mountReactPortal(this.portal,this,e)}rendered(){return this.isStatelessComponent()&&this.statelessComponentRendered()||!!(!this.isStatelessComponent()&&this.getFrameworkComponentInstance())}refreshComponent(e){this.oldPortal=this.portal,this.createOrUpdatePortal(e),this.portalManager.updateReactPortal(this.oldPortal,this.portal)}fallbackMethod(e,t){const s=this[`${e}Component`];if(!this.suppressFallbackMethods&&s)return s.bind(this)(t)}fallbackMethodAvailable(e){return this.suppressFallbackMethods?!1:!!this[`${e}Component`]}};function dt(e,t,s){e.forEach(n=>{const r=t[n];r&&(s[n]=r)})}var ae=class extends xt{constructor(){super(...arguments),this.awaitUpdateCallback=new lt(e=>{this.resolveUpdateCallback=e}),this.wrapperComponent=Bs}init(e){return this.sourceParams=e,super.init(this.getProps())}addMethod(){}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}getFrameworkComponentInstance(){return this}createElement(e,t){return super.createElement(this.wrapperComponent,{initialProps:t,CustomComponentClass:e,setMethods:s=>this.setMethods(s),addUpdateCallback:s=>{this.updateCallback=()=>(s(this.getProps()),new lt(n=>{setTimeout(()=>{n()})})),this.resolveUpdateCallback()}})}setMethods(e){this.providedMethods=e,dt(this.getOptionalMethods(),this.providedMethods,this)}getOptionalMethods(){return[]}getProps(){return{...this.sourceParams,key:this.key,ref:this.ref}}refreshProps(){return this.updateCallback?this.updateCallback():new lt(e=>this.awaitUpdateCallback.then(()=>{this.updateCallback().then(()=>e())}))}},Is=class extends ae{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},Os=class extends ae{constructor(){super(...arguments),this.date=null,this.onDateChange=e=>this.updateDate(e)}getDate(){return this.date}setDate(e){this.date=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached","setInputPlaceholder","setInputAriaLabel","setDisabled"]}updateDate(e){this.setDate(e),this.sourceParams.onDateChanged()}getProps(){const e=super.getProps();return e.date=this.date,e.onDateChange=this.onDateChange,delete e.onDateChanged,e}},Ds=class extends ae{constructor(){super(...arguments),this.label="",this.icon=null,this.shake=!1}setIcon(e,t){this.icon=e,this.shake=t,this.refreshProps()}setLabel(e){this.label=e,this.refreshProps()}getProps(){const e=super.getProps(),{label:t,icon:s,shake:n}=this;return e.label=t,e.icon=s,e.shake=n,e}},Ws=class extends ae{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e),this.onUiChange=()=>this.sourceParams.filterChangedCallback(),this.expectingNewMethods=!0,this.hasBeenActive=!1}isFilterActive(){return this.model!=null}doesFilterPass(e){return this.providedMethods.doesFilterPass(e)}getModel(){return this.model}setModel(e){return this.expectingNewMethods=!0,this.model=e,this.hasBeenActive||(this.hasBeenActive=this.isFilterActive()),this.refreshProps()}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}getOptionalMethods(){return["afterGuiAttached","afterGuiDetached","onNewRowsLoaded","getModelAsString","onAnyFilterChanged"]}setMethods(e){this.expectingNewMethods===!1&&this.hasBeenActive&&this.providedMethods?.doesFilterPass!==e?.doesFilterPass&&setTimeout(()=>{this.sourceParams.filterChangedCallback()}),this.expectingNewMethods=!1,super.setMethods(e)}updateModel(e){this.setModel(e).then(()=>this.sourceParams.filterChangedCallback())}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e.onUiChange=this.onUiChange,delete e.filterChangedCallback,delete e.filterModifiedCallback,delete e.valueGetter,e}};import{AgPromise as Ts}from"ag-grid-community";function Gt(e,t){e.parentFilterInstance(s=>{(s.setModel(t)||Ts.resolve()).then(()=>{e.filterParams.filterChangedCallback()})})}var Ns=class{constructor(e,t){this.floatingFilterParams=e,this.refreshProps=t,this.model=null,this.onModelChange=s=>this.updateModel(s)}getProps(){return{...this.floatingFilterParams,model:this.model,onModelChange:this.onModelChange}}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.floatingFilterParams=e,this.refreshProps()}setMethods(e){dt(this.getOptionalMethods(),e,this)}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),Gt(this.floatingFilterParams,e)}},Hs=class extends ae{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e)}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),Gt(this.sourceParams,e)}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e}},_s=class extends ae{refresh(e){this.sourceParams=e,this.refreshProps()}},Us=class extends ae{constructor(){super(...arguments),this.active=!1,this.expanded=!1,this.onActiveChange=e=>this.updateActive(e)}setActive(e){this.awaitSetActive(e)}setExpanded(e){this.expanded=e,this.refreshProps()}getOptionalMethods(){return["select","configureDefaults"]}awaitSetActive(e){return this.active=e,this.refreshProps()}updateActive(e){const t=this.awaitSetActive(e);e&&t.then(()=>this.sourceParams.onItemActivated())}getProps(){const e=super.getProps();return e.active=this.active,e.expanded=this.expanded,e.onActiveChange=this.onActiveChange,delete e.onItemActivated,e}},Ls=class extends ae{refresh(e){this.sourceParams=e,this.refreshProps()}},Vs=class extends ae{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},js=class extends ae{constructor(){super(...arguments),this.onStateChange=e=>this.updateState(e)}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}getState(){return this.state}updateState(e){this.state=e,this.refreshProps(),this.sourceParams.onStateUpdated()}getProps(){const e=super.getProps();return e.state=this.state,e.onStateChange=this.onStateChange,e}};import{AgPromise as zs,_warnOnce as $s}from"ag-grid-community";function Js(e,t){(e?.getInstance?.()??zs.resolve(void 0)).then(n=>t(n))}function Ye(){$s("As of v32, using custom components with `reactiveCustomComponents = false` is deprecated.")}var Ks=1e3,qs=class{constructor(e,t,s){this.destroyed=!1,this.portals=[],this.hasPendingPortalUpdate=!1,this.wrappingElement=t||"div",this.refresher=e,this.maxComponentCreationTimeMs=s||Ks}getPortals(){return this.portals}destroy(){this.destroyed=!0}destroyPortal(e){this.portals=this.portals.filter(t=>t!==e),this.batchUpdate()}getComponentWrappingElement(){return this.wrappingElement}mountReactPortal(e,t,s){this.portals=[...this.portals,e],this.waitForInstance(t,s),this.batchUpdate()}updateReactPortal(e,t){this.portals[this.portals.indexOf(e)]=t,this.batchUpdate()}batchUpdate(){this.hasPendingPortalUpdate||(setTimeout(()=>{this.destroyed||(this.refresher(),this.hasPendingPortalUpdate=!1)}),this.hasPendingPortalUpdate=!0)}waitForInstance(e,t,s=Date.now()){if(this.destroyed){t(null);return}if(e.rendered())t(e);else{if(Date.now()-s>=this.maxComponentCreationTimeMs&&!this.hasPendingPortalUpdate)return;window.setTimeout(()=>{this.waitForInstance(e,t,s)})}}};import{GridCtrl as Qs}from"ag-grid-community";import De,{memo as Ys,useCallback as pt,useEffect as Zs,useMemo as Ze,useRef as Se,useState as he}from"react";import{CssClassManager as Xs,FakeHScrollComp as er,FakeVScrollComp as tr,GridBodyCtrl as sr,OverlayWrapperComponent as rr,_setAriaColCount as nr,_setAriaRowCount as or}from"ag-grid-community";import We,{memo as ar,useCallback as cr,useContext as ir,useMemo as re,useRef as ce,useState as Q}from"react";import{GridHeaderCtrl as lr}from"ag-grid-community";import Xe,{memo as ur,useCallback as dr,useContext as pr,useMemo as At,useRef as It,useState as Ot}from"react";import{HeaderRowContainerCtrl as mr}from"ag-grid-community";import fe,{memo as hr,useCallback as Cr,useContext as fr,useRef as mt,useState as Dt}from"react";import{HeaderRowType as Wt,_EmptyBean as gr}from"ag-grid-community";import et,{memo as yr,useCallback as Tt,useContext as vr,useMemo as Nt,useRef as tt,useState as ht}from"react";import{CssClassManager as Rr,_EmptyBean as wr,_removeAriaSort as Er,_setAriaSort as Pr}from"ag-grid-community";import Te,{memo as br,useCallback as Sr,useContext as kr,useEffect as Fr,useLayoutEffect as Mr,useMemo as Br,useRef as ke,useState as xr}from"react";var Gr=({ctrl:e})=>{const t=e.isAlive(),{context:s}=kr(q),n=t?e.getColId():void 0,[r,d]=xr(),c=ke(),o=ke(null),u=ke(null),a=ke(null),l=ke(),f=ke();t&&!f.current&&(f.current=new Rr(()=>o.current));const R=Sr(i=>{if(o.current=i,c.current=i?s.createBean(new wr):s.destroyBean(c.current),!i||!t)return;const C={setWidth:v=>{o.current&&(o.current.style.width=v)},addOrRemoveCssClass:(v,E)=>f.current.addOrRemoveCssClass(v,E),setAriaSort:v=>{o.current&&(v?Pr(o.current,v):Er(o.current))},setUserCompDetails:v=>d(v),getUserCompInstance:()=>l.current||void 0};e.setComp(C,i,u.current,a.current,c.current);const w=e.getSelectAllGui();u.current?.insertAdjacentElement("afterend",w),c.current.addDestroyFunc(()=>w.remove())},[]);Mr(()=>Ie(r,s,a.current,l),[r]),Fr(()=>{e.setDragSource(o.current)},[r]);const y=Br(()=>!!(r?.componentFromFramework&&be(r.componentClass)),[r]),m=r&&r.componentFromFramework,h=r&&r.componentClass;return Te.createElement("div",{ref:R,className:"ag-header-cell","col-id":n,role:"columnheader"},Te.createElement("div",{ref:u,className:"ag-header-cell-resize",role:"presentation"}),Te.createElement("div",{ref:a,className:"ag-header-cell-comp-wrapper",role:"presentation"},m&&y&&Te.createElement(h,{...r.params}),m&&!y&&Te.createElement(h,{...r.params,ref:l})))},Ar=br(Gr);import{AgPromise as Ir,_EmptyBean as Or}from"ag-grid-community";import ge,{memo as Dr,useCallback as Wr,useContext as Tr,useLayoutEffect as Nr,useMemo as Fe,useRef as ye,useState as Me}from"react";var Hr=({ctrl:e})=>{const{context:t,gos:s}=Tr(q),[n,r]=Me(()=>new te("ag-header-cell","ag-floating-filter")),[d,c]=Me(()=>new te),[o,u]=Me(()=>new te("ag-floating-filter-button","ag-hidden")),[a,l]=Me("false"),[f,R]=Me(),[,y]=Me(1),m=ye(),h=ye(null),i=ye(null),C=ye(null),w=ye(null),v=ye(),E=ye(),P=B=>{B!=null&&v.current&&v.current(B)},O=Wr(B=>{if(h.current=B,m.current=B?t.createBean(new Or):t.destroyBean(m.current),!B)return;E.current=new Ir(G=>{v.current=G});const x={addOrRemoveCssClass:(G,U)=>r(A=>A.setClass(G,U)),addOrRemoveBodyCssClass:(G,U)=>c(A=>A.setClass(G,U)),setButtonWrapperDisplayed:G=>{u(U=>U.setClass("ag-hidden",!G)),l(G?"false":"true")},setWidth:G=>{h.current&&(h.current.style.width=G)},setCompDetails:G=>R(G),getFloatingFilterComp:()=>E.current?E.current:null,setMenuIcon:G=>w.current?.appendChild(G)};e.setComp(x,B,w.current,i.current,m.current)},[]);Nr(()=>Ie(f,t,i.current,P),[f]);const b=Fe(()=>n.toString(),[n]),k=Fe(()=>d.toString(),[d]),M=Fe(()=>o.toString(),[o]),p=Fe(()=>!!(f&&f.componentFromFramework&&be(f.componentClass)),[f]),g=Fe(()=>s.get("reactiveCustomComponents"),[]),F=Fe(()=>{if(f)if(g){const B=new Ns(f.params,()=>y(x=>x+1));return P(B),B}else f.componentFromFramework&&Ye()},[f]),J=F?.getProps(),I=f&&f.componentFromFramework,_=f&&f.componentClass;return ge.createElement("div",{ref:O,className:b,role:"gridcell"},ge.createElement("div",{ref:i,className:k,role:"presentation"},I&&!g&&ge.createElement(_,{...f.params,ref:p?()=>{}:P}),I&&g&&ge.createElement(Oe.Provider,{value:{setMethods:B=>F.setMethods(B)}},ge.createElement(_,{...J}))),ge.createElement("div",{ref:C,"aria-hidden":a,className:M,role:"presentation"},ge.createElement("button",{ref:w,type:"button",className:"ag-button ag-floating-filter-button-button",tabIndex:-1})))},_r=Dr(Hr);import{_EmptyBean as Ur}from"ag-grid-community";import Ne,{memo as Lr,useCallback as Vr,useContext as jr,useEffect as zr,useLayoutEffect as $r,useMemo as st,useRef as He,useState as _e}from"react";var Jr=({ctrl:e})=>{const{context:t}=jr(q),[s,n]=_e(()=>new te),[r,d]=_e(()=>new te),[c,o]=_e("false"),[u,a]=_e(),[l,f]=_e(),R=st(()=>e.getColId(),[]),y=He(),m=He(null),h=He(null),i=He(null),C=He(),w=Vr(k=>{if(m.current=k,y.current=k?t.createBean(new Ur):t.destroyBean(y.current),!k)return;const M={setWidth:p=>{m.current&&(m.current.style.width=p)},addOrRemoveCssClass:(p,g)=>n(F=>F.setClass(p,g)),setHeaderWrapperHidden:p=>{const g=i.current;g&&(p?g.style.setProperty("display","none"):g.style.removeProperty("display"))},setHeaderWrapperMaxHeight:p=>{const g=i.current;g&&(p!=null?g.style.setProperty("max-height",`${p}px`):g.style.removeProperty("max-height"),g.classList.toggle("ag-header-cell-comp-wrapper-limited-height",p!=null))},setUserCompDetails:p=>f(p),setResizableDisplayed:p=>{d(g=>g.setClass("ag-hidden",!p)),o(p?"false":"true")},setAriaExpanded:p=>a(p),getUserCompInstance:()=>C.current||void 0};e.setComp(M,k,h.current,i.current,y.current)},[]);$r(()=>Ie(l,t,i.current),[l]),zr(()=>{m.current&&e.setDragSource(m.current)},[l]);const v=st(()=>!!(l?.componentFromFramework&&be(l.componentClass)),[l]),E=st(()=>"ag-header-group-cell "+s.toString(),[s]),P=st(()=>"ag-header-cell-resize "+r.toString(),[r]),O=l&&l.componentFromFramework,b=l&&l.componentClass;return Ne.createElement("div",{ref:w,className:E,"col-id":R,role:"columnheader","aria-expanded":u},Ne.createElement("div",{ref:i,className:"ag-header-cell-comp-wrapper",role:"presentation"},O&&v&&Ne.createElement(b,{...l.params}),O&&!v&&Ne.createElement(b,{...l.params,ref:C})),Ne.createElement("div",{ref:h,"aria-hidden":c,className:P}))},Kr=Lr(Jr),qr=({ctrl:e})=>{const{context:t}=vr(q),{topOffset:s,rowHeight:n}=Nt(()=>e.getTopAndHeight(),[]),r=e.getAriaRowIndex(),d=e.getHeaderRowClass(),[c,o]=ht(()=>n+"px"),[u,a]=ht(()=>s+"px"),l=tt(null),f=tt(null),[R,y]=ht(()=>e.getHeaderCtrls()),m=tt(),h=tt(null),i=Tt(v=>{if(h.current=v,m.current=v?t.createBean(new gr):t.destroyBean(m.current),!v)return;const E={setHeight:P=>o(P),setTop:P=>a(P),setHeaderCtrls:(P,O,b)=>{f.current=l.current,l.current=P;const k=it(f.current,P,O);k!==f.current&&ct(b,()=>y(k))},setWidth:P=>{h.current&&(h.current.style.width=P)}};e.setComp(E,m.current,!1)},[]),C=Nt(()=>({height:c,top:u}),[c,u]),w=Tt(v=>{switch(e.getType()){case Wt.COLUMN_GROUP:return et.createElement(Kr,{ctrl:v,key:v.instanceId});case Wt.FLOATING_FILTER:return et.createElement(_r,{ctrl:v,key:v.instanceId});default:return et.createElement(Ar,{ctrl:v,key:v.instanceId})}},[]);return et.createElement("div",{ref:i,className:d,role:"row",style:C,"aria-rowindex":r},R.map(w))},Qr=yr(qr),Yr=({pinned:e})=>{const[t,s]=Dt(!0),[n,r]=Dt([]),{context:d}=fr(q),c=mt(null),o=mt(null),u=mt(),a=e==="left",l=e==="right",f=!a&&!l,R=Cr(h=>{if(c.current=h,u.current=h?d.createBean(new mr(e)):d.destroyBean(u.current),!h)return;const i={setDisplayed:s,setCtrls:C=>r(C),setCenterWidth:C=>{o.current&&(o.current.style.width=C)},setViewportScrollLeft:C=>{c.current&&(c.current.scrollLeft=C)},setPinnedContainerWidth:C=>{c.current&&(c.current.style.width=C,c.current.style.minWidth=C,c.current.style.maxWidth=C)}};u.current.setComp(i,c.current)},[]),y=t?"":"ag-hidden",m=()=>n.map(h=>fe.createElement(Qr,{ctrl:h,key:h.instanceId}));return fe.createElement(fe.Fragment,null,a&&fe.createElement("div",{ref:R,className:"ag-pinned-left-header "+y,"aria-hidden":!t,role:"rowgroup"},m()),l&&fe.createElement("div",{ref:R,className:"ag-pinned-right-header "+y,"aria-hidden":!t,role:"rowgroup"},m()),f&&fe.createElement("div",{ref:R,className:"ag-header-viewport "+y,role:"presentation"},fe.createElement("div",{ref:o,className:"ag-header-container",role:"rowgroup"},m())))},Ct=hr(Yr),Zr=()=>{const[e,t]=Ot(()=>new te),[s,n]=Ot(),{context:r}=pr(q),d=It(null),c=It(),o=dr(l=>{if(d.current=l,c.current=l?r.createBean(new lr):r.destroyBean(c.current),!l)return;const f={addOrRemoveCssClass:(R,y)=>t(m=>m.setClass(R,y)),setHeightAndMinHeight:R=>n(R)};c.current.setComp(f,l,l)},[]),u=At(()=>"ag-header "+e.toString(),[e]),a=At(()=>({height:s,minHeight:s}),[s]);return Xe.createElement("div",{ref:o,className:u,style:a,role:"presentation"},Xe.createElement(Ct,{pinned:"left"}),Xe.createElement(Ct,{pinned:null}),Xe.createElement(Ct,{pinned:"right"}))},Xr=ur(Zr);import{useEffect as en}from"react";var tn=(e,t)=>{en(()=>{const s=t.current;if(s){const n=s.parentElement;if(n){const r=document.createComment(e);return n.insertBefore(r,s),()=>{n.removeChild(r)}}}},[e])},ve=tn;import{RowContainerCtrl as sn,_getRowContainerOptions as rn}from"ag-grid-community";import Ue,{memo as nn,useCallback as Le,useContext as on,useMemo as ft,useRef as Be,useState as an}from"react";import{CssClassManager as cn,_EmptyBean as ln}from"ag-grid-community";import xe,{memo as un,useCallback as dn,useContext as pn,useEffect as Ht,useLayoutEffect as mn,useMemo as _t,useRef as de,useState as pe}from"react";import{CssClassManager as hn,_EmptyBean as Cn,_removeFromParent as fn}from"ag-grid-community";import H,{memo as gn,useCallback as Ve,useContext as yn,useLayoutEffect as gt,useMemo as yt,useRef as ie,useState as me}from"react";import{AgPromise as vn}from"ag-grid-community";var Rn=class{constructor(e,t){this.cellEditorParams=e,this.refreshProps=t,this.instanceCreated=new vn(s=>{this.resolveInstanceCreated=s}),this.onValueChange=s=>this.updateValue(s),this.value=e.value}getProps(){return{...this.cellEditorParams,initialValue:this.cellEditorParams.value,value:this.value,onValueChange:this.onValueChange}}getValue(){return this.value}refresh(e){this.cellEditorParams=e,this.refreshProps()}setMethods(e){dt(this.getOptionalMethods(),e,this)}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}setRef(e){this.componentInstance=e,this.resolveInstanceCreated?.(),this.resolveInstanceCreated=void 0}getOptionalMethods(){return["isCancelBeforeStart","isCancelAfterEnd","focusIn","focusOut","afterGuiAttached"]}updateValue(e){this.value=e,this.refreshProps()}};import Ut,{memo as wn,useContext as En,useState as Pn}from"react";import{createPortal as bn}from"react-dom";import{useEffect as Sn,useRef as rt,useState as kn}from"react";var Fn=e=>{const t=rt(e),s=rt(),n=rt(!1),r=rt(!1),[,d]=kn(0);n.current&&(r.current=!0),Sn(()=>(n.current||(s.current=t.current(),n.current=!0),d(c=>c+1),()=>{r.current&&s.current?.()}),[])},Mn=e=>{const[t,s]=Pn(),{context:n,popupService:r,localeService:d,gos:c,editService:o}=En(q);return Fn(()=>{const{editDetails:u,cellCtrl:a,eParentCell:l}=e,{compDetails:f}=u,R=c.get("stopEditingWhenCellsLoseFocus"),y=n.createBean(o.createPopupEditorWrapper(f.params)),m=y.getGui();if(e.jsChildComp){const E=e.jsChildComp.getGui();E&&m.appendChild(E)}const h={column:a.getColumn(),rowNode:a.getRowNode(),type:"popupCellEditor",eventSource:l,ePopup:m,position:u.popupPosition,keepWithinBounds:!0},i=r.positionPopupByComponent.bind(r,h),C=d.getLocaleTextFunc(),w=r.addPopup({modal:R,eChild:m,closeOnEsc:!0,closedCallback:()=>{a.onPopupEditorClosed()},anchorToElement:l,positionCallback:i,ariaLabel:C("ariaLabelCellEditor","Cell Editor")}),v=w?w.hideFunc:void 0;return s(y),e.jsChildComp?.afterGuiAttached?.(),()=>{v?.(),n.destroyBean(y)}}),Ut.createElement(Ut.Fragment,null,t&&e.wrappedContent&&bn(e.wrappedContent,t.getGui()))},Lt=wn(Mn);import{useCallback as Bn,useContext as xn,useEffect as Vt}from"react";var Gn=(e,t,s,n,r,d)=>{const{context:c}=xn(q),o=Bn(()=>{const u=r.current;if(!u)return;const a=u.getGui();a&&a.parentElement&&a.parentElement.removeChild(a),c.destroyBean(u),r.current=void 0},[]);Vt(()=>{const u=e!=null,a=e?.compDetails&&!e.compDetails.componentFromFramework,l=t&&s==null;if(!(u&&a&&!l)){o();return}const R=e.compDetails;if(r.current){const m=r.current,i=m.refresh!=null&&e.force==!1?m.refresh(R.params):!1;if(i===!0||i===void 0)return;o()}const y=R.newAgStackInstance();y?.then(m=>{if(!m)return;const h=m.getGui();if(!h)return;(t?s:d.current).appendChild(h),r.current=m})},[e,t,n]),Vt(()=>o,[])},An=Gn,In=(e,t,s)=>{const{compProxy:n}=e;s(n);const r=n.getProps(),d=be(t);return H.createElement(Oe.Provider,{value:{setMethods:c=>n.setMethods(c)}},d?H.createElement(t,{...r}):H.createElement(t,{...r,ref:c=>n.setRef(c)}))},jt=(e,t,s)=>{const n=e.compProxy;return H.createElement(H.Fragment,null,n?In(e,t,s):H.createElement(t,{...e.compDetails.params,ref:s}))},On=(e,t,s,n,r)=>{const d=e.compDetails,c=d.componentClass,o=d.componentFromFramework&&!e.popup,u=d.componentFromFramework&&e.popup,a=!d.componentFromFramework&&e.popup;return H.createElement(H.Fragment,null,o&&jt(e,c,t),u&&H.createElement(Lt,{editDetails:e,cellCtrl:n,eParentCell:s,wrappedContent:jt(e,c,t)}),a&&r&&H.createElement(Lt,{editDetails:e,cellCtrl:n,eParentCell:s,jsChildComp:r}))},Dn=(e,t,s,n,r,d,c)=>{const{compDetails:o,value:u}=e,a=!o,l=o&&o.componentFromFramework,f=o&&o.componentClass,R=u?.toString?u.toString():u,y=()=>H.createElement(H.Fragment,null,a&&H.createElement(H.Fragment,null,R),l&&!d&&H.createElement(f,{...o.params,key:t,ref:n}),l&&d&&H.createElement(f,{...o.params,key:t}));return H.createElement(H.Fragment,null,r?H.createElement("span",{role:"presentation",id:`cell-${s}`,className:"ag-cell-value",ref:c},y()):y())},Wn=({cellCtrl:e,printLayout:t,editingRow:s})=>{const{context:n}=yn(q),{colIdSanitised:r,instanceId:d}=e,c=ie(),[o,u]=me(()=>e.isCellRenderer()?void 0:{compDetails:void 0,value:e.getValueToDisplay(),force:!1}),[a,l]=me(),[f,R]=me(1),[y,m]=me(),[h,i]=me(!1),[C,w]=me(!1),[v,E]=me(!1),[P,O]=me(),b=yt(()=>e.isForceWrapper(),[e]),k=yt(()=>e.getCellAriaRole(),[e]),M=ie(null),p=ie(null),g=ie(),F=ie(),J=ie(),I=ie([]),_=ie(),[B,x]=me(0),G=Ve(N=>{_.current=N,x($=>$+1)},[]),U=o!=null&&(h||v||C),A=b||U,T=Ve(N=>{if(F.current=N,N){const $=N.isCancelBeforeStart&&N.isCancelBeforeStart();setTimeout(()=>{$?(e.stopEditing(!0),e.focusCell(!0)):e.cellEditorAttached()})}},[e]),W=ie();W.current||(W.current=new hn(()=>M.current)),An(o,A,_.current,B,g,M);const X=ie();gt(()=>{const N=X.current,$=o;if(X.current=o,N==null||N.compDetails==null||$==null||$.compDetails==null)return;const K=N.compDetails,D=$.compDetails;if(K.componentClass!=D.componentClass||p.current?.refresh==null)return;p.current.refresh(D.params)!=!0&&R(Y=>Y+1)},[o]),gt(()=>{if(!(a&&!a.compDetails.componentFromFramework))return;const $=a.compDetails,K=a.popup===!0,D=$.newAgStackInstance();return D.then(z=>{if(!z)return;const Y=z.getGui();T(z),K||((b?J:M).current?.appendChild(Y),z.afterGuiAttached&&z.afterGuiAttached()),O(z)}),()=>{D.then(z=>{const Y=z.getGui();n.destroyBean(z),T(void 0),O(void 0),Y?.parentElement?.removeChild(Y)})}},[a]);const S=Ve(N=>{if(J.current=N,!N){I.current.forEach(K=>K()),I.current=[];return}const $=K=>{if(K){const D=K.getGui();N.insertAdjacentElement("afterbegin",D),I.current.push(()=>{n.destroyBean(K),fn(D)})}return K};if(h){const K=e.createSelectionCheckbox();$(K)}v&&$(e.createDndSource()),C&&$(e.createRowDragComp())},[e,n,v,C,h]),j=Ve(N=>{if(M.current=N,c.current=N?n.createBean(new Cn):n.destroyBean(c.current),!N||!e)return;const $={addOrRemoveCssClass:(D,z)=>W.current.addOrRemoveCssClass(D,z),setUserStyles:D=>m(D),getFocusableElement:()=>M.current,setIncludeSelection:D=>i(D),setIncludeRowDrag:D=>w(D),setIncludeDndSource:D=>E(D),getCellEditor:()=>F.current||null,getCellRenderer:()=>p.current??g.current,getParentOfValue:()=>_.current??J.current??M.current,setRenderDetails:(D,z,Y)=>{u(le=>le?.compDetails!==D||le?.value!==z||le?.force!==Y?{value:z,compDetails:D,force:Y}:le)},setEditDetails:(D,z,Y,le)=>{if(D){let we;le?we=new Rn(D.params,()=>R(nt=>nt+1)):D.componentFromFramework&&Ye(),l({compDetails:D,popup:z,popupPosition:Y,compProxy:we}),z||u(void 0)}else l(we=>{we?.compProxy&&(F.current=void 0)})}},K=J.current||void 0;e.setComp($,N,K,t,s,c.current)},[]),L=yt(()=>!!(o?.compDetails?.componentFromFramework&&be(o.compDetails.componentClass)),[o]);gt(()=>{M.current&&(W.current.addOrRemoveCssClass("ag-cell-value",!A),W.current.addOrRemoveCssClass("ag-cell-inline-editing",!!a&&!a.popup),W.current.addOrRemoveCssClass("ag-cell-popup-editing",!!a&&!!a.popup),W.current.addOrRemoveCssClass("ag-cell-not-inline-editing",!a||!!a.popup),e.getRowCtrl()?.setInlineEditingCss(),e.shouldRestoreFocus()&&!e.isEditing()&&M.current.focus({preventScroll:!0}))});const ne=()=>H.createElement(H.Fragment,null,o!=null&&Dn(o,f,d,p,A,L,G),a!=null&&On(a,T,M.current,e,P)),Re=Ve(()=>e.onFocusOut(),[]);return H.createElement("div",{ref:j,style:y,role:k,"col-id":r,onBlur:Re},A?H.createElement("div",{className:"ag-cell-wrapper",role:"presentation",ref:S},ne()):ne())},Tn=gn(Wn),Nn=({rowCtrl:e,containerType:t})=>{const{context:s,gos:n}=pn(q),r=de(),d=de(e.getDomOrder()),c=e.isFullWidth(),o=e.getRowNode().displayed,[u,a]=pe(()=>o?e.getRowIndex():null),[l,f]=pe(()=>e.getRowId()),[R,y]=pe(()=>e.getBusinessKey()),[m,h]=pe(()=>e.getRowStyles()),i=de(null),C=de(null),[w,v]=pe(()=>null),[E,P]=pe(),[O,b]=pe(()=>o?e.getInitialRowTop(t):void 0),[k,M]=pe(()=>o?e.getInitialTransform(t):void 0),p=de(null),g=de(),F=de(!1),[J,I]=pe(0);Ht(()=>{if(F.current||!E||J>10)return;const S=p.current?.firstChild;S?(e.setupDetailRowAutoHeight(S),F.current=!0):I(j=>j+1)},[E,J]);const _=de();_.current||(_.current=new cn(()=>p.current));const B=dn(S=>{if(p.current=S,r.current=S?s.createBean(new ln):s.destroyBean(r.current),!S){e.unsetComp(t);return}if(!e.isAlive())return;const j={setTop:b,setTransform:M,addOrRemoveCssClass:(L,ne)=>_.current.addOrRemoveCssClass(L,ne),setDomOrder:L=>d.current=L,setRowIndex:a,setRowId:f,setRowBusinessKey:y,setUserStyles:h,setCellCtrls:(L,ne)=>{C.current=i.current,i.current=L;const Re=it(C.current,L,d.current);Re!==C.current&&ct(ne,()=>v(Re))},showFullWidth:L=>P(L),getFullWidthCellRenderer:()=>g.current,refreshFullWidth:L=>T.current?(P(ne=>({...ne,params:L()})),!0):!g.current||!g.current.refresh?!1:g.current.refresh(L())};e.setComp(j,S,t,r.current)},[]);mn(()=>Ie(E,s,p.current,g),[E]);const x=_t(()=>{const S={top:O,transform:k};return Object.assign(S,m),S},[O,k,m]),G=c&&E?.componentFromFramework,U=!c&&w!=null,A=_t(()=>!!(E?.componentFromFramework&&be(E.componentClass)),[E]),T=de(!1);Ht(()=>{T.current=A&&!!E&&!!n.get("reactiveCustomComponents")},[A,E]);const W=()=>w?.map(S=>xe.createElement(Tn,{cellCtrl:S,editingRow:e.isEditing(),printLayout:e.isPrintLayout(),key:S.instanceId})),X=()=>{const S=E.componentClass;return xe.createElement(xe.Fragment,null,A?xe.createElement(S,{...E.params}):xe.createElement(S,{...E.params,ref:g}))};return xe.createElement("div",{ref:B,role:"row",style:x,"row-index":u,"row-id":l,"row-business-key":R},U&&W(),G&&X())},Hn=un(Nn),_n=({name:e})=>{const{context:t}=on(q),s=ft(()=>rn(e),[e]),n=Be(null),r=Be(null),d=Be([]),c=Be([]),[o,u]=an(()=>[]),a=Be(!1),l=Be(),f=ft(()=>se(s.viewport),[s]),R=ft(()=>se(s.container),[s]),y=s.type==="center",m=y?n:r;ve(" AG Row Container "+e+" ",m);const h=Le(()=>y?n.current!=null&&r.current!=null:r.current!=null,[]),i=Le(()=>y?n.current==null&&r.current==null:r.current==null,[]),C=Le(()=>{if(i()&&(l.current=t.destroyBean(l.current)),h()){const P=b=>{const k=it(c.current,d.current,a.current);k!==c.current&&(c.current=k,ct(b,()=>u(k)))},O={setHorizontalScroll:b=>{n.current&&(n.current.scrollLeft=b)},setViewportHeight:b=>{n.current&&(n.current.style.height=b)},setRowCtrls:({rowCtrls:b,useFlushSync:k})=>{const M=!!k&&d.current.length>0&&b.length>0;d.current=b,P(M)},setDomOrder:b=>{a.current!=b&&(a.current=b,P(!1))},setContainerWidth:b=>{r.current&&(r.current.style.width=b)},setOffsetTop:b=>{r.current&&(r.current.style.transform=`translateY(${b})`)}};l.current=t.createBean(new sn(e)),l.current.setComp(O,r.current,n.current)}},[h,i]),w=Le(P=>{r.current=P,C()},[C]),v=Le(P=>{n.current=P,C()},[C]),E=()=>Ue.createElement("div",{className:R,ref:w,role:"rowgroup"},o.map(P=>Ue.createElement(Hn,{rowCtrl:P,containerType:s.type,key:P.instanceId})));return Ue.createElement(Ue.Fragment,null,y?Ue.createElement("div",{className:f,ref:v,role:"presentation"},E()):E())},Un=nn(_n),Ln=()=>{const{context:e,resizeObserverService:t}=ir(q),[s,n]=Q(""),[r,d]=Q(0),[c,o]=Q(0),[u,a]=Q("0px"),[l,f]=Q("0px"),[R,y]=Q("100%"),[m,h]=Q("0px"),[i,C]=Q("0px"),[w,v]=Q("100%"),[E,P]=Q(""),[O,b]=Q(""),[k,M]=Q(null),[p,g]=Q(""),[F,J]=Q(null),[I,_]=Q("ag-layout-normal"),B=ce();B.current||(B.current=new Xs(()=>x.current));const x=ce(null),G=ce(null),U=ce(null),A=ce(null),T=ce(null),W=ce(null),X=ce(null),S=ce([]),j=ce([]);ve(" AG Grid Body ",x),ve(" AG Pinned Top ",G),ve(" AG Sticky Top ",U),ve(" AG Middle ",W),ve(" AG Pinned Bottom ",X);const L=cr(oe=>{if(x.current=oe,!oe){S.current=e.destroyBeans(S.current),j.current.forEach(V=>V()),j.current=[];return}if(!e)return;const ze=(V,Z)=>{V.appendChild(Z),j.current.push(()=>V.removeChild(Z))},ot=V=>{const Z=e.createBean(new V);return S.current.push(Z),Z},Ae=(V,Z,qt)=>{ze(V,document.createComment(qt)),ze(V,ot(Z).getGui())};Ae(oe,er," AG Fake Horizontal Scroll "),Ae(oe,rr," AG Overlay Wrapper "),T.current&&Ae(T.current,tr," AG Fake Vertical Scroll ");const Kt={setRowAnimationCssOnBodyViewport:n,setColumnCount:V=>{x.current&&nr(x.current,V)},setRowCount:V=>{x.current&&or(x.current,V)},setTopHeight:d,setBottomHeight:o,setStickyTopHeight:a,setStickyTopTop:f,setStickyTopWidth:y,setTopDisplay:P,setBottomDisplay:b,setColumnMovingCss:(V,Z)=>B.current.addOrRemoveCssClass(V,Z),updateLayoutClasses:_,setAlwaysVerticalScrollClass:M,setPinnedTopBottomOverflowY:g,setCellSelectableCss:(V,Z)=>J(Z?V:null),setBodyViewportWidth:V=>{W.current&&(W.current.style.width=V)},registerBodyViewportResizeListener:V=>{if(W.current){const Z=t.observeResize(W.current,V);j.current.push(()=>Z())}},setStickyBottomHeight:h,setStickyBottomBottom:C,setStickyBottomWidth:v},Pt=e.createBean(new sr);S.current.push(Pt),Pt.setComp(Kt,oe,W.current,G.current,X.current,U.current,A.current)},[]),ne=re(()=>se("ag-root","ag-unselectable",I),[I]),Re=re(()=>se("ag-body-viewport",s,I,k,F),[s,I,k,F]),N=re(()=>se("ag-body",I),[I]),$=re(()=>se("ag-floating-top",F),[F]),K=re(()=>se("ag-sticky-top",F),[F]),D=re(()=>se("ag-sticky-bottom",m==="0px"?"ag-hidden":null,F),[F,m]),z=re(()=>se("ag-floating-bottom",F),[F]),Y=re(()=>({height:r,minHeight:r,display:E,overflowY:p}),[r,E,p]),le=re(()=>({height:u,top:l,width:R}),[u,l,R]),we=re(()=>({height:m,bottom:i,width:w}),[m,i,w]),nt=re(()=>({height:c,minHeight:c,display:O,overflowY:p}),[c,O,p]),Jt=oe=>We.createElement(Un,{name:oe,key:`${oe}-container`}),Ge=({section:oe,children:ze,className:ot,style:Ae})=>We.createElement("div",{ref:oe,className:ot,role:"presentation",style:Ae},ze.map(Jt));return We.createElement("div",{ref:L,className:ne,role:"treegrid"},We.createElement(Xr,null),Ge({section:G,className:$,style:Y,children:["topLeft","topCenter","topRight","topFullWidth"]}),We.createElement("div",{className:N,ref:T,role:"presentation"},Ge({section:W,className:Re,children:["left","center","right","fullWidth"]})),Ge({section:U,className:K,style:le,children:["stickyTopLeft","stickyTopCenter","stickyTopRight","stickyTopFullWidth"]}),Ge({section:A,className:D,style:we,children:["stickyBottomLeft","stickyBottomCenter","stickyBottomRight","stickyBottomFullWidth"]}),Ge({section:X,className:z,style:nt,children:["bottomLeft","bottomCenter","bottomRight","bottomFullWidth"]}))},Vn=ar(Ln);import{TabGuardClassNames as vt,TabGuardCtrl as jn}from"ag-grid-community";import Rt,{forwardRef as zn,memo as $n,useCallback as wt,useContext as Jn,useImperativeHandle as Kn,useRef as Et}from"react";var qn=(e,t)=>{const{children:s,eFocusableElement:n,onTabKeyDown:r,gridCtrl:d,forceFocusOutWhenTabGuardsAreEmpty:c}=e,{context:o}=Jn(q),u=Et(null),a=Et(null),l=Et(),f=i=>{const C=i==null?void 0:parseInt(i,10).toString();[u,a].forEach(w=>{C===void 0?w.current?.removeAttribute("tabindex"):w.current?.setAttribute("tabindex",C)})};Kn(t,()=>({forceFocusOutOfContainer(i){l.current?.forceFocusOutOfContainer(i)}}));const R=wt(()=>{const i=u.current,C=a.current;if(!i&&!C){l.current=o.destroyBean(l.current);return}if(i&&C){const w={setTabIndex:f};l.current=o.createBean(new jn({comp:w,eTopGuard:i,eBottomGuard:C,eFocusableElement:n,onTabKeyDown:r,forceFocusOutWhenTabGuardsAreEmpty:c,focusInnerElement:v=>d.focusInnerElement(v)}))}},[]),y=wt(i=>{u.current=i,R()},[R]),m=wt(i=>{a.current=i,R()},[R]),h=i=>{const C=i==="top"?vt.TAB_GUARD_TOP:vt.TAB_GUARD_BOTTOM;return Rt.createElement("div",{className:`${vt.TAB_GUARD} ${C}`,role:"presentation",ref:i==="top"?y:m})};return Rt.createElement(Rt.Fragment,null,h("top"),s,h("bottom"))},Qn=zn(qn),Yn=$n(Qn),Zn=({context:e})=>{const[t,s]=he(""),[n,r]=he(""),[d,c]=he(""),[o,u]=he(null),[a,l]=he(null),[f,R]=he(!1),[y,m]=he(),h=Se(),i=Se(null),C=Se(),[w,v]=he(null),E=Se(()=>{}),P=Se(),O=Se([]),b=pt(()=>{},[]),k=Ze(()=>e.isDestroyed()?null:e.getBeans(),[e]);ve(" AG Grid ",i);const M=pt(I=>{if(i.current=I,h.current=I?e.createBean(new Qs):e.destroyBean(h.current),!I||e.isDestroyed())return;const _=h.current;E.current=_.focusInnerElement.bind(_);const B={destroyGridUi:()=>{},setRtlClass:s,setGridThemeClass:r,forceFocusOutOfContainer:x=>{if(!x&&P.current?.isDisplayed()){P.current.forceFocusOutOfContainer(x);return}C.current?.forceFocusOutOfContainer(x)},updateLayoutClasses:c,getFocusableContainers:()=>{const x=[],G=i.current?.querySelector(".ag-root");return G&&x.push({getGui:()=>G}),O.current.forEach(U=>{U.isDisplayed()&&x.push(U)}),x},setCursor:u,setUserSelect:l};_.setComp(B,I,I),R(!0)},[]);Zs(()=>{const I=h.current,_=i.current;if(!y||!k||!I||!w||!_)return;const B=[],{watermarkSelector:x,paginationSelector:G,sideBarSelector:U,statusBarSelector:A,gridHeaderDropZonesSelector:T}=I.getOptionalSelectors(),W=[];if(T){const S=e.createBean(new T.component),j=S.getGui();_.insertAdjacentElement("afterbegin",j),W.push(j),B.push(S)}if(U){const S=e.createBean(new U.component),j=S.getGui(),L=w.querySelector(".ag-tab-guard-bottom");L&&(L.insertAdjacentElement("beforebegin",j),W.push(j)),B.push(S),O.current.push(S)}const X=S=>{const j=e.createBean(new S),L=j.getGui();return _.insertAdjacentElement("beforeend",L),W.push(L),B.push(j),j};if(A&&X(A.component),G){const S=X(G.component);P.current=S,O.current.push(S)}return x&&X(x.component),()=>{e.destroyBeans(B),W.forEach(S=>{S.parentElement?.removeChild(S)})}},[y,w,k]);const p=Ze(()=>se("ag-root-wrapper",t,n,d),[t,n,d]),g=Ze(()=>se("ag-root-wrapper-body","ag-focus-managed",d),[d]),F=Ze(()=>({userSelect:a??"",WebkitUserSelect:a??"",cursor:o??""}),[a,o]),J=pt(I=>{C.current=I,m(I!==null)},[]);return De.createElement("div",{ref:M,className:p,style:F,role:"presentation"},De.createElement("div",{className:g,ref:v,role:"presentation"},f&&w&&k&&De.createElement(q.Provider,{value:k},De.createElement(Yn,{ref:J,eFocusableElement:w,onTabKeyDown:b,gridCtrl:h.current,forceFocusOutWhenTabGuardsAreEmpty:!0},De.createElement(Vn,null)))))},Xn=Ys(Zn);import{BeanStub as eo}from"ag-grid-community";var to=class extends eo{wireBeans(e){this.ctrlsService=e.ctrlsService}areHeaderCellsRendered(){return this.ctrlsService.getHeaderRowContainerCtrls().every(e=>e.getAllCtrls().every(t=>t.areCellsRendered()))}},zt=e=>{const t=ee(),s=ee(null),n=ee(null),r=ee([]),d=ee([]),c=ee(e),o=ee(),u=ee(),a=ee(!1),[l,f]=Ee(void 0),[,R]=Ee(0),y=Je(i=>{if(s.current=i,!i){r.current.forEach(p=>p()),r.current.length=0;return}const C=e.modules||[];n.current||(n.current=new qs(()=>R(p=>p+1),e.componentWrappingElement,e.maxComponentCreationTimeMs),r.current.push(()=>{n.current?.destroy(),n.current=null}));const w=ss(e.gridOptions,e),v=()=>{if(a.current){const p=()=>o.current?.shouldQueueUpdates()?void 0:d.current.shift();let g=p();for(;g;)g(),g=p()}},E=new oo(v);o.current=E;const P=new to,O={providedBeanInstances:{frameworkComponentWrapper:new ro(n.current,w.reactiveCustomComponents??rs("reactiveCustomComponents")??!0),renderStatusService:P},modules:C,frameworkOverrides:E},b=p=>{f(p),p.createBean(P),r.current.push(()=>{p.destroy()}),p.getBean("ctrlsService").whenReady({addDestroyFunc:g=>{r.current.push(g)}},()=>{if(p.isDestroyed())return;const g=t.current;g&&e.setGridApi?.(g)})},k=p=>{p.getBean("ctrlsService").whenReady({addDestroyFunc:g=>{r.current.push(g)}},()=>{d.current.forEach(g=>g()),d.current.length=0,a.current=!0})},M=new Xt;w.gridId??(w.gridId=u.current),t.current=M.create(i,w,b,k,O),r.current.push(()=>{t.current=void 0}),t.current&&(u.current=t.current.getGridId())},[]),m=Ke(()=>({height:"100%",...e.containerStyle||{}}),[e.containerStyle]),h=Je(i=>{a.current&&!o.current?.shouldQueueUpdates()?i():d.current.push(i)},[]);return us(()=>{const i=so(c.current,e);c.current=e,h(()=>{t.current&&as(i,t.current)})},[e]),$e.createElement("div",{style:m,className:e.className,ref:y},l&&!l.isDestroyed()?$e.createElement(Xn,{context:l}):null,n.current?.getPortals()??null)};function so(e,t){const s={};return Object.keys(t).forEach(n=>{const r=t[n];e[n]!==r&&(s[n]=r)}),s}var ro=class extends Zt{constructor(e,t){super(),this.parent=e,this.reactiveCustomComponents=t}createWrapper(e,t){if(this.reactiveCustomComponents){const r=(d=>{switch(d){case"filter":return Ws;case"floatingFilterComponent":return Hs;case"dateComponent":return Os;case"dragAndDropImageComponent":return Ds;case"loadingOverlayComponent":return _s;case"noRowsOverlayComponent":return Ls;case"statusPanel":return Vs;case"toolPanel":return js;case"menuItem":return Us;case"cellRenderer":return Is}})(t.propertyName);if(r)return new r(e,this.parent,t)}else switch(t.propertyName){case"filter":case"floatingFilterComponent":case"dateComponent":case"dragAndDropImageComponent":case"loadingOverlayComponent":case"noRowsOverlayComponent":case"statusPanel":case"toolPanel":case"menuItem":case"cellRenderer":Ye();break}const s=!t.cellRenderer&&t.propertyName!=="toolPanel";return new xt(e,this.parent,t,s)}},no=is((e,t)=>{const{ctrlsFactory:s,context:n,gos:r,resizeObserverService:d,rowModel:c}=ls(q),[o,u]=Ee(()=>new te),[a,l]=Ee(()=>new te),[f,R]=Ee(),[y,m]=Ee(),h=ee(),i=ee(null),C=ee(),w=Ke(()=>es.__getGridRegisteredModules(e.api.getGridId()),[e]),v=Ke(()=>o.toString()+" ag-details-row",[o]),E=Ke(()=>a.toString()+" ag-details-grid",[a]);t&&ds(t,()=>({refresh(){return h.current?.refresh()??!1}})),e.template&&cs("detailCellRendererParams.template is not supported by AG Grid React. To change the template, provide a Custom Detail Cell Renderer. See https://ag-grid.com/react-data-grid/master-detail-custom-detail/");const P=Je(b=>{if(i.current=b,!b){h.current=n.destroyBean(h.current),C.current?.();return}const k={addOrRemoveCssClass:(p,g)=>u(F=>F.setClass(p,g)),addOrRemoveDetailGridCssClass:(p,g)=>l(F=>F.setClass(p,g)),setDetailGrid:p=>R(p),setRowData:p=>m(p),getGui:()=>i.current},M=s.getInstance("detailCellRenderer");if(M&&(n.createBean(M),M.init(k,e),h.current=M,r.get("detailRowAutoHeight"))){const p=()=>{if(i.current==null)return;const g=i.current.clientHeight;g!=null&&g>0&&setTimeout(()=>{e.node.setRowHeight(g),(ns(r)||os(r))&&c.onRowHeightChanged()},0)};C.current=d.observeResize(b,p),p()}},[]),O=Je(b=>{h.current?.registerDetailWithMaster(b)},[]);return $e.createElement("div",{className:v,ref:P},f&&$e.createElement(zt,{className:E,...f,modules:w,rowData:y,setGridApi:O}))}),oo=class extends ts{constructor(e){super("react"),this.processQueuedUpdates=e,this.queueUpdates=!1,this.frameworkComponents={agGroupCellRenderer:kt,agGroupRowRenderer:kt,agDetailCellRenderer:no},this.wrapIncoming=(t,s)=>s==="ensureVisible"?Ps(t):t(),this.renderingEngine="react"}frameworkComponent(e){return this.frameworkComponents[e]}isFrameworkComponent(e){if(!e)return!1;const t=e.prototype;return!(t&&"getGui"in t)}getLockOnRefresh(){this.queueUpdates=!0}releaseLockOnRefresh(){this.queueUpdates=!1,this.processQueuedUpdates()}shouldQueueUpdates(){return this.queueUpdates}runWhenReadyAsync(){return Es()}},ao=class extends Yt{constructor(){super(...arguments),this.apiListeners=[],this.setGridApi=e=>{this.api=e,this.apiListeners.forEach(t=>t(e))}}registerApiListener(e){this.apiListeners.push(e)}componentWillUnmount(){this.apiListeners.length=0}render(){return Qt.createElement(zt,{...this.props,setGridApi:this.setGridApi})}};import{useContext as co}from"react";function je(e){const{setMethods:t}=co(Oe);t(e)}function io(e){je(e)}function lo(e){return je(e)}function uo(e){return je(e)}function po(e){je(e)}function mo(e){je(e)}export{ao as AgGridReact,Oe as CustomComponentContext,Js as getInstance,io as useGridCellEditor,lo as useGridDate,uo as useGridFilter,po as useGridFloatingFilter,mo as useGridMenuItem,Ye as warnReactiveCustomComponents};
