"use strict";var __create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__export=(e,t)=>{for(var r in t)__defProp(e,r,{get:t[r],enumerable:!0})},__copyProps=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of __getOwnPropNames(t))!__hasOwnProp.call(e,s)&&s!==r&&__defProp(e,s,{get:()=>t[s],enumerable:!(n=__getOwnPropDesc(t,s))||n.enumerable});return e},__toESM=(e,t,r)=>(r=e!=null?__create(__getProtoOf(e)):{},__copyProps(t||!e||!e.__esModule?__defProp(r,"default",{value:e,enumerable:!0}):r,e)),__toCommonJS=e=>__copyProps(__defProp({},"__esModule",{value:!0}),e),src_exports={};__export(src_exports,{AgGridReact:()=>AgGridReact,CustomComponentContext:()=>CustomContext,getInstance:()=>getInstance,useGridCellEditor:()=>useGridCellEditor,useGridDate:()=>useGridDate,useGridFilter:()=>useGridFilter,useGridFloatingFilter:()=>useGridFloatingFilter,useGridMenuItem:()=>useGridMenuItem,warnReactiveCustomComponents:()=>warnReactiveCustomComponents}),module.exports=__toCommonJS(src_exports);var import_react24=__toESM(require("react")),import_ag_grid_community20=require("ag-grid-community"),import_react23=__toESM(require("react")),import_ag_grid_community=require("ag-grid-community"),import_react3=__toESM(require("react")),import_react=__toESM(require("react")),BeansContext=import_react.default.createContext({}),showJsComp=(e,t,r,n)=>{if(!e||e.componentFromFramework||t.isDestroyed())return;const d=e.newAgStackInstance();if(d==null)return;let c,o,l=!1;return d.then(a=>{if(l){t.destroyBean(a);return}c=a,o=c.getGui(),r.appendChild(o),setRef(n,c)}),()=>{l=!0,c&&(o?.parentElement?.removeChild(o),t.destroyBean(c),n&&setRef(n,void 0))}},setRef=(e,t)=>{if(e)if(e instanceof Function)e(t);else{const r=e;r.current=t}},import_react2=__toESM(require("react")),import_react_dom=__toESM(require("react-dom")),classesList=(...e)=>e.filter(r=>r!=null&&r!=="").join(" "),CssClasses=class ie{constructor(...t){this.classesMap={},t.forEach(r=>{this.classesMap[r]=!0})}setClass(t,r){if(!!this.classesMap[t]==r)return this;const s=new ie;return s.classesMap={...this.classesMap},s.classesMap[t]=r,s}toString(){return Object.keys(this.classesMap).filter(r=>this.classesMap[r]).join(" ")}},isComponentStateless=e=>{const t=()=>typeof Symbol=="function"&&Symbol.for,r=()=>t()?Symbol.for("react.memo"):60115;return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===r()},reactVersion=import_react2.default.version?.split(".")[0],isReactVersion17Minus=reactVersion==="16"||reactVersion==="17";function isReact19(){return reactVersion==="19"}var disableFlushSync=!1;function runWithoutFlushSync(e){return disableFlushSync||setTimeout(()=>disableFlushSync=!1,0),disableFlushSync=!0,e()}var agFlushSync=(e,t)=>{!isReactVersion17Minus&&e&&!disableFlushSync?import_react_dom.default.flushSync(t):t()};function getNextValueIfDifferent(e,t,r){if(t==null||e==null)return t;if(e===t||t.length===0&&e.length===0)return e;if(r||e.length===0&&t.length>0||e.length>0&&t.length===0)return t;const n=[],s=[],d=new Map,c=new Map;for(let o=0;o<t.length;o++){const l=t[o];c.set(l.instanceId,l)}for(let o=0;o<e.length;o++){const l=e[o];d.set(l.instanceId,l),c.has(l.instanceId)&&n.push(l)}for(let o=0;o<t.length;o++){const l=t[o],a=l.instanceId;d.has(a)||s.push(l)}return n.length===e.length&&s.length===0?e:n.length===0&&s.length===t.length?t:n.length===0?s:s.length===0?n:[...n,...s]}var GroupCellRenderer=(0,import_react3.forwardRef)((e,t)=>{const{ctrlsFactory:r,context:n}=(0,import_react3.useContext)(BeansContext),s=(0,import_react3.useRef)(null),d=(0,import_react3.useRef)(null),c=(0,import_react3.useRef)(null),o=(0,import_react3.useRef)(null),l=(0,import_react3.useRef)(null),a=(0,import_react3.useRef)(),[u,C]=(0,import_react3.useState)(),[R,y]=(0,import_react3.useState)(),[m,h]=(0,import_react3.useState)(),[i,f]=(0,import_react3.useState)(()=>new CssClasses),[w,v]=(0,import_react3.useState)(()=>new CssClasses("ag-hidden")),[S,b]=(0,import_react3.useState)(()=>new CssClasses("ag-hidden")),[x,E]=(0,import_react3.useState)(()=>new CssClasses("ag-invisible"));(0,import_react3.useImperativeHandle)(t,()=>({refresh(){return!1}})),(0,import_react3.useLayoutEffect)(()=>showJsComp(u,n,d.current),[u]);const M=(0,import_react3.useCallback)(G=>{if(s.current=G,!G){a.current=n.destroyBean(a.current);return}const B={setInnerRenderer:(A,W)=>{C(A),h(W)},setChildCount:A=>y(A),addOrRemoveCssClass:(A,W)=>f(D=>D.setClass(A,W)),setContractedDisplayed:A=>b(W=>W.setClass("ag-hidden",!A)),setExpandedDisplayed:A=>v(W=>W.setClass("ag-hidden",!A)),setCheckboxVisible:A=>E(W=>W.setClass("ag-invisible",!A))},H=r.getInstance("groupCellRendererCtrl");H&&(a.current=n.createBean(H),a.current.init(B,G,c.current,o.current,l.current,GroupCellRenderer,e))},[]),F=(0,import_react3.useMemo)(()=>`ag-cell-wrapper ${i.toString()}`,[i]),p=(0,import_react3.useMemo)(()=>`ag-group-expanded ${w.toString()}`,[w]),g=(0,import_react3.useMemo)(()=>`ag-group-contracted ${S.toString()}`,[S]),k=(0,import_react3.useMemo)(()=>`ag-group-checkbox ${x.toString()}`,[x]),z=u&&u.componentFromFramework,O=z?u.componentClass:void 0,N=u==null&&m!=null,_=(0,import_ag_grid_community._escapeString)(m,!0);return import_react3.default.createElement("span",{className:F,ref:M,...e.colDef?{}:{role:a.current?.getCellAriaRole()}},import_react3.default.createElement("span",{className:p,ref:o}),import_react3.default.createElement("span",{className:g,ref:l}),import_react3.default.createElement("span",{className:k,ref:c}),import_react3.default.createElement("span",{className:"ag-group-value",ref:d},N&&import_react3.default.createElement(import_react3.default.Fragment,null,_),z&&import_react3.default.createElement(O,{...u.params})),import_react3.default.createElement("span",{className:"ag-group-child-count"},R))}),groupCellRenderer_default=GroupCellRenderer,import_ag_grid_community3=require("ag-grid-community"),import_react5=__toESM(require("react")),import_react4=require("react"),CustomContext=(0,import_react4.createContext)({setMethods:()=>{}}),CustomWrapperComp=e=>{const{initialProps:t,addUpdateCallback:r,CustomComponentClass:n,setMethods:s}=e,[{key:d,...c},o]=(0,import_react5.useState)(t);return(0,import_react5.useEffect)(()=>{r(l=>o(l))},[]),import_react5.default.createElement(CustomContext.Provider,{value:{setMethods:s}},import_react5.default.createElement(n,{key:d,...c}))},customWrapperComp_default=(0,import_react5.memo)(CustomWrapperComp),import_ag_grid_community2=require("ag-grid-community"),import_react6=require("react"),import_react_dom2=require("react-dom"),counter=0;function generateNewKey(){return`agPortalKey_${++counter}`}var ReactComponent=class{constructor(e,t,r,n){this.portal=null,this.oldPortal=null,this.reactComponent=e,this.portalManager=t,this.componentType=r,this.suppressFallbackMethods=!!n,this.statelessComponent=this.isStateless(this.reactComponent),this.key=generateNewKey(),this.portalKey=generateNewKey(),this.instanceCreated=this.isStatelessComponent()?import_ag_grid_community2.AgPromise.resolve(!1):new import_ag_grid_community2.AgPromise(s=>{this.resolveInstanceCreated=s})}getGui(){return this.eParentElement}getRootElement(){return this.eParentElement.firstChild}destroy(){this.componentInstance&&typeof this.componentInstance.destroy=="function"&&this.componentInstance.destroy();const e=this.portal;e&&this.portalManager.destroyPortal(e)}createParentElement(e){const t=this.portalManager.getComponentWrappingElement(),r=document.createElement(t||"div");return r.classList.add("ag-react-container"),e.reactContainer=r,r}addParentContainerStyleAndClasses(){this.componentInstance&&(this.componentInstance.getReactContainerStyle&&this.componentInstance.getReactContainerStyle()&&((0,import_ag_grid_community2._warnOnce)('Since v31.1 "getReactContainerStyle" is deprecated. Apply styling directly to ".ag-react-container" if needed.'),Object.assign(this.eParentElement.style,this.componentInstance.getReactContainerStyle())),this.componentInstance.getReactContainerClasses&&this.componentInstance.getReactContainerClasses()&&((0,import_ag_grid_community2._warnOnce)('Since v31.1 "getReactContainerClasses" is deprecated. Apply styling directly to ".ag-react-container" if needed.'),this.componentInstance.getReactContainerClasses().forEach(t=>this.eParentElement.classList.add(t))))}statelessComponentRendered(){return this.eParentElement.childElementCount>0||this.eParentElement.childNodes.length>0}getFrameworkComponentInstance(){return this.componentInstance}isStatelessComponent(){return this.statelessComponent}getReactComponentName(){return this.reactComponent.name}getMemoType(){return this.hasSymbol()?Symbol.for("react.memo"):60115}hasSymbol(){return typeof Symbol=="function"&&Symbol.for}isStateless(e){return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===this.getMemoType()}hasMethod(e){const t=this.getFrameworkComponentInstance();return!!t&&t[e]!=null||this.fallbackMethodAvailable(e)}callMethod(e,t){const r=this.getFrameworkComponentInstance();if(this.isStatelessComponent())return this.fallbackMethod(e,t&&t[0]?t[0]:{});if(!r){setTimeout(()=>this.callMethod(e,t));return}const n=r[e];if(n)return n.apply(r,t);if(this.fallbackMethodAvailable(e))return this.fallbackMethod(e,t&&t[0]?t[0]:{})}addMethod(e,t){this[e]=t}init(e){return this.eParentElement=this.createParentElement(e),this.createOrUpdatePortal(e),new import_ag_grid_community2.AgPromise(t=>this.createReactComponent(t))}createOrUpdatePortal(e){this.isStatelessComponent()||(this.ref=t=>{this.componentInstance=t,this.addParentContainerStyleAndClasses(),this.resolveInstanceCreated?.(!0),this.resolveInstanceCreated=void 0},e.ref=this.ref),this.reactElement=this.createElement(this.reactComponent,{...e,key:this.key}),this.portal=(0,import_react_dom2.createPortal)(this.reactElement,this.eParentElement,this.portalKey)}createElement(e,t){return(0,import_react6.createElement)(e,t)}createReactComponent(e){this.portalManager.mountReactPortal(this.portal,this,e)}rendered(){return this.isStatelessComponent()&&this.statelessComponentRendered()||!!(!this.isStatelessComponent()&&this.getFrameworkComponentInstance())}refreshComponent(e){this.oldPortal=this.portal,this.createOrUpdatePortal(e),this.portalManager.updateReactPortal(this.oldPortal,this.portal)}fallbackMethod(e,t){const r=this[`${e}Component`];if(!this.suppressFallbackMethods&&r)return r.bind(this)(t)}fallbackMethodAvailable(e){return this.suppressFallbackMethods?!1:!!this[`${e}Component`]}};function addOptionalMethods(e,t,r){e.forEach(n=>{const s=t[n];s&&(r[n]=s)})}var CustomComponentWrapper=class extends ReactComponent{constructor(){super(...arguments),this.awaitUpdateCallback=new import_ag_grid_community3.AgPromise(e=>{this.resolveUpdateCallback=e}),this.wrapperComponent=customWrapperComp_default}init(e){return this.sourceParams=e,super.init(this.getProps())}addMethod(){}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}getFrameworkComponentInstance(){return this}createElement(e,t){return super.createElement(this.wrapperComponent,{initialProps:t,CustomComponentClass:e,setMethods:r=>this.setMethods(r),addUpdateCallback:r=>{this.updateCallback=()=>(r(this.getProps()),new import_ag_grid_community3.AgPromise(n=>{setTimeout(()=>{n()})})),this.resolveUpdateCallback()}})}setMethods(e){this.providedMethods=e,addOptionalMethods(this.getOptionalMethods(),this.providedMethods,this)}getOptionalMethods(){return[]}getProps(){return{...this.sourceParams,key:this.key,ref:this.ref}}refreshProps(){return this.updateCallback?this.updateCallback():new import_ag_grid_community3.AgPromise(e=>this.awaitUpdateCallback.then(()=>{this.updateCallback().then(()=>e())}))}},CellRendererComponentWrapper=class extends CustomComponentWrapper{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},DateComponentWrapper=class extends CustomComponentWrapper{constructor(){super(...arguments),this.date=null,this.onDateChange=e=>this.updateDate(e)}getDate(){return this.date}setDate(e){this.date=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached","setInputPlaceholder","setInputAriaLabel","setDisabled"]}updateDate(e){this.setDate(e),this.sourceParams.onDateChanged()}getProps(){const e=super.getProps();return e.date=this.date,e.onDateChange=this.onDateChange,delete e.onDateChanged,e}},DragAndDropImageComponentWrapper=class extends CustomComponentWrapper{constructor(){super(...arguments),this.label="",this.icon=null,this.shake=!1}setIcon(e,t){this.icon=e,this.shake=t,this.refreshProps()}setLabel(e){this.label=e,this.refreshProps()}getProps(){const e=super.getProps(),{label:t,icon:r,shake:n}=this;return e.label=t,e.icon=r,e.shake=n,e}},FilterComponentWrapper=class extends CustomComponentWrapper{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e),this.onUiChange=()=>this.sourceParams.filterChangedCallback(),this.expectingNewMethods=!0,this.hasBeenActive=!1}isFilterActive(){return this.model!=null}doesFilterPass(e){return this.providedMethods.doesFilterPass(e)}getModel(){return this.model}setModel(e){return this.expectingNewMethods=!0,this.model=e,this.hasBeenActive||(this.hasBeenActive=this.isFilterActive()),this.refreshProps()}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}getOptionalMethods(){return["afterGuiAttached","afterGuiDetached","onNewRowsLoaded","getModelAsString","onAnyFilterChanged"]}setMethods(e){this.expectingNewMethods===!1&&this.hasBeenActive&&this.providedMethods?.doesFilterPass!==e?.doesFilterPass&&setTimeout(()=>{this.sourceParams.filterChangedCallback()}),this.expectingNewMethods=!1,super.setMethods(e)}updateModel(e){this.setModel(e).then(()=>this.sourceParams.filterChangedCallback())}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e.onUiChange=this.onUiChange,delete e.filterChangedCallback,delete e.filterModifiedCallback,delete e.valueGetter,e}},import_ag_grid_community4=require("ag-grid-community");function updateFloatingFilterParent(e,t){e.parentFilterInstance(r=>{(r.setModel(t)||import_ag_grid_community4.AgPromise.resolve()).then(()=>{e.filterParams.filterChangedCallback()})})}var FloatingFilterComponentProxy=class{constructor(e,t){this.floatingFilterParams=e,this.refreshProps=t,this.model=null,this.onModelChange=r=>this.updateModel(r)}getProps(){return{...this.floatingFilterParams,model:this.model,onModelChange:this.onModelChange}}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.floatingFilterParams=e,this.refreshProps()}setMethods(e){addOptionalMethods(this.getOptionalMethods(),e,this)}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),updateFloatingFilterParent(this.floatingFilterParams,e)}},FloatingFilterComponentWrapper=class extends CustomComponentWrapper{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e)}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),updateFloatingFilterParent(this.sourceParams,e)}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e}},LoadingOverlayComponentWrapper=class extends CustomComponentWrapper{refresh(e){this.sourceParams=e,this.refreshProps()}},MenuItemComponentWrapper=class extends CustomComponentWrapper{constructor(){super(...arguments),this.active=!1,this.expanded=!1,this.onActiveChange=e=>this.updateActive(e)}setActive(e){this.awaitSetActive(e)}setExpanded(e){this.expanded=e,this.refreshProps()}getOptionalMethods(){return["select","configureDefaults"]}awaitSetActive(e){return this.active=e,this.refreshProps()}updateActive(e){const t=this.awaitSetActive(e);e&&t.then(()=>this.sourceParams.onItemActivated())}getProps(){const e=super.getProps();return e.active=this.active,e.expanded=this.expanded,e.onActiveChange=this.onActiveChange,delete e.onItemActivated,e}},NoRowsOverlayComponentWrapper=class extends CustomComponentWrapper{refresh(e){this.sourceParams=e,this.refreshProps()}},StatusPanelComponentWrapper=class extends CustomComponentWrapper{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},ToolPanelComponentWrapper=class extends CustomComponentWrapper{constructor(){super(...arguments),this.onStateChange=e=>this.updateState(e)}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}getState(){return this.state}updateState(e){this.state=e,this.refreshProps(),this.sourceParams.onStateUpdated()}getProps(){const e=super.getProps();return e.state=this.state,e.onStateChange=this.onStateChange,e}},import_ag_grid_community5=require("ag-grid-community");function getInstance(e,t){(e?.getInstance?.()??import_ag_grid_community5.AgPromise.resolve(void 0)).then(n=>t(n))}function warnReactiveCustomComponents(){(0,import_ag_grid_community5._warnOnce)("As of v32, using custom components with `reactiveCustomComponents = false` is deprecated.")}var MAX_COMPONENT_CREATION_TIME_IN_MS=1e3,PortalManager=class{constructor(e,t,r){this.destroyed=!1,this.portals=[],this.hasPendingPortalUpdate=!1,this.wrappingElement=t||"div",this.refresher=e,this.maxComponentCreationTimeMs=r||MAX_COMPONENT_CREATION_TIME_IN_MS}getPortals(){return this.portals}destroy(){this.destroyed=!0}destroyPortal(e){this.portals=this.portals.filter(t=>t!==e),this.batchUpdate()}getComponentWrappingElement(){return this.wrappingElement}mountReactPortal(e,t,r){this.portals=[...this.portals,e],this.waitForInstance(t,r),this.batchUpdate()}updateReactPortal(e,t){this.portals[this.portals.indexOf(e)]=t,this.batchUpdate()}batchUpdate(){this.hasPendingPortalUpdate||(setTimeout(()=>{this.destroyed||(this.refresher(),this.hasPendingPortalUpdate=!1)}),this.hasPendingPortalUpdate=!0)}waitForInstance(e,t,r=Date.now()){if(this.destroyed){t(null);return}if(e.rendered())t(e);else{if(Date.now()-r>=this.maxComponentCreationTimeMs&&!this.hasPendingPortalUpdate)return;window.setTimeout(()=>{this.waitForInstance(e,t,r)})}}},import_ag_grid_community18=require("ag-grid-community"),import_react22=__toESM(require("react")),import_ag_grid_community16=require("ag-grid-community"),import_react20=__toESM(require("react")),import_ag_grid_community11=require("ag-grid-community"),import_react12=__toESM(require("react")),import_ag_grid_community10=require("ag-grid-community"),import_react11=__toESM(require("react")),import_ag_grid_community9=require("ag-grid-community"),import_react10=__toESM(require("react")),import_ag_grid_community6=require("ag-grid-community"),import_react7=__toESM(require("react")),HeaderCellComp=({ctrl:e})=>{const t=e.isAlive(),{context:r}=(0,import_react7.useContext)(BeansContext),n=t?e.getColId():void 0,[s,d]=(0,import_react7.useState)(),c=(0,import_react7.useRef)(),o=(0,import_react7.useRef)(null),l=(0,import_react7.useRef)(null),a=(0,import_react7.useRef)(null),u=(0,import_react7.useRef)(),C=(0,import_react7.useRef)();t&&!C.current&&(C.current=new import_ag_grid_community6.CssClassManager(()=>o.current));const R=(0,import_react7.useCallback)(i=>{if(o.current=i,c.current=i?r.createBean(new import_ag_grid_community6._EmptyBean):r.destroyBean(c.current),!i||!t)return;const f={setWidth:v=>{o.current&&(o.current.style.width=v)},addOrRemoveCssClass:(v,S)=>C.current.addOrRemoveCssClass(v,S),setAriaSort:v=>{o.current&&(v?(0,import_ag_grid_community6._setAriaSort)(o.current,v):(0,import_ag_grid_community6._removeAriaSort)(o.current))},setUserCompDetails:v=>d(v),getUserCompInstance:()=>u.current||void 0};e.setComp(f,i,l.current,a.current,c.current);const w=e.getSelectAllGui();l.current?.insertAdjacentElement("afterend",w),c.current.addDestroyFunc(()=>w.remove())},[]);(0,import_react7.useLayoutEffect)(()=>showJsComp(s,r,a.current,u),[s]),(0,import_react7.useEffect)(()=>{e.setDragSource(o.current)},[s]);const y=(0,import_react7.useMemo)(()=>!!(s?.componentFromFramework&&isComponentStateless(s.componentClass)),[s]),m=s&&s.componentFromFramework,h=s&&s.componentClass;return import_react7.default.createElement("div",{ref:R,className:"ag-header-cell","col-id":n,role:"columnheader"},import_react7.default.createElement("div",{ref:l,className:"ag-header-cell-resize",role:"presentation"}),import_react7.default.createElement("div",{ref:a,className:"ag-header-cell-comp-wrapper",role:"presentation"},m&&y&&import_react7.default.createElement(h,{...s.params}),m&&!y&&import_react7.default.createElement(h,{...s.params,ref:u})))},headerCellComp_default=(0,import_react7.memo)(HeaderCellComp),import_ag_grid_community7=require("ag-grid-community"),import_react8=__toESM(require("react")),HeaderFilterCellComp=({ctrl:e})=>{const{context:t,gos:r}=(0,import_react8.useContext)(BeansContext),[n,s]=(0,import_react8.useState)(()=>new CssClasses("ag-header-cell","ag-floating-filter")),[d,c]=(0,import_react8.useState)(()=>new CssClasses),[o,l]=(0,import_react8.useState)(()=>new CssClasses("ag-floating-filter-button","ag-hidden")),[a,u]=(0,import_react8.useState)("false"),[C,R]=(0,import_react8.useState)(),[,y]=(0,import_react8.useState)(1),m=(0,import_react8.useRef)(),h=(0,import_react8.useRef)(null),i=(0,import_react8.useRef)(null),f=(0,import_react8.useRef)(null),w=(0,import_react8.useRef)(null),v=(0,import_react8.useRef)(),S=(0,import_react8.useRef)(),b=_=>{_!=null&&v.current&&v.current(_)},x=(0,import_react8.useCallback)(_=>{if(h.current=_,m.current=_?t.createBean(new import_ag_grid_community7._EmptyBean):t.destroyBean(m.current),!_)return;S.current=new import_ag_grid_community7.AgPromise(B=>{v.current=B});const G={addOrRemoveCssClass:(B,H)=>s(A=>A.setClass(B,H)),addOrRemoveBodyCssClass:(B,H)=>c(A=>A.setClass(B,H)),setButtonWrapperDisplayed:B=>{l(H=>H.setClass("ag-hidden",!B)),u(B?"false":"true")},setWidth:B=>{h.current&&(h.current.style.width=B)},setCompDetails:B=>R(B),getFloatingFilterComp:()=>S.current?S.current:null,setMenuIcon:B=>w.current?.appendChild(B)};e.setComp(G,_,w.current,i.current,m.current)},[]);(0,import_react8.useLayoutEffect)(()=>showJsComp(C,t,i.current,b),[C]);const E=(0,import_react8.useMemo)(()=>n.toString(),[n]),M=(0,import_react8.useMemo)(()=>d.toString(),[d]),F=(0,import_react8.useMemo)(()=>o.toString(),[o]),p=(0,import_react8.useMemo)(()=>!!(C&&C.componentFromFramework&&isComponentStateless(C.componentClass)),[C]),g=(0,import_react8.useMemo)(()=>r.get("reactiveCustomComponents"),[]),k=(0,import_react8.useMemo)(()=>{if(C)if(g){const _=new FloatingFilterComponentProxy(C.params,()=>y(G=>G+1));return b(_),_}else C.componentFromFramework&&warnReactiveCustomComponents()},[C]),z=k?.getProps(),O=C&&C.componentFromFramework,N=C&&C.componentClass;return import_react8.default.createElement("div",{ref:x,className:E,role:"gridcell"},import_react8.default.createElement("div",{ref:i,className:M,role:"presentation"},O&&!g&&import_react8.default.createElement(N,{...C.params,ref:p?()=>{}:b}),O&&g&&import_react8.default.createElement(CustomContext.Provider,{value:{setMethods:_=>k.setMethods(_)}},import_react8.default.createElement(N,{...z}))),import_react8.default.createElement("div",{ref:f,"aria-hidden":a,className:F,role:"presentation"},import_react8.default.createElement("button",{ref:w,type:"button",className:"ag-button ag-floating-filter-button-button",tabIndex:-1})))},headerFilterCellComp_default=(0,import_react8.memo)(HeaderFilterCellComp),import_ag_grid_community8=require("ag-grid-community"),import_react9=__toESM(require("react")),HeaderGroupCellComp=({ctrl:e})=>{const{context:t}=(0,import_react9.useContext)(BeansContext),[r,n]=(0,import_react9.useState)(()=>new CssClasses),[s,d]=(0,import_react9.useState)(()=>new CssClasses),[c,o]=(0,import_react9.useState)("false"),[l,a]=(0,import_react9.useState)(),[u,C]=(0,import_react9.useState)(),R=(0,import_react9.useMemo)(()=>e.getColId(),[]),y=(0,import_react9.useRef)(),m=(0,import_react9.useRef)(null),h=(0,import_react9.useRef)(null),i=(0,import_react9.useRef)(null),f=(0,import_react9.useRef)(),w=(0,import_react9.useCallback)(M=>{if(m.current=M,y.current=M?t.createBean(new import_ag_grid_community8._EmptyBean):t.destroyBean(y.current),!M)return;const F={setWidth:p=>{m.current&&(m.current.style.width=p)},addOrRemoveCssClass:(p,g)=>n(k=>k.setClass(p,g)),setHeaderWrapperHidden:p=>{const g=i.current;g&&(p?g.style.setProperty("display","none"):g.style.removeProperty("display"))},setHeaderWrapperMaxHeight:p=>{const g=i.current;g&&(p!=null?g.style.setProperty("max-height",`${p}px`):g.style.removeProperty("max-height"),g.classList.toggle("ag-header-cell-comp-wrapper-limited-height",p!=null))},setUserCompDetails:p=>C(p),setResizableDisplayed:p=>{d(g=>g.setClass("ag-hidden",!p)),o(p?"false":"true")},setAriaExpanded:p=>a(p),getUserCompInstance:()=>f.current||void 0};e.setComp(F,M,h.current,i.current,y.current)},[]);(0,import_react9.useLayoutEffect)(()=>showJsComp(u,t,i.current),[u]),(0,import_react9.useEffect)(()=>{m.current&&e.setDragSource(m.current)},[u]);const v=(0,import_react9.useMemo)(()=>!!(u?.componentFromFramework&&isComponentStateless(u.componentClass)),[u]),S=(0,import_react9.useMemo)(()=>"ag-header-group-cell "+r.toString(),[r]),b=(0,import_react9.useMemo)(()=>"ag-header-cell-resize "+s.toString(),[s]),x=u&&u.componentFromFramework,E=u&&u.componentClass;return import_react9.default.createElement("div",{ref:w,className:S,"col-id":R,role:"columnheader","aria-expanded":l},import_react9.default.createElement("div",{ref:i,className:"ag-header-cell-comp-wrapper",role:"presentation"},x&&v&&import_react9.default.createElement(E,{...u.params}),x&&!v&&import_react9.default.createElement(E,{...u.params,ref:f})),import_react9.default.createElement("div",{ref:h,"aria-hidden":c,className:b}))},headerGroupCellComp_default=(0,import_react9.memo)(HeaderGroupCellComp),HeaderRowComp=({ctrl:e})=>{const{context:t}=(0,import_react10.useContext)(BeansContext),{topOffset:r,rowHeight:n}=(0,import_react10.useMemo)(()=>e.getTopAndHeight(),[]),s=e.getAriaRowIndex(),d=e.getHeaderRowClass(),[c,o]=(0,import_react10.useState)(()=>n+"px"),[l,a]=(0,import_react10.useState)(()=>r+"px"),u=(0,import_react10.useRef)(null),C=(0,import_react10.useRef)(null),[R,y]=(0,import_react10.useState)(()=>e.getHeaderCtrls()),m=(0,import_react10.useRef)(),h=(0,import_react10.useRef)(null),i=(0,import_react10.useCallback)(v=>{if(h.current=v,m.current=v?t.createBean(new import_ag_grid_community9._EmptyBean):t.destroyBean(m.current),!v)return;const S={setHeight:b=>o(b),setTop:b=>a(b),setHeaderCtrls:(b,x,E)=>{C.current=u.current,u.current=b;const M=getNextValueIfDifferent(C.current,b,x);M!==C.current&&agFlushSync(E,()=>y(M))},setWidth:b=>{h.current&&(h.current.style.width=b)}};e.setComp(S,m.current,!1)},[]),f=(0,import_react10.useMemo)(()=>({height:c,top:l}),[c,l]),w=(0,import_react10.useCallback)(v=>{switch(e.getType()){case import_ag_grid_community9.HeaderRowType.COLUMN_GROUP:return import_react10.default.createElement(headerGroupCellComp_default,{ctrl:v,key:v.instanceId});case import_ag_grid_community9.HeaderRowType.FLOATING_FILTER:return import_react10.default.createElement(headerFilterCellComp_default,{ctrl:v,key:v.instanceId});default:return import_react10.default.createElement(headerCellComp_default,{ctrl:v,key:v.instanceId})}},[]);return import_react10.default.createElement("div",{ref:i,className:d,role:"row",style:f,"aria-rowindex":s},R.map(w))},headerRowComp_default=(0,import_react10.memo)(HeaderRowComp),HeaderRowContainerComp=({pinned:e})=>{const[t,r]=(0,import_react11.useState)(!0),[n,s]=(0,import_react11.useState)([]),{context:d}=(0,import_react11.useContext)(BeansContext),c=(0,import_react11.useRef)(null),o=(0,import_react11.useRef)(null),l=(0,import_react11.useRef)(),a=e==="left",u=e==="right",C=!a&&!u,R=(0,import_react11.useCallback)(h=>{if(c.current=h,l.current=h?d.createBean(new import_ag_grid_community10.HeaderRowContainerCtrl(e)):d.destroyBean(l.current),!h)return;const i={setDisplayed:r,setCtrls:f=>s(f),setCenterWidth:f=>{o.current&&(o.current.style.width=f)},setViewportScrollLeft:f=>{c.current&&(c.current.scrollLeft=f)},setPinnedContainerWidth:f=>{c.current&&(c.current.style.width=f,c.current.style.minWidth=f,c.current.style.maxWidth=f)}};l.current.setComp(i,c.current)},[]),y=t?"":"ag-hidden",m=()=>n.map(h=>import_react11.default.createElement(headerRowComp_default,{ctrl:h,key:h.instanceId}));return import_react11.default.createElement(import_react11.default.Fragment,null,a&&import_react11.default.createElement("div",{ref:R,className:"ag-pinned-left-header "+y,"aria-hidden":!t,role:"rowgroup"},m()),u&&import_react11.default.createElement("div",{ref:R,className:"ag-pinned-right-header "+y,"aria-hidden":!t,role:"rowgroup"},m()),C&&import_react11.default.createElement("div",{ref:R,className:"ag-header-viewport "+y,role:"presentation"},import_react11.default.createElement("div",{ref:o,className:"ag-header-container",role:"rowgroup"},m())))},headerRowContainerComp_default=(0,import_react11.memo)(HeaderRowContainerComp),GridHeaderComp=()=>{const[e,t]=(0,import_react12.useState)(()=>new CssClasses),[r,n]=(0,import_react12.useState)(),{context:s}=(0,import_react12.useContext)(BeansContext),d=(0,import_react12.useRef)(null),c=(0,import_react12.useRef)(),o=(0,import_react12.useCallback)(u=>{if(d.current=u,c.current=u?s.createBean(new import_ag_grid_community11.GridHeaderCtrl):s.destroyBean(c.current),!u)return;const C={addOrRemoveCssClass:(R,y)=>t(m=>m.setClass(R,y)),setHeightAndMinHeight:R=>n(R)};c.current.setComp(C,u,u)},[]),l=(0,import_react12.useMemo)(()=>"ag-header "+e.toString(),[e]),a=(0,import_react12.useMemo)(()=>({height:r,minHeight:r}),[r]);return import_react12.default.createElement("div",{ref:o,className:l,style:a,role:"presentation"},import_react12.default.createElement(headerRowContainerComp_default,{pinned:"left"}),import_react12.default.createElement(headerRowContainerComp_default,{pinned:null}),import_react12.default.createElement(headerRowContainerComp_default,{pinned:"right"}))},gridHeaderComp_default=(0,import_react12.memo)(GridHeaderComp),import_react13=require("react"),useReactCommentEffect=(e,t)=>{(0,import_react13.useEffect)(()=>{const r=t.current;if(r){const n=r.parentElement;if(n){const s=document.createComment(e);return n.insertBefore(s,r),()=>{n.removeChild(s)}}}},[e])},reactComment_default=useReactCommentEffect,import_ag_grid_community15=require("ag-grid-community"),import_react19=__toESM(require("react")),import_ag_grid_community14=require("ag-grid-community"),import_react18=__toESM(require("react")),import_ag_grid_community13=require("ag-grid-community"),import_react17=__toESM(require("react")),import_ag_grid_community12=require("ag-grid-community"),CellEditorComponentProxy=class{constructor(e,t){this.cellEditorParams=e,this.refreshProps=t,this.instanceCreated=new import_ag_grid_community12.AgPromise(r=>{this.resolveInstanceCreated=r}),this.onValueChange=r=>this.updateValue(r),this.value=e.value}getProps(){return{...this.cellEditorParams,initialValue:this.cellEditorParams.value,value:this.value,onValueChange:this.onValueChange}}getValue(){return this.value}refresh(e){this.cellEditorParams=e,this.refreshProps()}setMethods(e){addOptionalMethods(this.getOptionalMethods(),e,this)}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}setRef(e){this.componentInstance=e,this.resolveInstanceCreated?.(),this.resolveInstanceCreated=void 0}getOptionalMethods(){return["isCancelBeforeStart","isCancelAfterEnd","focusIn","focusOut","afterGuiAttached"]}updateValue(e){this.value=e,this.refreshProps()}},import_react15=__toESM(require("react")),import_react_dom3=require("react-dom"),import_react14=require("react"),useEffectOnce=e=>{const t=(0,import_react14.useRef)(e),r=(0,import_react14.useRef)(),n=(0,import_react14.useRef)(!1),s=(0,import_react14.useRef)(!1),[,d]=(0,import_react14.useState)(0);n.current&&(s.current=!0),(0,import_react14.useEffect)(()=>(n.current||(r.current=t.current(),n.current=!0),d(c=>c+1),()=>{s.current&&r.current?.()}),[])},PopupEditorComp=e=>{const[t,r]=(0,import_react15.useState)(),{context:n,popupService:s,localeService:d,gos:c,editService:o}=(0,import_react15.useContext)(BeansContext);return useEffectOnce(()=>{const{editDetails:l,cellCtrl:a,eParentCell:u}=e,{compDetails:C}=l,R=c.get("stopEditingWhenCellsLoseFocus"),y=n.createBean(o.createPopupEditorWrapper(C.params)),m=y.getGui();if(e.jsChildComp){const S=e.jsChildComp.getGui();S&&m.appendChild(S)}const h={column:a.getColumn(),rowNode:a.getRowNode(),type:"popupCellEditor",eventSource:u,ePopup:m,position:l.popupPosition,keepWithinBounds:!0},i=s.positionPopupByComponent.bind(s,h),f=d.getLocaleTextFunc(),w=s.addPopup({modal:R,eChild:m,closeOnEsc:!0,closedCallback:()=>{a.onPopupEditorClosed()},anchorToElement:u,positionCallback:i,ariaLabel:f("ariaLabelCellEditor","Cell Editor")}),v=w?w.hideFunc:void 0;return r(y),e.jsChildComp?.afterGuiAttached?.(),()=>{v?.(),n.destroyBean(y)}}),import_react15.default.createElement(import_react15.default.Fragment,null,t&&e.wrappedContent&&(0,import_react_dom3.createPortal)(e.wrappedContent,t.getGui()))},popupEditorComp_default=(0,import_react15.memo)(PopupEditorComp),import_react16=require("react"),useJsCellRenderer=(e,t,r,n,s,d)=>{const{context:c}=(0,import_react16.useContext)(BeansContext),o=(0,import_react16.useCallback)(()=>{const l=s.current;if(!l)return;const a=l.getGui();a&&a.parentElement&&a.parentElement.removeChild(a),c.destroyBean(l),s.current=void 0},[]);(0,import_react16.useEffect)(()=>{const l=e!=null,a=e?.compDetails&&!e.compDetails.componentFromFramework,u=t&&r==null;if(!(l&&a&&!u)){o();return}const R=e.compDetails;if(s.current){const m=s.current,i=m.refresh!=null&&e.force==!1?m.refresh(R.params):!1;if(i===!0||i===void 0)return;o()}const y=R.newAgStackInstance();y?.then(m=>{if(!m)return;const h=m.getGui();if(!h)return;(t?r:d.current).appendChild(h),s.current=m})},[e,t,n]),(0,import_react16.useEffect)(()=>o,[])},showJsRenderer_default=useJsCellRenderer,jsxEditorProxy=(e,t,r)=>{const{compProxy:n}=e;r(n);const s=n.getProps(),d=isComponentStateless(t);return import_react17.default.createElement(CustomContext.Provider,{value:{setMethods:c=>n.setMethods(c)}},d?import_react17.default.createElement(t,{...s}):import_react17.default.createElement(t,{...s,ref:c=>n.setRef(c)}))},jsxEditor=(e,t,r)=>{const n=e.compProxy;return import_react17.default.createElement(import_react17.default.Fragment,null,n?jsxEditorProxy(e,t,r):import_react17.default.createElement(t,{...e.compDetails.params,ref:r}))},jsxEditValue=(e,t,r,n,s)=>{const d=e.compDetails,c=d.componentClass,o=d.componentFromFramework&&!e.popup,l=d.componentFromFramework&&e.popup,a=!d.componentFromFramework&&e.popup;return import_react17.default.createElement(import_react17.default.Fragment,null,o&&jsxEditor(e,c,t),l&&import_react17.default.createElement(popupEditorComp_default,{editDetails:e,cellCtrl:n,eParentCell:r,wrappedContent:jsxEditor(e,c,t)}),a&&s&&import_react17.default.createElement(popupEditorComp_default,{editDetails:e,cellCtrl:n,eParentCell:r,jsChildComp:s}))},jsxShowValue=(e,t,r,n,s,d,c)=>{const{compDetails:o,value:l}=e,a=!o,u=o&&o.componentFromFramework,C=o&&o.componentClass,R=l?.toString?l.toString():l,y=()=>import_react17.default.createElement(import_react17.default.Fragment,null,a&&import_react17.default.createElement(import_react17.default.Fragment,null,R),u&&!d&&import_react17.default.createElement(C,{...o.params,key:t,ref:n}),u&&d&&import_react17.default.createElement(C,{...o.params,key:t}));return import_react17.default.createElement(import_react17.default.Fragment,null,s?import_react17.default.createElement("span",{role:"presentation",id:`cell-${r}`,className:"ag-cell-value",ref:c},y()):y())},CellComp=({cellCtrl:e,printLayout:t,editingRow:r})=>{const{context:n}=(0,import_react17.useContext)(BeansContext),{colIdSanitised:s,instanceId:d}=e,c=(0,import_react17.useRef)(),[o,l]=(0,import_react17.useState)(()=>e.isCellRenderer()?void 0:{compDetails:void 0,value:e.getValueToDisplay(),force:!1}),[a,u]=(0,import_react17.useState)(),[C,R]=(0,import_react17.useState)(1),[y,m]=(0,import_react17.useState)(),[h,i]=(0,import_react17.useState)(!1),[f,w]=(0,import_react17.useState)(!1),[v,S]=(0,import_react17.useState)(!1),[b,x]=(0,import_react17.useState)(),E=(0,import_react17.useMemo)(()=>e.isForceWrapper(),[e]),M=(0,import_react17.useMemo)(()=>e.getCellAriaRole(),[e]),F=(0,import_react17.useRef)(null),p=(0,import_react17.useRef)(null),g=(0,import_react17.useRef)(),k=(0,import_react17.useRef)(),z=(0,import_react17.useRef)(),O=(0,import_react17.useRef)([]),N=(0,import_react17.useRef)(),[_,G]=(0,import_react17.useState)(0),B=(0,import_react17.useCallback)(T=>{N.current=T,G(j=>j+1)},[]),H=o!=null&&(h||v||f),A=E||H,W=(0,import_react17.useCallback)(T=>{if(k.current=T,T){const j=T.isCancelBeforeStart&&T.isCancelBeforeStart();setTimeout(()=>{j?(e.stopEditing(!0),e.focusCell(!0)):e.cellEditorAttached()})}},[e]),D=(0,import_react17.useRef)();D.current||(D.current=new import_ag_grid_community13.CssClassManager(()=>F.current)),showJsRenderer_default(o,A,N.current,_,g,F);const Q=(0,import_react17.useRef)();(0,import_react17.useLayoutEffect)(()=>{const T=Q.current,j=o;if(Q.current=o,T==null||T.compDetails==null||j==null||j.compDetails==null)return;const $=T.compDetails,I=j.compDetails;if($.componentClass!=I.componentClass||p.current?.refresh==null)return;p.current.refresh(I.params)!=!0&&R(J=>J+1)},[o]),(0,import_react17.useLayoutEffect)(()=>{if(!(a&&!a.compDetails.componentFromFramework))return;const j=a.compDetails,$=a.popup===!0,I=j.newAgStackInstance();return I.then(V=>{if(!V)return;const J=V.getGui();W(V),$||((E?z:F).current?.appendChild(J),V.afterGuiAttached&&V.afterGuiAttached()),x(V)}),()=>{I.then(V=>{const J=V.getGui();n.destroyBean(V),W(void 0),x(void 0),J?.parentElement?.removeChild(J)})}},[a]);const P=(0,import_react17.useCallback)(T=>{if(z.current=T,!T){O.current.forEach($=>$()),O.current=[];return}const j=$=>{if($){const I=$.getGui();T.insertAdjacentElement("afterbegin",I),O.current.push(()=>{n.destroyBean($),(0,import_ag_grid_community13._removeFromParent)(I)})}return $};if(h){const $=e.createSelectionCheckbox();j($)}v&&j(e.createDndSource()),f&&j(e.createRowDragComp())},[e,n,v,f,h]),L=(0,import_react17.useCallback)(T=>{if(F.current=T,c.current=T?n.createBean(new import_ag_grid_community13._EmptyBean):n.destroyBean(c.current),!T||!e)return;const j={addOrRemoveCssClass:(I,V)=>D.current.addOrRemoveCssClass(I,V),setUserStyles:I=>m(I),getFocusableElement:()=>F.current,setIncludeSelection:I=>i(I),setIncludeRowDrag:I=>w(I),setIncludeDndSource:I=>S(I),getCellEditor:()=>k.current||null,getCellRenderer:()=>p.current??g.current,getParentOfValue:()=>N.current??z.current??F.current,setRenderDetails:(I,V,J)=>{l(X=>X?.compDetails!==I||X?.value!==V||X?.force!==J?{value:V,compDetails:I,force:J}:X)},setEditDetails:(I,V,J,X)=>{if(I){let te;X?te=new CellEditorComponentProxy(I.params,()=>R(oe=>oe+1)):I.componentFromFramework&&warnReactiveCustomComponents(),u({compDetails:I,popup:V,popupPosition:J,compProxy:te}),V||l(void 0)}else u(te=>{te?.compProxy&&(k.current=void 0)})}},$=z.current||void 0;e.setComp(j,T,$,t,r,c.current)},[]),U=(0,import_react17.useMemo)(()=>!!(o?.compDetails?.componentFromFramework&&isComponentStateless(o.compDetails.componentClass)),[o]);(0,import_react17.useLayoutEffect)(()=>{F.current&&(D.current.addOrRemoveCssClass("ag-cell-value",!A),D.current.addOrRemoveCssClass("ag-cell-inline-editing",!!a&&!a.popup),D.current.addOrRemoveCssClass("ag-cell-popup-editing",!!a&&!!a.popup),D.current.addOrRemoveCssClass("ag-cell-not-inline-editing",!a||!!a.popup),e.getRowCtrl()?.setInlineEditingCss(),e.shouldRestoreFocus()&&!e.isEditing()&&F.current.focus({preventScroll:!0}))});const Y=()=>import_react17.default.createElement(import_react17.default.Fragment,null,o!=null&&jsxShowValue(o,C,d,p,A,U,B),a!=null&&jsxEditValue(a,W,F.current,e,b)),ee=(0,import_react17.useCallback)(()=>e.onFocusOut(),[]);return import_react17.default.createElement("div",{ref:L,style:y,role:M,"col-id":s,onBlur:ee},A?import_react17.default.createElement("div",{className:"ag-cell-wrapper",role:"presentation",ref:P},Y()):Y())},cellComp_default=(0,import_react17.memo)(CellComp),RowComp=({rowCtrl:e,containerType:t})=>{const{context:r,gos:n}=(0,import_react18.useContext)(BeansContext),s=(0,import_react18.useRef)(),d=(0,import_react18.useRef)(e.getDomOrder()),c=e.isFullWidth(),o=e.getRowNode().displayed,[l,a]=(0,import_react18.useState)(()=>o?e.getRowIndex():null),[u,C]=(0,import_react18.useState)(()=>e.getRowId()),[R,y]=(0,import_react18.useState)(()=>e.getBusinessKey()),[m,h]=(0,import_react18.useState)(()=>e.getRowStyles()),i=(0,import_react18.useRef)(null),f=(0,import_react18.useRef)(null),[w,v]=(0,import_react18.useState)(()=>null),[S,b]=(0,import_react18.useState)(),[x,E]=(0,import_react18.useState)(()=>o?e.getInitialRowTop(t):void 0),[M,F]=(0,import_react18.useState)(()=>o?e.getInitialTransform(t):void 0),p=(0,import_react18.useRef)(null),g=(0,import_react18.useRef)(),k=(0,import_react18.useRef)(!1),[z,O]=(0,import_react18.useState)(0);(0,import_react18.useEffect)(()=>{if(k.current||!S||z>10)return;const P=p.current?.firstChild;P?(e.setupDetailRowAutoHeight(P),k.current=!0):O(L=>L+1)},[S,z]);const N=(0,import_react18.useRef)();N.current||(N.current=new import_ag_grid_community14.CssClassManager(()=>p.current));const _=(0,import_react18.useCallback)(P=>{if(p.current=P,s.current=P?r.createBean(new import_ag_grid_community14._EmptyBean):r.destroyBean(s.current),!P){e.unsetComp(t);return}if(!e.isAlive())return;const L={setTop:E,setTransform:F,addOrRemoveCssClass:(U,Y)=>N.current.addOrRemoveCssClass(U,Y),setDomOrder:U=>d.current=U,setRowIndex:a,setRowId:C,setRowBusinessKey:y,setUserStyles:h,setCellCtrls:(U,Y)=>{f.current=i.current,i.current=U;const ee=getNextValueIfDifferent(f.current,U,d.current);ee!==f.current&&agFlushSync(Y,()=>v(ee))},showFullWidth:U=>b(U),getFullWidthCellRenderer:()=>g.current,refreshFullWidth:U=>W.current?(b(Y=>({...Y,params:U()})),!0):!g.current||!g.current.refresh?!1:g.current.refresh(U())};e.setComp(L,P,t,s.current)},[]);(0,import_react18.useLayoutEffect)(()=>showJsComp(S,r,p.current,g),[S]);const G=(0,import_react18.useMemo)(()=>{const P={top:x,transform:M};return Object.assign(P,m),P},[x,M,m]),B=c&&S?.componentFromFramework,H=!c&&w!=null,A=(0,import_react18.useMemo)(()=>!!(S?.componentFromFramework&&isComponentStateless(S.componentClass)),[S]),W=(0,import_react18.useRef)(!1);(0,import_react18.useEffect)(()=>{W.current=A&&!!S&&!!n.get("reactiveCustomComponents")},[A,S]);const D=()=>w?.map(P=>import_react18.default.createElement(cellComp_default,{cellCtrl:P,editingRow:e.isEditing(),printLayout:e.isPrintLayout(),key:P.instanceId})),Q=()=>{const P=S.componentClass;return import_react18.default.createElement(import_react18.default.Fragment,null,A?import_react18.default.createElement(P,{...S.params}):import_react18.default.createElement(P,{...S.params,ref:g}))};return import_react18.default.createElement("div",{ref:_,role:"row",style:G,"row-index":l,"row-id":u,"row-business-key":R},H&&D(),B&&Q())},rowComp_default=(0,import_react18.memo)(RowComp),RowContainerComp=({name:e})=>{const{context:t}=(0,import_react19.useContext)(BeansContext),r=(0,import_react19.useMemo)(()=>(0,import_ag_grid_community15._getRowContainerOptions)(e),[e]),n=(0,import_react19.useRef)(null),s=(0,import_react19.useRef)(null),d=(0,import_react19.useRef)([]),c=(0,import_react19.useRef)([]),[o,l]=(0,import_react19.useState)(()=>[]),a=(0,import_react19.useRef)(!1),u=(0,import_react19.useRef)(),C=(0,import_react19.useMemo)(()=>classesList(r.viewport),[r]),R=(0,import_react19.useMemo)(()=>classesList(r.container),[r]),y=r.type==="center",m=y?n:s;reactComment_default(" AG Row Container "+e+" ",m);const h=(0,import_react19.useCallback)(()=>y?n.current!=null&&s.current!=null:s.current!=null,[]),i=(0,import_react19.useCallback)(()=>y?n.current==null&&s.current==null:s.current==null,[]),f=(0,import_react19.useCallback)(()=>{if(i()&&(u.current=t.destroyBean(u.current)),h()){const b=E=>{const M=getNextValueIfDifferent(c.current,d.current,a.current);M!==c.current&&(c.current=M,agFlushSync(E,()=>l(M)))},x={setHorizontalScroll:E=>{n.current&&(n.current.scrollLeft=E)},setViewportHeight:E=>{n.current&&(n.current.style.height=E)},setRowCtrls:({rowCtrls:E,useFlushSync:M})=>{const F=!!M&&d.current.length>0&&E.length>0;d.current=E,b(F)},setDomOrder:E=>{a.current!=E&&(a.current=E,b(!1))},setContainerWidth:E=>{s.current&&(s.current.style.width=E)},setOffsetTop:E=>{s.current&&(s.current.style.transform=`translateY(${E})`)}};u.current=t.createBean(new import_ag_grid_community15.RowContainerCtrl(e)),u.current.setComp(x,s.current,n.current)}},[h,i]),w=(0,import_react19.useCallback)(b=>{s.current=b,f()},[f]),v=(0,import_react19.useCallback)(b=>{n.current=b,f()},[f]),S=()=>import_react19.default.createElement("div",{className:R,ref:w,role:"rowgroup"},o.map(b=>import_react19.default.createElement(rowComp_default,{rowCtrl:b,containerType:r.type,key:b.instanceId})));return import_react19.default.createElement(import_react19.default.Fragment,null,y?import_react19.default.createElement("div",{className:C,ref:v,role:"presentation"},S()):S())},rowContainerComp_default=(0,import_react19.memo)(RowContainerComp),GridBodyComp=()=>{const{context:e,resizeObserverService:t}=(0,import_react20.useContext)(BeansContext),[r,n]=(0,import_react20.useState)(""),[s,d]=(0,import_react20.useState)(0),[c,o]=(0,import_react20.useState)(0),[l,a]=(0,import_react20.useState)("0px"),[u,C]=(0,import_react20.useState)("0px"),[R,y]=(0,import_react20.useState)("100%"),[m,h]=(0,import_react20.useState)("0px"),[i,f]=(0,import_react20.useState)("0px"),[w,v]=(0,import_react20.useState)("100%"),[S,b]=(0,import_react20.useState)(""),[x,E]=(0,import_react20.useState)(""),[M,F]=(0,import_react20.useState)(null),[p,g]=(0,import_react20.useState)(""),[k,z]=(0,import_react20.useState)(null),[O,N]=(0,import_react20.useState)("ag-layout-normal"),_=(0,import_react20.useRef)();_.current||(_.current=new import_ag_grid_community16.CssClassManager(()=>G.current));const G=(0,import_react20.useRef)(null),B=(0,import_react20.useRef)(null),H=(0,import_react20.useRef)(null),A=(0,import_react20.useRef)(null),W=(0,import_react20.useRef)(null),D=(0,import_react20.useRef)(null),Q=(0,import_react20.useRef)(null),P=(0,import_react20.useRef)([]),L=(0,import_react20.useRef)([]);reactComment_default(" AG Grid Body ",G),reactComment_default(" AG Pinned Top ",B),reactComment_default(" AG Sticky Top ",H),reactComment_default(" AG Middle ",D),reactComment_default(" AG Pinned Bottom ",Q);const U=(0,import_react20.useCallback)(Z=>{if(G.current=Z,!Z){P.current=e.destroyBeans(P.current),L.current.forEach(q=>q()),L.current=[];return}if(!e)return;const ne=(q,K)=>{q.appendChild(K),L.current.push(()=>q.removeChild(K))},ae=q=>{const K=e.createBean(new q);return P.current.push(K),K},se=(q,K,de)=>{ne(q,document.createComment(de)),ne(q,ae(K).getGui())};se(Z,import_ag_grid_community16.FakeHScrollComp," AG Fake Horizontal Scroll "),se(Z,import_ag_grid_community16.OverlayWrapperComponent," AG Overlay Wrapper "),W.current&&se(W.current,import_ag_grid_community16.FakeVScrollComp," AG Fake Vertical Scroll ");const le={setRowAnimationCssOnBodyViewport:n,setColumnCount:q=>{G.current&&(0,import_ag_grid_community16._setAriaColCount)(G.current,q)},setRowCount:q=>{G.current&&(0,import_ag_grid_community16._setAriaRowCount)(G.current,q)},setTopHeight:d,setBottomHeight:o,setStickyTopHeight:a,setStickyTopTop:C,setStickyTopWidth:y,setTopDisplay:b,setBottomDisplay:E,setColumnMovingCss:(q,K)=>_.current.addOrRemoveCssClass(q,K),updateLayoutClasses:N,setAlwaysVerticalScrollClass:F,setPinnedTopBottomOverflowY:g,setCellSelectableCss:(q,K)=>z(K?q:null),setBodyViewportWidth:q=>{D.current&&(D.current.style.width=q)},registerBodyViewportResizeListener:q=>{if(D.current){const K=t.observeResize(D.current,q);L.current.push(()=>K())}},setStickyBottomHeight:h,setStickyBottomBottom:f,setStickyBottomWidth:v},ce=e.createBean(new import_ag_grid_community16.GridBodyCtrl);P.current.push(ce),ce.setComp(le,Z,D.current,B.current,Q.current,H.current,A.current)},[]),Y=(0,import_react20.useMemo)(()=>classesList("ag-root","ag-unselectable",O),[O]),ee=(0,import_react20.useMemo)(()=>classesList("ag-body-viewport",r,O,M,k),[r,O,M,k]),T=(0,import_react20.useMemo)(()=>classesList("ag-body",O),[O]),j=(0,import_react20.useMemo)(()=>classesList("ag-floating-top",k),[k]),$=(0,import_react20.useMemo)(()=>classesList("ag-sticky-top",k),[k]),I=(0,import_react20.useMemo)(()=>classesList("ag-sticky-bottom",m==="0px"?"ag-hidden":null,k),[k,m]),V=(0,import_react20.useMemo)(()=>classesList("ag-floating-bottom",k),[k]),J=(0,import_react20.useMemo)(()=>({height:s,minHeight:s,display:S,overflowY:p}),[s,S,p]),X=(0,import_react20.useMemo)(()=>({height:l,top:u,width:R}),[l,u,R]),te=(0,import_react20.useMemo)(()=>({height:m,bottom:i,width:w}),[m,i,w]),oe=(0,import_react20.useMemo)(()=>({height:c,minHeight:c,display:x,overflowY:p}),[c,x,p]),ue=Z=>import_react20.default.createElement(rowContainerComp_default,{name:Z,key:`${Z}-container`}),re=({section:Z,children:ne,className:ae,style:se})=>import_react20.default.createElement("div",{ref:Z,className:ae,role:"presentation",style:se},ne.map(ue));return import_react20.default.createElement("div",{ref:U,className:Y,role:"treegrid"},import_react20.default.createElement(gridHeaderComp_default,null),re({section:B,className:j,style:J,children:["topLeft","topCenter","topRight","topFullWidth"]}),import_react20.default.createElement("div",{className:T,ref:W,role:"presentation"},re({section:D,className:ee,children:["left","center","right","fullWidth"]})),re({section:H,className:$,style:X,children:["stickyTopLeft","stickyTopCenter","stickyTopRight","stickyTopFullWidth"]}),re({section:A,className:I,style:te,children:["stickyBottomLeft","stickyBottomCenter","stickyBottomRight","stickyBottomFullWidth"]}),re({section:Q,className:V,style:oe,children:["bottomLeft","bottomCenter","bottomRight","bottomFullWidth"]}))},gridBodyComp_default=(0,import_react20.memo)(GridBodyComp),import_ag_grid_community17=require("ag-grid-community"),import_react21=__toESM(require("react")),TabGuardCompRef=(e,t)=>{const{children:r,eFocusableElement:n,onTabKeyDown:s,gridCtrl:d,forceFocusOutWhenTabGuardsAreEmpty:c}=e,{context:o}=(0,import_react21.useContext)(BeansContext),l=(0,import_react21.useRef)(null),a=(0,import_react21.useRef)(null),u=(0,import_react21.useRef)(),C=i=>{const f=i==null?void 0:parseInt(i,10).toString();[l,a].forEach(w=>{f===void 0?w.current?.removeAttribute("tabindex"):w.current?.setAttribute("tabindex",f)})};(0,import_react21.useImperativeHandle)(t,()=>({forceFocusOutOfContainer(i){u.current?.forceFocusOutOfContainer(i)}}));const R=(0,import_react21.useCallback)(()=>{const i=l.current,f=a.current;if(!i&&!f){u.current=o.destroyBean(u.current);return}if(i&&f){const w={setTabIndex:C};u.current=o.createBean(new import_ag_grid_community17.TabGuardCtrl({comp:w,eTopGuard:i,eBottomGuard:f,eFocusableElement:n,onTabKeyDown:s,forceFocusOutWhenTabGuardsAreEmpty:c,focusInnerElement:v=>d.focusInnerElement(v)}))}},[]),y=(0,import_react21.useCallback)(i=>{l.current=i,R()},[R]),m=(0,import_react21.useCallback)(i=>{a.current=i,R()},[R]),h=i=>{const f=i==="top"?import_ag_grid_community17.TabGuardClassNames.TAB_GUARD_TOP:import_ag_grid_community17.TabGuardClassNames.TAB_GUARD_BOTTOM;return import_react21.default.createElement("div",{className:`${import_ag_grid_community17.TabGuardClassNames.TAB_GUARD} ${f}`,role:"presentation",ref:i==="top"?y:m})};return import_react21.default.createElement(import_react21.default.Fragment,null,h("top"),r,h("bottom"))},TabGuardComp=(0,import_react21.forwardRef)(TabGuardCompRef),tabGuardComp_default=(0,import_react21.memo)(TabGuardComp),GridComp=({context:e})=>{const[t,r]=(0,import_react22.useState)(""),[n,s]=(0,import_react22.useState)(""),[d,c]=(0,import_react22.useState)(""),[o,l]=(0,import_react22.useState)(null),[a,u]=(0,import_react22.useState)(null),[C,R]=(0,import_react22.useState)(!1),[y,m]=(0,import_react22.useState)(),h=(0,import_react22.useRef)(),i=(0,import_react22.useRef)(null),f=(0,import_react22.useRef)(),[w,v]=(0,import_react22.useState)(null),S=(0,import_react22.useRef)(()=>{}),b=(0,import_react22.useRef)(),x=(0,import_react22.useRef)([]),E=(0,import_react22.useCallback)(()=>{},[]),M=(0,import_react22.useMemo)(()=>e.isDestroyed()?null:e.getBeans(),[e]);reactComment_default(" AG Grid ",i);const F=(0,import_react22.useCallback)(O=>{if(i.current=O,h.current=O?e.createBean(new import_ag_grid_community18.GridCtrl):e.destroyBean(h.current),!O||e.isDestroyed())return;const N=h.current;S.current=N.focusInnerElement.bind(N);const _={destroyGridUi:()=>{},setRtlClass:r,setGridThemeClass:s,forceFocusOutOfContainer:G=>{if(!G&&b.current?.isDisplayed()){b.current.forceFocusOutOfContainer(G);return}f.current?.forceFocusOutOfContainer(G)},updateLayoutClasses:c,getFocusableContainers:()=>{const G=[],B=i.current?.querySelector(".ag-root");return B&&G.push({getGui:()=>B}),x.current.forEach(H=>{H.isDisplayed()&&G.push(H)}),G},setCursor:l,setUserSelect:u};N.setComp(_,O,O),R(!0)},[]);(0,import_react22.useEffect)(()=>{const O=h.current,N=i.current;if(!y||!M||!O||!w||!N)return;const _=[],{watermarkSelector:G,paginationSelector:B,sideBarSelector:H,statusBarSelector:A,gridHeaderDropZonesSelector:W}=O.getOptionalSelectors(),D=[];if(W){const P=e.createBean(new W.component),L=P.getGui();N.insertAdjacentElement("afterbegin",L),D.push(L),_.push(P)}if(H){const P=e.createBean(new H.component),L=P.getGui(),U=w.querySelector(".ag-tab-guard-bottom");U&&(U.insertAdjacentElement("beforebegin",L),D.push(L)),_.push(P),x.current.push(P)}const Q=P=>{const L=e.createBean(new P),U=L.getGui();return N.insertAdjacentElement("beforeend",U),D.push(U),_.push(L),L};if(A&&Q(A.component),B){const P=Q(B.component);b.current=P,x.current.push(P)}return G&&Q(G.component),()=>{e.destroyBeans(_),D.forEach(P=>{P.parentElement?.removeChild(P)})}},[y,w,M]);const p=(0,import_react22.useMemo)(()=>classesList("ag-root-wrapper",t,n,d),[t,n,d]),g=(0,import_react22.useMemo)(()=>classesList("ag-root-wrapper-body","ag-focus-managed",d),[d]),k=(0,import_react22.useMemo)(()=>({userSelect:a??"",WebkitUserSelect:a??"",cursor:o??""}),[a,o]),z=(0,import_react22.useCallback)(O=>{f.current=O,m(O!==null)},[]);return import_react22.default.createElement("div",{ref:F,className:p,style:k,role:"presentation"},import_react22.default.createElement("div",{className:g,ref:v,role:"presentation"},C&&w&&M&&import_react22.default.createElement(BeansContext.Provider,{value:M},import_react22.default.createElement(tabGuardComp_default,{ref:z,eFocusableElement:w,onTabKeyDown:E,gridCtrl:h.current,forceFocusOutWhenTabGuardsAreEmpty:!0},import_react22.default.createElement(gridBodyComp_default,null)))))},gridComp_default=(0,import_react22.memo)(GridComp),import_ag_grid_community19=require("ag-grid-community"),RenderStatusService=class extends import_ag_grid_community19.BeanStub{wireBeans(e){this.ctrlsService=e.ctrlsService}areHeaderCellsRendered(){return this.ctrlsService.getHeaderRowContainerCtrls().every(e=>e.getAllCtrls().every(t=>t.areCellsRendered()))}},AgGridReactUi=e=>{const t=(0,import_react23.useRef)(),r=(0,import_react23.useRef)(null),n=(0,import_react23.useRef)(null),s=(0,import_react23.useRef)([]),d=(0,import_react23.useRef)([]),c=(0,import_react23.useRef)(e),o=(0,import_react23.useRef)(),l=(0,import_react23.useRef)(),a=(0,import_react23.useRef)(!1),[u,C]=(0,import_react23.useState)(void 0),[,R]=(0,import_react23.useState)(0),y=(0,import_react23.useCallback)(i=>{if(r.current=i,!i){s.current.forEach(p=>p()),s.current.length=0;return}const f=e.modules||[];n.current||(n.current=new PortalManager(()=>R(p=>p+1),e.componentWrappingElement,e.maxComponentCreationTimeMs),s.current.push(()=>{n.current?.destroy(),n.current=null}));const w=(0,import_ag_grid_community20._combineAttributesAndGridOptions)(e.gridOptions,e),v=()=>{if(a.current){const p=()=>o.current?.shouldQueueUpdates()?void 0:d.current.shift();let g=p();for(;g;)g(),g=p()}},S=new ReactFrameworkOverrides(v);o.current=S;const b=new RenderStatusService,x={providedBeanInstances:{frameworkComponentWrapper:new ReactFrameworkComponentWrapper(n.current,w.reactiveCustomComponents??(0,import_ag_grid_community20._getGlobalGridOption)("reactiveCustomComponents")??!0),renderStatusService:b},modules:f,frameworkOverrides:S},E=p=>{C(p),p.createBean(b),s.current.push(()=>{p.destroy()}),p.getBean("ctrlsService").whenReady({addDestroyFunc:g=>{s.current.push(g)}},()=>{if(p.isDestroyed())return;const g=t.current;g&&e.setGridApi?.(g)})},M=p=>{p.getBean("ctrlsService").whenReady({addDestroyFunc:g=>{s.current.push(g)}},()=>{d.current.forEach(g=>g()),d.current.length=0,a.current=!0})},F=new import_ag_grid_community20.GridCoreCreator;w.gridId??(w.gridId=l.current),t.current=F.create(i,w,E,M,x),s.current.push(()=>{t.current=void 0}),t.current&&(l.current=t.current.getGridId())},[]),m=(0,import_react23.useMemo)(()=>({height:"100%",...e.containerStyle||{}}),[e.containerStyle]),h=(0,import_react23.useCallback)(i=>{a.current&&!o.current?.shouldQueueUpdates()?i():d.current.push(i)},[]);return(0,import_react23.useEffect)(()=>{const i=extractGridPropertyChanges(c.current,e);c.current=e,h(()=>{t.current&&(0,import_ag_grid_community20._processOnChange)(i,t.current)})},[e]),import_react23.default.createElement("div",{style:m,className:e.className,ref:y},u&&!u.isDestroyed()?import_react23.default.createElement(gridComp_default,{context:u}):null,n.current?.getPortals()??null)};function extractGridPropertyChanges(e,t){const r={};return Object.keys(t).forEach(n=>{const s=t[n];e[n]!==s&&(r[n]=s)}),r}var ReactFrameworkComponentWrapper=class extends import_ag_grid_community20.BaseComponentWrapper{constructor(e,t){super(),this.parent=e,this.reactiveCustomComponents=t}createWrapper(e,t){if(this.reactiveCustomComponents){const s=(d=>{switch(d){case"filter":return FilterComponentWrapper;case"floatingFilterComponent":return FloatingFilterComponentWrapper;case"dateComponent":return DateComponentWrapper;case"dragAndDropImageComponent":return DragAndDropImageComponentWrapper;case"loadingOverlayComponent":return LoadingOverlayComponentWrapper;case"noRowsOverlayComponent":return NoRowsOverlayComponentWrapper;case"statusPanel":return StatusPanelComponentWrapper;case"toolPanel":return ToolPanelComponentWrapper;case"menuItem":return MenuItemComponentWrapper;case"cellRenderer":return CellRendererComponentWrapper}})(t.propertyName);if(s)return new s(e,this.parent,t)}else switch(t.propertyName){case"filter":case"floatingFilterComponent":case"dateComponent":case"dragAndDropImageComponent":case"loadingOverlayComponent":case"noRowsOverlayComponent":case"statusPanel":case"toolPanel":case"menuItem":case"cellRenderer":warnReactiveCustomComponents();break}const r=!t.cellRenderer&&t.propertyName!=="toolPanel";return new ReactComponent(e,this.parent,t,r)}},DetailCellRenderer=(0,import_react23.forwardRef)((e,t)=>{const{ctrlsFactory:r,context:n,gos:s,resizeObserverService:d,rowModel:c}=(0,import_react23.useContext)(BeansContext),[o,l]=(0,import_react23.useState)(()=>new CssClasses),[a,u]=(0,import_react23.useState)(()=>new CssClasses),[C,R]=(0,import_react23.useState)(),[y,m]=(0,import_react23.useState)(),h=(0,import_react23.useRef)(),i=(0,import_react23.useRef)(null),f=(0,import_react23.useRef)(),w=(0,import_react23.useMemo)(()=>import_ag_grid_community20.ModuleRegistry.__getGridRegisteredModules(e.api.getGridId()),[e]),v=(0,import_react23.useMemo)(()=>o.toString()+" ag-details-row",[o]),S=(0,import_react23.useMemo)(()=>a.toString()+" ag-details-grid",[a]);t&&(0,import_react23.useImperativeHandle)(t,()=>({refresh(){return h.current?.refresh()??!1}})),e.template&&(0,import_ag_grid_community20._warnOnce)("detailCellRendererParams.template is not supported by AG Grid React. To change the template, provide a Custom Detail Cell Renderer. See https://ag-grid.com/react-data-grid/master-detail-custom-detail/");const b=(0,import_react23.useCallback)(E=>{if(i.current=E,!E){h.current=n.destroyBean(h.current),f.current?.();return}const M={addOrRemoveCssClass:(p,g)=>l(k=>k.setClass(p,g)),addOrRemoveDetailGridCssClass:(p,g)=>u(k=>k.setClass(p,g)),setDetailGrid:p=>R(p),setRowData:p=>m(p),getGui:()=>i.current},F=r.getInstance("detailCellRenderer");if(F&&(n.createBean(F),F.init(M,e),h.current=F,s.get("detailRowAutoHeight"))){const p=()=>{if(i.current==null)return;const g=i.current.clientHeight;g!=null&&g>0&&setTimeout(()=>{e.node.setRowHeight(g),((0,import_ag_grid_community20._isClientSideRowModel)(s)||(0,import_ag_grid_community20._isServerSideRowModel)(s))&&c.onRowHeightChanged()},0)};f.current=d.observeResize(E,p),p()}},[]),x=(0,import_react23.useCallback)(E=>{h.current?.registerDetailWithMaster(E)},[]);return import_react23.default.createElement("div",{className:v,ref:b},C&&import_react23.default.createElement(AgGridReactUi,{className:S,...C,modules:w,rowData:y,setGridApi:x}))}),ReactFrameworkOverrides=class extends import_ag_grid_community20.VanillaFrameworkOverrides{constructor(e){super("react"),this.processQueuedUpdates=e,this.queueUpdates=!1,this.frameworkComponents={agGroupCellRenderer:groupCellRenderer_default,agGroupRowRenderer:groupCellRenderer_default,agDetailCellRenderer:DetailCellRenderer},this.wrapIncoming=(t,r)=>r==="ensureVisible"?runWithoutFlushSync(t):t(),this.renderingEngine="react"}frameworkComponent(e){return this.frameworkComponents[e]}isFrameworkComponent(e){if(!e)return!1;const t=e.prototype;return!(t&&"getGui"in t)}getLockOnRefresh(){this.queueUpdates=!0}releaseLockOnRefresh(){this.queueUpdates=!1,this.processQueuedUpdates()}shouldQueueUpdates(){return this.queueUpdates}runWhenReadyAsync(){return isReact19()}},AgGridReact=class extends import_react24.Component{constructor(){super(...arguments),this.apiListeners=[],this.setGridApi=e=>{this.api=e,this.apiListeners.forEach(t=>t(e))}}registerApiListener(e){this.apiListeners.push(e)}componentWillUnmount(){this.apiListeners.length=0}render(){return import_react24.default.createElement(AgGridReactUi,{...this.props,setGridApi:this.setGridApi})}},import_react25=require("react");function useGridCustomComponent(e){const{setMethods:t}=(0,import_react25.useContext)(CustomContext);t(e)}function useGridCellEditor(e){useGridCustomComponent(e)}function useGridDate(e){return useGridCustomComponent(e)}function useGridFilter(e){return useGridCustomComponent(e)}function useGridFloatingFilter(e){useGridCustomComponent(e)}function useGridMenuItem(e){useGridCustomComponent(e)}
