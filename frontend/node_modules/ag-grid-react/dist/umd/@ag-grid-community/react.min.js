(function(Me,_e){typeof exports=="object"&&typeof module=="object"?module.exports=_e(require("ag-grid-community"),require("react"),require("react-dom")):typeof define=="function"&&define.amd?define("AgGridReact",["ag-grid-community","react","react-dom"],_e):typeof exports=="object"?exports.AgGridReact=_e(require("ag-grid-community"),require("react"),require("react-dom")):Me.AgGridReact=_e(Me["ag-grid-community"],Me.react,Me["react-dom"])})(this,(Me,_e,ss)=>{var Ve={},ke={exports:Ve},vt=Object.create,We=Object.defineProperty,Rt=Object.getOwnPropertyDescriptor,wt=Object.getOwnPropertyNames,St=Object.getPrototypeOf,bt=Object.prototype.hasOwnProperty,Pt=(e,t)=>{for(var r in t)We(e,r,{get:t[r],enumerable:!0})},tt=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of wt(t))!bt.call(e,s)&&s!==r&&We(e,s,{get:()=>t[s],enumerable:!(n=Rt(t,s))||n.enumerable});return e},Z=(e,t,r)=>(r=e!=null?vt(St(e)):{},tt(t||!e||!e.__esModule?We(r,"default",{value:e,enumerable:!0}):r,e)),Et=e=>tt(We({},"__esModule",{value:!0}),e),rt={};Pt(rt,{AgGridReact:()=>$r,CustomComponentContext:()=>Be,getInstance:()=>ar,useGridCellEditor:()=>Jr,useGridDate:()=>Kr,useGridFilter:()=>Qr,useGridFloatingFilter:()=>Yr,useGridMenuItem:()=>Zr,warnReactiveCustomComponents:()=>Ue}),ke.exports=Et(rt);var st=Z(require("react"),1),he=require("ag-grid-community"),F=Z(require("react"),1),Mt=require("ag-grid-community"),A=Z(require("react"),1),_t=Z(require("react"),1),kt=Z(require("react"),1),Ft=Z(require("react-dom"),1),je=require("ag-grid-community"),Fe=Z(require("react"),1),Gt=require("react"),Ge=require("ag-grid-community"),At=require("react"),xt=require("react-dom"),Bt=require("ag-grid-community"),nt=require("ag-grid-community"),Ot=require("ag-grid-community"),H=Z(require("react"),1),Re=require("ag-grid-community"),S=Z(require("react"),1),It=require("ag-grid-community"),ie=Z(require("react"),1),Dt=require("ag-grid-community"),ce=Z(require("react"),1),ze=require("ag-grid-community"),re=Z(require("react"),1),Te=require("ag-grid-community"),te=Z(require("react"),1),ot=require("ag-grid-community"),B=Z(require("react"),1),Wt=require("ag-grid-community"),L=Z(require("react"),1),Tt=require("react"),at=require("ag-grid-community"),$=Z(require("react"),1),ct=require("ag-grid-community"),G=Z(require("react"),1),$e=require("ag-grid-community"),h=Z(require("react"),1),Nt=require("ag-grid-community"),Ae=Z(require("react"),1),Ht=require("react-dom"),Se=require("react"),Ne=require("react"),He=require("ag-grid-community"),ue=Z(require("react"),1),qt=require("ag-grid-community"),Ut=require("react"),ne=_t.default.createContext({}),xe=(e,t,r,n)=>{if(!e||e.componentFromFramework||t.isDestroyed())return;const d=e.newAgStackInstance();if(d==null)return;let c,o,l=!1;return d.then(a=>{if(l){t.destroyBean(a);return}c=a,o=c.getGui(),r.appendChild(o),it(n,c)}),()=>{l=!0,c&&(o?.parentElement?.removeChild(o),t.destroyBean(c),n&&it(n,void 0))}},it=(e,t)=>{if(e)if(e instanceof Function)e(t);else{const r=e;r.current=t}},fe=(...e)=>e.filter(r=>r!=null&&r!=="").join(" "),pe=class yt{constructor(...t){this.classesMap={},t.forEach(r=>{this.classesMap[r]=!0})}setClass(t,r){if(!!this.classesMap[t]==r)return this;const s=new yt;return s.classesMap={...this.classesMap},s.classesMap[t]=r,s}toString(){return Object.keys(this.classesMap).filter(r=>this.classesMap[r]).join(" ")}},be=e=>{const t=()=>typeof Symbol=="function"&&Symbol.for,r=()=>t()?Symbol.for("react.memo"):60115;return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===r()},Je=kt.default.version?.split(".")[0],Lt=Je==="16"||Je==="17";function Vt(){return Je==="19"}var qe=!1;function jt(e){return qe||setTimeout(()=>qe=!1,0),qe=!0,e()}var Ke=(e,t)=>{!Lt&&e&&!qe?Ft.default.flushSync(t):t()};function Qe(e,t,r){if(t==null||e==null)return t;if(e===t||t.length===0&&e.length===0)return e;if(r||e.length===0&&t.length>0||e.length>0&&t.length===0)return t;const n=[],s=[],d=new Map,c=new Map;for(let o=0;o<t.length;o++){const l=t[o];c.set(l.instanceId,l)}for(let o=0;o<e.length;o++){const l=e[o];d.set(l.instanceId,l),c.has(l.instanceId)&&n.push(l)}for(let o=0;o<t.length;o++){const l=t[o],a=l.instanceId;d.has(a)||s.push(l)}return n.length===e.length&&s.length===0?e:n.length===0&&s.length===t.length?t:n.length===0?s:s.length===0?n:[...n,...s]}var ut=(0,A.forwardRef)((e,t)=>{const{ctrlsFactory:r,context:n}=(0,A.useContext)(ne),s=(0,A.useRef)(null),d=(0,A.useRef)(null),c=(0,A.useRef)(null),o=(0,A.useRef)(null),l=(0,A.useRef)(null),a=(0,A.useRef)(),[u,g]=(0,A.useState)(),[w,v]=(0,A.useState)(),[m,f]=(0,A.useState)(),[i,C]=(0,A.useState)(()=>new pe),[b,R]=(0,A.useState)(()=>new pe("ag-hidden")),[P,E]=(0,A.useState)(()=>new pe("ag-hidden")),[q,M]=(0,A.useState)(()=>new pe("ag-invisible"));(0,A.useImperativeHandle)(t,()=>({refresh(){return!1}})),(0,A.useLayoutEffect)(()=>xe(u,n,d.current),[u]);const k=(0,A.useCallback)(D=>{if(s.current=D,!D){a.current=n.destroyBean(a.current);return}const W={setInnerRenderer:(T,j)=>{g(T),f(j)},setChildCount:T=>v(T),addOrRemoveCssClass:(T,j)=>C(V=>V.setClass(T,j)),setContractedDisplayed:T=>E(j=>j.setClass("ag-hidden",!T)),setExpandedDisplayed:T=>R(j=>j.setClass("ag-hidden",!T)),setCheckboxVisible:T=>M(j=>j.setClass("ag-invisible",!T))},K=r.getInstance("groupCellRendererCtrl");K&&(a.current=n.createBean(K),a.current.init(W,D,c.current,o.current,l.current,ut,e))},[]),O=(0,A.useMemo)(()=>`ag-cell-wrapper ${i.toString()}`,[i]),p=(0,A.useMemo)(()=>`ag-group-expanded ${b.toString()}`,[b]),y=(0,A.useMemo)(()=>`ag-group-contracted ${P.toString()}`,[P]),x=(0,A.useMemo)(()=>`ag-group-checkbox ${q.toString()}`,[q]),oe=u&&u.componentFromFramework,N=oe?u.componentClass:void 0,J=u==null&&m!=null,I=(0,Mt._escapeString)(m,!0);return A.default.createElement("span",{className:O,ref:k,...e.colDef?{}:{role:a.current?.getCellAriaRole()}},A.default.createElement("span",{className:p,ref:o}),A.default.createElement("span",{className:y,ref:l}),A.default.createElement("span",{className:x,ref:c}),A.default.createElement("span",{className:"ag-group-value",ref:d},J&&A.default.createElement(A.default.Fragment,null,I),oe&&A.default.createElement(N,{...u.params})),A.default.createElement("span",{className:"ag-group-child-count"},w))}),lt=ut,Be=(0,Gt.createContext)({setMethods:()=>{}}),zt=e=>{const{initialProps:t,addUpdateCallback:r,CustomComponentClass:n,setMethods:s}=e,[{key:d,...c},o]=(0,Fe.useState)(t);return(0,Fe.useEffect)(()=>{r(l=>o(l))},[]),Fe.default.createElement(Be.Provider,{value:{setMethods:s}},Fe.default.createElement(n,{key:d,...c}))},$t=(0,Fe.memo)(zt),Jt=0;function dt(){return`agPortalKey_${++Jt}`}var pt=class{constructor(e,t,r,n){this.portal=null,this.oldPortal=null,this.reactComponent=e,this.portalManager=t,this.componentType=r,this.suppressFallbackMethods=!!n,this.statelessComponent=this.isStateless(this.reactComponent),this.key=dt(),this.portalKey=dt(),this.instanceCreated=this.isStatelessComponent()?Ge.AgPromise.resolve(!1):new Ge.AgPromise(s=>{this.resolveInstanceCreated=s})}getGui(){return this.eParentElement}getRootElement(){return this.eParentElement.firstChild}destroy(){this.componentInstance&&typeof this.componentInstance.destroy=="function"&&this.componentInstance.destroy();const e=this.portal;e&&this.portalManager.destroyPortal(e)}createParentElement(e){const t=this.portalManager.getComponentWrappingElement(),r=document.createElement(t||"div");return r.classList.add("ag-react-container"),e.reactContainer=r,r}addParentContainerStyleAndClasses(){this.componentInstance&&(this.componentInstance.getReactContainerStyle&&this.componentInstance.getReactContainerStyle()&&((0,Ge._warnOnce)('Since v31.1 "getReactContainerStyle" is deprecated. Apply styling directly to ".ag-react-container" if needed.'),Object.assign(this.eParentElement.style,this.componentInstance.getReactContainerStyle())),this.componentInstance.getReactContainerClasses&&this.componentInstance.getReactContainerClasses()&&((0,Ge._warnOnce)('Since v31.1 "getReactContainerClasses" is deprecated. Apply styling directly to ".ag-react-container" if needed.'),this.componentInstance.getReactContainerClasses().forEach(t=>this.eParentElement.classList.add(t))))}statelessComponentRendered(){return this.eParentElement.childElementCount>0||this.eParentElement.childNodes.length>0}getFrameworkComponentInstance(){return this.componentInstance}isStatelessComponent(){return this.statelessComponent}getReactComponentName(){return this.reactComponent.name}getMemoType(){return this.hasSymbol()?Symbol.for("react.memo"):60115}hasSymbol(){return typeof Symbol=="function"&&Symbol.for}isStateless(e){return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===this.getMemoType()}hasMethod(e){const t=this.getFrameworkComponentInstance();return!!t&&t[e]!=null||this.fallbackMethodAvailable(e)}callMethod(e,t){const r=this.getFrameworkComponentInstance();if(this.isStatelessComponent())return this.fallbackMethod(e,t&&t[0]?t[0]:{});if(!r){setTimeout(()=>this.callMethod(e,t));return}const n=r[e];if(n)return n.apply(r,t);if(this.fallbackMethodAvailable(e))return this.fallbackMethod(e,t&&t[0]?t[0]:{})}addMethod(e,t){this[e]=t}init(e){return this.eParentElement=this.createParentElement(e),this.createOrUpdatePortal(e),new Ge.AgPromise(t=>this.createReactComponent(t))}createOrUpdatePortal(e){this.isStatelessComponent()||(this.ref=t=>{this.componentInstance=t,this.addParentContainerStyleAndClasses(),this.resolveInstanceCreated?.(!0),this.resolveInstanceCreated=void 0},e.ref=this.ref),this.reactElement=this.createElement(this.reactComponent,{...e,key:this.key}),this.portal=(0,xt.createPortal)(this.reactElement,this.eParentElement,this.portalKey)}createElement(e,t){return(0,At.createElement)(e,t)}createReactComponent(e){this.portalManager.mountReactPortal(this.portal,this,e)}rendered(){return this.isStatelessComponent()&&this.statelessComponentRendered()||!!(!this.isStatelessComponent()&&this.getFrameworkComponentInstance())}refreshComponent(e){this.oldPortal=this.portal,this.createOrUpdatePortal(e),this.portalManager.updateReactPortal(this.oldPortal,this.portal)}fallbackMethod(e,t){const r=this[`${e}Component`];if(!this.suppressFallbackMethods&&r)return r.bind(this)(t)}fallbackMethodAvailable(e){return this.suppressFallbackMethods?!1:!!this[`${e}Component`]}};function Ye(e,t,r){e.forEach(n=>{const s=t[n];s&&(r[n]=s)})}var Ce=class extends pt{constructor(){super(...arguments),this.awaitUpdateCallback=new je.AgPromise(e=>{this.resolveUpdateCallback=e}),this.wrapperComponent=$t}init(e){return this.sourceParams=e,super.init(this.getProps())}addMethod(){}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}getFrameworkComponentInstance(){return this}createElement(e,t){return super.createElement(this.wrapperComponent,{initialProps:t,CustomComponentClass:e,setMethods:r=>this.setMethods(r),addUpdateCallback:r=>{this.updateCallback=()=>(r(this.getProps()),new je.AgPromise(n=>{setTimeout(()=>{n()})})),this.resolveUpdateCallback()}})}setMethods(e){this.providedMethods=e,Ye(this.getOptionalMethods(),this.providedMethods,this)}getOptionalMethods(){return[]}getProps(){return{...this.sourceParams,key:this.key,ref:this.ref}}refreshProps(){return this.updateCallback?this.updateCallback():new je.AgPromise(e=>this.awaitUpdateCallback.then(()=>{this.updateCallback().then(()=>e())}))}},Kt=class extends Ce{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},Qt=class extends Ce{constructor(){super(...arguments),this.date=null,this.onDateChange=e=>this.updateDate(e)}getDate(){return this.date}setDate(e){this.date=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached","setInputPlaceholder","setInputAriaLabel","setDisabled"]}updateDate(e){this.setDate(e),this.sourceParams.onDateChanged()}getProps(){const e=super.getProps();return e.date=this.date,e.onDateChange=this.onDateChange,delete e.onDateChanged,e}},Yt=class extends Ce{constructor(){super(...arguments),this.label="",this.icon=null,this.shake=!1}setIcon(e,t){this.icon=e,this.shake=t,this.refreshProps()}setLabel(e){this.label=e,this.refreshProps()}getProps(){const e=super.getProps(),{label:t,icon:r,shake:n}=this;return e.label=t,e.icon=r,e.shake=n,e}},Zt=class extends Ce{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e),this.onUiChange=()=>this.sourceParams.filterChangedCallback(),this.expectingNewMethods=!0,this.hasBeenActive=!1}isFilterActive(){return this.model!=null}doesFilterPass(e){return this.providedMethods.doesFilterPass(e)}getModel(){return this.model}setModel(e){return this.expectingNewMethods=!0,this.model=e,this.hasBeenActive||(this.hasBeenActive=this.isFilterActive()),this.refreshProps()}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}getOptionalMethods(){return["afterGuiAttached","afterGuiDetached","onNewRowsLoaded","getModelAsString","onAnyFilterChanged"]}setMethods(e){this.expectingNewMethods===!1&&this.hasBeenActive&&this.providedMethods?.doesFilterPass!==e?.doesFilterPass&&setTimeout(()=>{this.sourceParams.filterChangedCallback()}),this.expectingNewMethods=!1,super.setMethods(e)}updateModel(e){this.setModel(e).then(()=>this.sourceParams.filterChangedCallback())}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e.onUiChange=this.onUiChange,delete e.filterChangedCallback,delete e.filterModifiedCallback,delete e.valueGetter,e}};function mt(e,t){e.parentFilterInstance(r=>{(r.setModel(t)||Bt.AgPromise.resolve()).then(()=>{e.filterParams.filterChangedCallback()})})}var Xt=class{constructor(e,t){this.floatingFilterParams=e,this.refreshProps=t,this.model=null,this.onModelChange=r=>this.updateModel(r)}getProps(){return{...this.floatingFilterParams,model:this.model,onModelChange:this.onModelChange}}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.floatingFilterParams=e,this.refreshProps()}setMethods(e){Ye(this.getOptionalMethods(),e,this)}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),mt(this.floatingFilterParams,e)}},er=class extends Ce{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e)}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),mt(this.sourceParams,e)}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e}},tr=class extends Ce{refresh(e){this.sourceParams=e,this.refreshProps()}},rr=class extends Ce{constructor(){super(...arguments),this.active=!1,this.expanded=!1,this.onActiveChange=e=>this.updateActive(e)}setActive(e){this.awaitSetActive(e)}setExpanded(e){this.expanded=e,this.refreshProps()}getOptionalMethods(){return["select","configureDefaults"]}awaitSetActive(e){return this.active=e,this.refreshProps()}updateActive(e){const t=this.awaitSetActive(e);e&&t.then(()=>this.sourceParams.onItemActivated())}getProps(){const e=super.getProps();return e.active=this.active,e.expanded=this.expanded,e.onActiveChange=this.onActiveChange,delete e.onItemActivated,e}},sr=class extends Ce{refresh(e){this.sourceParams=e,this.refreshProps()}},nr=class extends Ce{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},or=class extends Ce{constructor(){super(...arguments),this.onStateChange=e=>this.updateState(e)}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}getState(){return this.state}updateState(e){this.state=e,this.refreshProps(),this.sourceParams.onStateUpdated()}getProps(){const e=super.getProps();return e.state=this.state,e.onStateChange=this.onStateChange,e}};function ar(e,t){(e?.getInstance?.()??nt.AgPromise.resolve(void 0)).then(n=>t(n))}function Ue(){(0,nt._warnOnce)("As of v32, using custom components with `reactiveCustomComponents = false` is deprecated.")}var cr=1e3,ir=class{constructor(e,t,r){this.destroyed=!1,this.portals=[],this.hasPendingPortalUpdate=!1,this.wrappingElement=t||"div",this.refresher=e,this.maxComponentCreationTimeMs=r||cr}getPortals(){return this.portals}destroy(){this.destroyed=!0}destroyPortal(e){this.portals=this.portals.filter(t=>t!==e),this.batchUpdate()}getComponentWrappingElement(){return this.wrappingElement}mountReactPortal(e,t,r){this.portals=[...this.portals,e],this.waitForInstance(t,r),this.batchUpdate()}updateReactPortal(e,t){this.portals[this.portals.indexOf(e)]=t,this.batchUpdate()}batchUpdate(){this.hasPendingPortalUpdate||(setTimeout(()=>{this.destroyed||(this.refresher(),this.hasPendingPortalUpdate=!1)}),this.hasPendingPortalUpdate=!0)}waitForInstance(e,t,r=Date.now()){if(this.destroyed){t(null);return}if(e.rendered())t(e);else{if(Date.now()-r>=this.maxComponentCreationTimeMs&&!this.hasPendingPortalUpdate)return;window.setTimeout(()=>{this.waitForInstance(e,t,r)})}}},ur=({ctrl:e})=>{const t=e.isAlive(),{context:r}=(0,te.useContext)(ne),n=t?e.getColId():void 0,[s,d]=(0,te.useState)(),c=(0,te.useRef)(),o=(0,te.useRef)(null),l=(0,te.useRef)(null),a=(0,te.useRef)(null),u=(0,te.useRef)(),g=(0,te.useRef)();t&&!g.current&&(g.current=new Te.CssClassManager(()=>o.current));const w=(0,te.useCallback)(i=>{if(o.current=i,c.current=i?r.createBean(new Te._EmptyBean):r.destroyBean(c.current),!i||!t)return;const C={setWidth:R=>{o.current&&(o.current.style.width=R)},addOrRemoveCssClass:(R,P)=>g.current.addOrRemoveCssClass(R,P),setAriaSort:R=>{o.current&&(R?(0,Te._setAriaSort)(o.current,R):(0,Te._removeAriaSort)(o.current))},setUserCompDetails:R=>d(R),getUserCompInstance:()=>u.current||void 0};e.setComp(C,i,l.current,a.current,c.current);const b=e.getSelectAllGui();l.current?.insertAdjacentElement("afterend",b),c.current.addDestroyFunc(()=>b.remove())},[]);(0,te.useLayoutEffect)(()=>xe(s,r,a.current,u),[s]),(0,te.useEffect)(()=>{e.setDragSource(o.current)},[s]);const v=(0,te.useMemo)(()=>!!(s?.componentFromFramework&&be(s.componentClass)),[s]),m=s&&s.componentFromFramework,f=s&&s.componentClass;return te.default.createElement("div",{ref:w,className:"ag-header-cell","col-id":n,role:"columnheader"},te.default.createElement("div",{ref:l,className:"ag-header-cell-resize",role:"presentation"}),te.default.createElement("div",{ref:a,className:"ag-header-cell-comp-wrapper",role:"presentation"},m&&v&&te.default.createElement(f,{...s.params}),m&&!v&&te.default.createElement(f,{...s.params,ref:u})))},lr=(0,te.memo)(ur),dr=({ctrl:e})=>{const{context:t,gos:r}=(0,B.useContext)(ne),[n,s]=(0,B.useState)(()=>new pe("ag-header-cell","ag-floating-filter")),[d,c]=(0,B.useState)(()=>new pe),[o,l]=(0,B.useState)(()=>new pe("ag-floating-filter-button","ag-hidden")),[a,u]=(0,B.useState)("false"),[g,w]=(0,B.useState)(),[,v]=(0,B.useState)(1),m=(0,B.useRef)(),f=(0,B.useRef)(null),i=(0,B.useRef)(null),C=(0,B.useRef)(null),b=(0,B.useRef)(null),R=(0,B.useRef)(),P=(0,B.useRef)(),E=I=>{I!=null&&R.current&&R.current(I)},q=(0,B.useCallback)(I=>{if(f.current=I,m.current=I?t.createBean(new ot._EmptyBean):t.destroyBean(m.current),!I)return;P.current=new ot.AgPromise(W=>{R.current=W});const D={addOrRemoveCssClass:(W,K)=>s(T=>T.setClass(W,K)),addOrRemoveBodyCssClass:(W,K)=>c(T=>T.setClass(W,K)),setButtonWrapperDisplayed:W=>{l(K=>K.setClass("ag-hidden",!W)),u(W?"false":"true")},setWidth:W=>{f.current&&(f.current.style.width=W)},setCompDetails:W=>w(W),getFloatingFilterComp:()=>P.current?P.current:null,setMenuIcon:W=>b.current?.appendChild(W)};e.setComp(D,I,b.current,i.current,m.current)},[]);(0,B.useLayoutEffect)(()=>xe(g,t,i.current,E),[g]);const M=(0,B.useMemo)(()=>n.toString(),[n]),k=(0,B.useMemo)(()=>d.toString(),[d]),O=(0,B.useMemo)(()=>o.toString(),[o]),p=(0,B.useMemo)(()=>!!(g&&g.componentFromFramework&&be(g.componentClass)),[g]),y=(0,B.useMemo)(()=>r.get("reactiveCustomComponents"),[]),x=(0,B.useMemo)(()=>{if(g)if(y){const I=new Xt(g.params,()=>v(D=>D+1));return E(I),I}else g.componentFromFramework&&Ue()},[g]),oe=x?.getProps(),N=g&&g.componentFromFramework,J=g&&g.componentClass;return B.default.createElement("div",{ref:q,className:M,role:"gridcell"},B.default.createElement("div",{ref:i,className:k,role:"presentation"},N&&!y&&B.default.createElement(J,{...g.params,ref:p?()=>{}:E}),N&&y&&B.default.createElement(Be.Provider,{value:{setMethods:I=>x.setMethods(I)}},B.default.createElement(J,{...oe}))),B.default.createElement("div",{ref:C,"aria-hidden":a,className:O,role:"presentation"},B.default.createElement("button",{ref:b,type:"button",className:"ag-button ag-floating-filter-button-button",tabIndex:-1})))},pr=(0,B.memo)(dr),mr=({ctrl:e})=>{const{context:t}=(0,L.useContext)(ne),[r,n]=(0,L.useState)(()=>new pe),[s,d]=(0,L.useState)(()=>new pe),[c,o]=(0,L.useState)("false"),[l,a]=(0,L.useState)(),[u,g]=(0,L.useState)(),w=(0,L.useMemo)(()=>e.getColId(),[]),v=(0,L.useRef)(),m=(0,L.useRef)(null),f=(0,L.useRef)(null),i=(0,L.useRef)(null),C=(0,L.useRef)(),b=(0,L.useCallback)(k=>{if(m.current=k,v.current=k?t.createBean(new Wt._EmptyBean):t.destroyBean(v.current),!k)return;const O={setWidth:p=>{m.current&&(m.current.style.width=p)},addOrRemoveCssClass:(p,y)=>n(x=>x.setClass(p,y)),setHeaderWrapperHidden:p=>{const y=i.current;y&&(p?y.style.setProperty("display","none"):y.style.removeProperty("display"))},setHeaderWrapperMaxHeight:p=>{const y=i.current;y&&(p!=null?y.style.setProperty("max-height",`${p}px`):y.style.removeProperty("max-height"),y.classList.toggle("ag-header-cell-comp-wrapper-limited-height",p!=null))},setUserCompDetails:p=>g(p),setResizableDisplayed:p=>{d(y=>y.setClass("ag-hidden",!p)),o(p?"false":"true")},setAriaExpanded:p=>a(p),getUserCompInstance:()=>C.current||void 0};e.setComp(O,k,f.current,i.current,v.current)},[]);(0,L.useLayoutEffect)(()=>xe(u,t,i.current),[u]),(0,L.useEffect)(()=>{m.current&&e.setDragSource(m.current)},[u]);const R=(0,L.useMemo)(()=>!!(u?.componentFromFramework&&be(u.componentClass)),[u]),P=(0,L.useMemo)(()=>"ag-header-group-cell "+r.toString(),[r]),E=(0,L.useMemo)(()=>"ag-header-cell-resize "+s.toString(),[s]),q=u&&u.componentFromFramework,M=u&&u.componentClass;return L.default.createElement("div",{ref:b,className:P,"col-id":w,role:"columnheader","aria-expanded":l},L.default.createElement("div",{ref:i,className:"ag-header-cell-comp-wrapper",role:"presentation"},q&&R&&L.default.createElement(M,{...u.params}),q&&!R&&L.default.createElement(M,{...u.params,ref:C})),L.default.createElement("div",{ref:f,"aria-hidden":c,className:E}))},fr=(0,L.memo)(mr),hr=({ctrl:e})=>{const{context:t}=(0,re.useContext)(ne),{topOffset:r,rowHeight:n}=(0,re.useMemo)(()=>e.getTopAndHeight(),[]),s=e.getAriaRowIndex(),d=e.getHeaderRowClass(),[c,o]=(0,re.useState)(()=>n+"px"),[l,a]=(0,re.useState)(()=>r+"px"),u=(0,re.useRef)(null),g=(0,re.useRef)(null),[w,v]=(0,re.useState)(()=>e.getHeaderCtrls()),m=(0,re.useRef)(),f=(0,re.useRef)(null),i=(0,re.useCallback)(R=>{if(f.current=R,m.current=R?t.createBean(new ze._EmptyBean):t.destroyBean(m.current),!R)return;const P={setHeight:E=>o(E),setTop:E=>a(E),setHeaderCtrls:(E,q,M)=>{g.current=u.current,u.current=E;const k=Qe(g.current,E,q);k!==g.current&&Ke(M,()=>v(k))},setWidth:E=>{f.current&&(f.current.style.width=E)}};e.setComp(P,m.current,!1)},[]),C=(0,re.useMemo)(()=>({height:c,top:l}),[c,l]),b=(0,re.useCallback)(R=>{switch(e.getType()){case ze.HeaderRowType.COLUMN_GROUP:return re.default.createElement(fr,{ctrl:R,key:R.instanceId});case ze.HeaderRowType.FLOATING_FILTER:return re.default.createElement(pr,{ctrl:R,key:R.instanceId});default:return re.default.createElement(lr,{ctrl:R,key:R.instanceId})}},[]);return re.default.createElement("div",{ref:i,className:d,role:"row",style:C,"aria-rowindex":s},w.map(b))},Cr=(0,re.memo)(hr),gr=({pinned:e})=>{const[t,r]=(0,ce.useState)(!0),[n,s]=(0,ce.useState)([]),{context:d}=(0,ce.useContext)(ne),c=(0,ce.useRef)(null),o=(0,ce.useRef)(null),l=(0,ce.useRef)(),a=e==="left",u=e==="right",g=!a&&!u,w=(0,ce.useCallback)(f=>{if(c.current=f,l.current=f?d.createBean(new Dt.HeaderRowContainerCtrl(e)):d.destroyBean(l.current),!f)return;const i={setDisplayed:r,setCtrls:C=>s(C),setCenterWidth:C=>{o.current&&(o.current.style.width=C)},setViewportScrollLeft:C=>{c.current&&(c.current.scrollLeft=C)},setPinnedContainerWidth:C=>{c.current&&(c.current.style.width=C,c.current.style.minWidth=C,c.current.style.maxWidth=C)}};l.current.setComp(i,c.current)},[]),v=t?"":"ag-hidden",m=()=>n.map(f=>ce.default.createElement(Cr,{ctrl:f,key:f.instanceId}));return ce.default.createElement(ce.default.Fragment,null,a&&ce.default.createElement("div",{ref:w,className:"ag-pinned-left-header "+v,"aria-hidden":!t,role:"rowgroup"},m()),u&&ce.default.createElement("div",{ref:w,className:"ag-pinned-right-header "+v,"aria-hidden":!t,role:"rowgroup"},m()),g&&ce.default.createElement("div",{ref:w,className:"ag-header-viewport "+v,role:"presentation"},ce.default.createElement("div",{ref:o,className:"ag-header-container",role:"rowgroup"},m())))},Ze=(0,ce.memo)(gr),yr=()=>{const[e,t]=(0,ie.useState)(()=>new pe),[r,n]=(0,ie.useState)(),{context:s}=(0,ie.useContext)(ne),d=(0,ie.useRef)(null),c=(0,ie.useRef)(),o=(0,ie.useCallback)(u=>{if(d.current=u,c.current=u?s.createBean(new It.GridHeaderCtrl):s.destroyBean(c.current),!u)return;const g={addOrRemoveCssClass:(w,v)=>t(m=>m.setClass(w,v)),setHeightAndMinHeight:w=>n(w)};c.current.setComp(g,u,u)},[]),l=(0,ie.useMemo)(()=>"ag-header "+e.toString(),[e]),a=(0,ie.useMemo)(()=>({height:r,minHeight:r}),[r]);return ie.default.createElement("div",{ref:o,className:l,style:a,role:"presentation"},ie.default.createElement(Ze,{pinned:"left"}),ie.default.createElement(Ze,{pinned:null}),ie.default.createElement(Ze,{pinned:"right"}))},vr=(0,ie.memo)(yr),Rr=(e,t)=>{(0,Tt.useEffect)(()=>{const r=t.current;if(r){const n=r.parentElement;if(n){const s=document.createComment(e);return n.insertBefore(s,r),()=>{n.removeChild(s)}}}},[e])},we=Rr,wr=class{constructor(e,t){this.cellEditorParams=e,this.refreshProps=t,this.instanceCreated=new Nt.AgPromise(r=>{this.resolveInstanceCreated=r}),this.onValueChange=r=>this.updateValue(r),this.value=e.value}getProps(){return{...this.cellEditorParams,initialValue:this.cellEditorParams.value,value:this.value,onValueChange:this.onValueChange}}getValue(){return this.value}refresh(e){this.cellEditorParams=e,this.refreshProps()}setMethods(e){Ye(this.getOptionalMethods(),e,this)}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}setRef(e){this.componentInstance=e,this.resolveInstanceCreated?.(),this.resolveInstanceCreated=void 0}getOptionalMethods(){return["isCancelBeforeStart","isCancelAfterEnd","focusIn","focusOut","afterGuiAttached"]}updateValue(e){this.value=e,this.refreshProps()}},Sr=e=>{const t=(0,Se.useRef)(e),r=(0,Se.useRef)(),n=(0,Se.useRef)(!1),s=(0,Se.useRef)(!1),[,d]=(0,Se.useState)(0);n.current&&(s.current=!0),(0,Se.useEffect)(()=>(n.current||(r.current=t.current(),n.current=!0),d(c=>c+1),()=>{s.current&&r.current?.()}),[])},br=e=>{const[t,r]=(0,Ae.useState)(),{context:n,popupService:s,localeService:d,gos:c,editService:o}=(0,Ae.useContext)(ne);return Sr(()=>{const{editDetails:l,cellCtrl:a,eParentCell:u}=e,{compDetails:g}=l,w=c.get("stopEditingWhenCellsLoseFocus"),v=n.createBean(o.createPopupEditorWrapper(g.params)),m=v.getGui();if(e.jsChildComp){const P=e.jsChildComp.getGui();P&&m.appendChild(P)}const f={column:a.getColumn(),rowNode:a.getRowNode(),type:"popupCellEditor",eventSource:u,ePopup:m,position:l.popupPosition,keepWithinBounds:!0},i=s.positionPopupByComponent.bind(s,f),C=d.getLocaleTextFunc(),b=s.addPopup({modal:w,eChild:m,closeOnEsc:!0,closedCallback:()=>{a.onPopupEditorClosed()},anchorToElement:u,positionCallback:i,ariaLabel:C("ariaLabelCellEditor","Cell Editor")}),R=b?b.hideFunc:void 0;return r(v),e.jsChildComp?.afterGuiAttached?.(),()=>{R?.(),n.destroyBean(v)}}),Ae.default.createElement(Ae.default.Fragment,null,t&&e.wrappedContent&&(0,Ht.createPortal)(e.wrappedContent,t.getGui()))},ft=(0,Ae.memo)(br),Pr=(e,t,r,n,s,d)=>{const{context:c}=(0,Ne.useContext)(ne),o=(0,Ne.useCallback)(()=>{const l=s.current;if(!l)return;const a=l.getGui();a&&a.parentElement&&a.parentElement.removeChild(a),c.destroyBean(l),s.current=void 0},[]);(0,Ne.useEffect)(()=>{const l=e!=null,a=e?.compDetails&&!e.compDetails.componentFromFramework,u=t&&r==null;if(!(l&&a&&!u)){o();return}const w=e.compDetails;if(s.current){const m=s.current,i=m.refresh!=null&&e.force==!1?m.refresh(w.params):!1;if(i===!0||i===void 0)return;o()}const v=w.newAgStackInstance();v?.then(m=>{if(!m)return;const f=m.getGui();if(!f)return;(t?r:d.current).appendChild(f),s.current=m})},[e,t,n]),(0,Ne.useEffect)(()=>o,[])},Er=Pr,Mr=(e,t,r)=>{const{compProxy:n}=e;r(n);const s=n.getProps(),d=be(t);return h.default.createElement(Be.Provider,{value:{setMethods:c=>n.setMethods(c)}},d?h.default.createElement(t,{...s}):h.default.createElement(t,{...s,ref:c=>n.setRef(c)}))},ht=(e,t,r)=>{const n=e.compProxy;return h.default.createElement(h.default.Fragment,null,n?Mr(e,t,r):h.default.createElement(t,{...e.compDetails.params,ref:r}))},_r=(e,t,r,n,s)=>{const d=e.compDetails,c=d.componentClass,o=d.componentFromFramework&&!e.popup,l=d.componentFromFramework&&e.popup,a=!d.componentFromFramework&&e.popup;return h.default.createElement(h.default.Fragment,null,o&&ht(e,c,t),l&&h.default.createElement(ft,{editDetails:e,cellCtrl:n,eParentCell:r,wrappedContent:ht(e,c,t)}),a&&s&&h.default.createElement(ft,{editDetails:e,cellCtrl:n,eParentCell:r,jsChildComp:s}))},kr=(e,t,r,n,s,d,c)=>{const{compDetails:o,value:l}=e,a=!o,u=o&&o.componentFromFramework,g=o&&o.componentClass,w=l?.toString?l.toString():l,v=()=>h.default.createElement(h.default.Fragment,null,a&&h.default.createElement(h.default.Fragment,null,w),u&&!d&&h.default.createElement(g,{...o.params,key:t,ref:n}),u&&d&&h.default.createElement(g,{...o.params,key:t}));return h.default.createElement(h.default.Fragment,null,s?h.default.createElement("span",{role:"presentation",id:`cell-${r}`,className:"ag-cell-value",ref:c},v()):v())},Fr=({cellCtrl:e,printLayout:t,editingRow:r})=>{const{context:n}=(0,h.useContext)(ne),{colIdSanitised:s,instanceId:d}=e,c=(0,h.useRef)(),[o,l]=(0,h.useState)(()=>e.isCellRenderer()?void 0:{compDetails:void 0,value:e.getValueToDisplay(),force:!1}),[a,u]=(0,h.useState)(),[g,w]=(0,h.useState)(1),[v,m]=(0,h.useState)(),[f,i]=(0,h.useState)(!1),[C,b]=(0,h.useState)(!1),[R,P]=(0,h.useState)(!1),[E,q]=(0,h.useState)(),M=(0,h.useMemo)(()=>e.isForceWrapper(),[e]),k=(0,h.useMemo)(()=>e.getCellAriaRole(),[e]),O=(0,h.useRef)(null),p=(0,h.useRef)(null),y=(0,h.useRef)(),x=(0,h.useRef)(),oe=(0,h.useRef)(),N=(0,h.useRef)([]),J=(0,h.useRef)(),[I,D]=(0,h.useState)(0),W=(0,h.useCallback)(z=>{J.current=z,D(se=>se+1)},[]),K=o!=null&&(f||R||C),T=M||K,j=(0,h.useCallback)(z=>{if(x.current=z,z){const se=z.isCancelBeforeStart&&z.isCancelBeforeStart();setTimeout(()=>{se?(e.stopEditing(!0),e.focusCell(!0)):e.cellEditorAttached()})}},[e]),V=(0,h.useRef)();V.current||(V.current=new $e.CssClassManager(()=>O.current)),Er(o,T,J.current,I,y,O);const me=(0,h.useRef)();(0,h.useLayoutEffect)(()=>{const z=me.current,se=o;if(me.current=o,z==null||z.compDetails==null||se==null||se.compDetails==null)return;const ae=z.compDetails,U=se.compDetails;if(ae.componentClass!=U.componentClass||p.current?.refresh==null)return;p.current.refresh(U.params)!=!0&&w(le=>le+1)},[o]),(0,h.useLayoutEffect)(()=>{if(!(a&&!a.compDetails.componentFromFramework))return;const se=a.compDetails,ae=a.popup===!0,U=se.newAgStackInstance();return U.then(ee=>{if(!ee)return;const le=ee.getGui();j(ee),ae||((M?oe:O).current?.appendChild(le),ee.afterGuiAttached&&ee.afterGuiAttached()),q(ee)}),()=>{U.then(ee=>{const le=ee.getGui();n.destroyBean(ee),j(void 0),q(void 0),le?.parentElement?.removeChild(le)})}},[a]);const _=(0,h.useCallback)(z=>{if(oe.current=z,!z){N.current.forEach(ae=>ae()),N.current=[];return}const se=ae=>{if(ae){const U=ae.getGui();z.insertAdjacentElement("afterbegin",U),N.current.push(()=>{n.destroyBean(ae),(0,$e._removeFromParent)(U)})}return ae};if(f){const ae=e.createSelectionCheckbox();se(ae)}R&&se(e.createDndSource()),C&&se(e.createRowDragComp())},[e,n,R,C,f]),X=(0,h.useCallback)(z=>{if(O.current=z,c.current=z?n.createBean(new $e._EmptyBean):n.destroyBean(c.current),!z||!e)return;const se={addOrRemoveCssClass:(U,ee)=>V.current.addOrRemoveCssClass(U,ee),setUserStyles:U=>m(U),getFocusableElement:()=>O.current,setIncludeSelection:U=>i(U),setIncludeRowDrag:U=>b(U),setIncludeDndSource:U=>P(U),getCellEditor:()=>x.current||null,getCellRenderer:()=>p.current??y.current,getParentOfValue:()=>J.current??oe.current??O.current,setRenderDetails:(U,ee,le)=>{l(ve=>ve?.compDetails!==U||ve?.value!==ee||ve?.force!==le?{value:ee,compDetails:U,force:le}:ve)},setEditDetails:(U,ee,le,ve)=>{if(U){let Ee;ve?Ee=new wr(U.params,()=>w(Xe=>Xe+1)):U.componentFromFramework&&Ue(),u({compDetails:U,popup:ee,popupPosition:le,compProxy:Ee}),ee||l(void 0)}else u(Ee=>{Ee?.compProxy&&(x.current=void 0)})}},ae=oe.current||void 0;e.setComp(se,z,ae,t,r,c.current)},[]),Q=(0,h.useMemo)(()=>!!(o?.compDetails?.componentFromFramework&&be(o.compDetails.componentClass)),[o]);(0,h.useLayoutEffect)(()=>{O.current&&(V.current.addOrRemoveCssClass("ag-cell-value",!T),V.current.addOrRemoveCssClass("ag-cell-inline-editing",!!a&&!a.popup),V.current.addOrRemoveCssClass("ag-cell-popup-editing",!!a&&!!a.popup),V.current.addOrRemoveCssClass("ag-cell-not-inline-editing",!a||!!a.popup),e.getRowCtrl()?.setInlineEditingCss(),e.shouldRestoreFocus()&&!e.isEditing()&&O.current.focus({preventScroll:!0}))});const ge=()=>h.default.createElement(h.default.Fragment,null,o!=null&&kr(o,g,d,p,T,Q,W),a!=null&&_r(a,j,O.current,e,E)),Pe=(0,h.useCallback)(()=>e.onFocusOut(),[]);return h.default.createElement("div",{ref:X,style:v,role:k,"col-id":s,onBlur:Pe},T?h.default.createElement("div",{className:"ag-cell-wrapper",role:"presentation",ref:_},ge()):ge())},Gr=(0,h.memo)(Fr),Ar=({rowCtrl:e,containerType:t})=>{const{context:r,gos:n}=(0,G.useContext)(ne),s=(0,G.useRef)(),d=(0,G.useRef)(e.getDomOrder()),c=e.isFullWidth(),o=e.getRowNode().displayed,[l,a]=(0,G.useState)(()=>o?e.getRowIndex():null),[u,g]=(0,G.useState)(()=>e.getRowId()),[w,v]=(0,G.useState)(()=>e.getBusinessKey()),[m,f]=(0,G.useState)(()=>e.getRowStyles()),i=(0,G.useRef)(null),C=(0,G.useRef)(null),[b,R]=(0,G.useState)(()=>null),[P,E]=(0,G.useState)(),[q,M]=(0,G.useState)(()=>o?e.getInitialRowTop(t):void 0),[k,O]=(0,G.useState)(()=>o?e.getInitialTransform(t):void 0),p=(0,G.useRef)(null),y=(0,G.useRef)(),x=(0,G.useRef)(!1),[oe,N]=(0,G.useState)(0);(0,G.useEffect)(()=>{if(x.current||!P||oe>10)return;const _=p.current?.firstChild;_?(e.setupDetailRowAutoHeight(_),x.current=!0):N(X=>X+1)},[P,oe]);const J=(0,G.useRef)();J.current||(J.current=new ct.CssClassManager(()=>p.current));const I=(0,G.useCallback)(_=>{if(p.current=_,s.current=_?r.createBean(new ct._EmptyBean):r.destroyBean(s.current),!_){e.unsetComp(t);return}if(!e.isAlive())return;const X={setTop:M,setTransform:O,addOrRemoveCssClass:(Q,ge)=>J.current.addOrRemoveCssClass(Q,ge),setDomOrder:Q=>d.current=Q,setRowIndex:a,setRowId:g,setRowBusinessKey:v,setUserStyles:f,setCellCtrls:(Q,ge)=>{C.current=i.current,i.current=Q;const Pe=Qe(C.current,Q,d.current);Pe!==C.current&&Ke(ge,()=>R(Pe))},showFullWidth:Q=>E(Q),getFullWidthCellRenderer:()=>y.current,refreshFullWidth:Q=>j.current?(E(ge=>({...ge,params:Q()})),!0):!y.current||!y.current.refresh?!1:y.current.refresh(Q())};e.setComp(X,_,t,s.current)},[]);(0,G.useLayoutEffect)(()=>xe(P,r,p.current,y),[P]);const D=(0,G.useMemo)(()=>{const _={top:q,transform:k};return Object.assign(_,m),_},[q,k,m]),W=c&&P?.componentFromFramework,K=!c&&b!=null,T=(0,G.useMemo)(()=>!!(P?.componentFromFramework&&be(P.componentClass)),[P]),j=(0,G.useRef)(!1);(0,G.useEffect)(()=>{j.current=T&&!!P&&!!n.get("reactiveCustomComponents")},[T,P]);const V=()=>b?.map(_=>G.default.createElement(Gr,{cellCtrl:_,editingRow:e.isEditing(),printLayout:e.isPrintLayout(),key:_.instanceId})),me=()=>{const _=P.componentClass;return G.default.createElement(G.default.Fragment,null,T?G.default.createElement(_,{...P.params}):G.default.createElement(_,{...P.params,ref:y}))};return G.default.createElement("div",{ref:I,role:"row",style:D,"row-index":l,"row-id":u,"row-business-key":w},K&&V(),W&&me())},xr=(0,G.memo)(Ar),Br=({name:e})=>{const{context:t}=(0,$.useContext)(ne),r=(0,$.useMemo)(()=>(0,at._getRowContainerOptions)(e),[e]),n=(0,$.useRef)(null),s=(0,$.useRef)(null),d=(0,$.useRef)([]),c=(0,$.useRef)([]),[o,l]=(0,$.useState)(()=>[]),a=(0,$.useRef)(!1),u=(0,$.useRef)(),g=(0,$.useMemo)(()=>fe(r.viewport),[r]),w=(0,$.useMemo)(()=>fe(r.container),[r]),v=r.type==="center",m=v?n:s;we(" AG Row Container "+e+" ",m);const f=(0,$.useCallback)(()=>v?n.current!=null&&s.current!=null:s.current!=null,[]),i=(0,$.useCallback)(()=>v?n.current==null&&s.current==null:s.current==null,[]),C=(0,$.useCallback)(()=>{if(i()&&(u.current=t.destroyBean(u.current)),f()){const E=M=>{const k=Qe(c.current,d.current,a.current);k!==c.current&&(c.current=k,Ke(M,()=>l(k)))},q={setHorizontalScroll:M=>{n.current&&(n.current.scrollLeft=M)},setViewportHeight:M=>{n.current&&(n.current.style.height=M)},setRowCtrls:({rowCtrls:M,useFlushSync:k})=>{const O=!!k&&d.current.length>0&&M.length>0;d.current=M,E(O)},setDomOrder:M=>{a.current!=M&&(a.current=M,E(!1))},setContainerWidth:M=>{s.current&&(s.current.style.width=M)},setOffsetTop:M=>{s.current&&(s.current.style.transform=`translateY(${M})`)}};u.current=t.createBean(new at.RowContainerCtrl(e)),u.current.setComp(q,s.current,n.current)}},[f,i]),b=(0,$.useCallback)(E=>{s.current=E,C()},[C]),R=(0,$.useCallback)(E=>{n.current=E,C()},[C]),P=()=>$.default.createElement("div",{className:w,ref:b,role:"rowgroup"},o.map(E=>$.default.createElement(xr,{rowCtrl:E,containerType:r.type,key:E.instanceId})));return $.default.createElement($.default.Fragment,null,v?$.default.createElement("div",{className:g,ref:R,role:"presentation"},P()):P())},Or=(0,$.memo)(Br),Ir=()=>{const{context:e,resizeObserverService:t}=(0,S.useContext)(ne),[r,n]=(0,S.useState)(""),[s,d]=(0,S.useState)(0),[c,o]=(0,S.useState)(0),[l,a]=(0,S.useState)("0px"),[u,g]=(0,S.useState)("0px"),[w,v]=(0,S.useState)("100%"),[m,f]=(0,S.useState)("0px"),[i,C]=(0,S.useState)("0px"),[b,R]=(0,S.useState)("100%"),[P,E]=(0,S.useState)(""),[q,M]=(0,S.useState)(""),[k,O]=(0,S.useState)(null),[p,y]=(0,S.useState)(""),[x,oe]=(0,S.useState)(null),[N,J]=(0,S.useState)("ag-layout-normal"),I=(0,S.useRef)();I.current||(I.current=new Re.CssClassManager(()=>D.current));const D=(0,S.useRef)(null),W=(0,S.useRef)(null),K=(0,S.useRef)(null),T=(0,S.useRef)(null),j=(0,S.useRef)(null),V=(0,S.useRef)(null),me=(0,S.useRef)(null),_=(0,S.useRef)([]),X=(0,S.useRef)([]);we(" AG Grid Body ",D),we(" AG Pinned Top ",W),we(" AG Sticky Top ",K),we(" AG Middle ",V),we(" AG Pinned Bottom ",me);const Q=(0,S.useCallback)(ye=>{if(D.current=ye,!ye){_.current=e.destroyBeans(_.current),X.current.forEach(Y=>Y()),X.current=[];return}if(!e)return;const Le=(Y,de)=>{Y.appendChild(de),X.current.push(()=>Y.removeChild(de))},et=Y=>{const de=e.createBean(new Y);return _.current.push(de),de},De=(Y,de,rs)=>{Le(Y,document.createComment(rs)),Le(Y,et(de).getGui())};De(ye,Re.FakeHScrollComp," AG Fake Horizontal Scroll "),De(ye,Re.OverlayWrapperComponent," AG Overlay Wrapper "),j.current&&De(j.current,Re.FakeVScrollComp," AG Fake Vertical Scroll ");const ts={setRowAnimationCssOnBodyViewport:n,setColumnCount:Y=>{D.current&&(0,Re._setAriaColCount)(D.current,Y)},setRowCount:Y=>{D.current&&(0,Re._setAriaRowCount)(D.current,Y)},setTopHeight:d,setBottomHeight:o,setStickyTopHeight:a,setStickyTopTop:g,setStickyTopWidth:v,setTopDisplay:E,setBottomDisplay:M,setColumnMovingCss:(Y,de)=>I.current.addOrRemoveCssClass(Y,de),updateLayoutClasses:J,setAlwaysVerticalScrollClass:O,setPinnedTopBottomOverflowY:y,setCellSelectableCss:(Y,de)=>oe(de?Y:null),setBodyViewportWidth:Y=>{V.current&&(V.current.style.width=Y)},registerBodyViewportResizeListener:Y=>{if(V.current){const de=t.observeResize(V.current,Y);X.current.push(()=>de())}},setStickyBottomHeight:f,setStickyBottomBottom:C,setStickyBottomWidth:R},gt=e.createBean(new Re.GridBodyCtrl);_.current.push(gt),gt.setComp(ts,ye,V.current,W.current,me.current,K.current,T.current)},[]),ge=(0,S.useMemo)(()=>fe("ag-root","ag-unselectable",N),[N]),Pe=(0,S.useMemo)(()=>fe("ag-body-viewport",r,N,k,x),[r,N,k,x]),z=(0,S.useMemo)(()=>fe("ag-body",N),[N]),se=(0,S.useMemo)(()=>fe("ag-floating-top",x),[x]),ae=(0,S.useMemo)(()=>fe("ag-sticky-top",x),[x]),U=(0,S.useMemo)(()=>fe("ag-sticky-bottom",m==="0px"?"ag-hidden":null,x),[x,m]),ee=(0,S.useMemo)(()=>fe("ag-floating-bottom",x),[x]),le=(0,S.useMemo)(()=>({height:s,minHeight:s,display:P,overflowY:p}),[s,P,p]),ve=(0,S.useMemo)(()=>({height:l,top:u,width:w}),[l,u,w]),Ee=(0,S.useMemo)(()=>({height:m,bottom:i,width:b}),[m,i,b]),Xe=(0,S.useMemo)(()=>({height:c,minHeight:c,display:q,overflowY:p}),[c,q,p]),es=ye=>S.default.createElement(Or,{name:ye,key:`${ye}-container`}),Ie=({section:ye,children:Le,className:et,style:De})=>S.default.createElement("div",{ref:ye,className:et,role:"presentation",style:De},Le.map(es));return S.default.createElement("div",{ref:Q,className:ge,role:"treegrid"},S.default.createElement(vr,null),Ie({section:W,className:se,style:le,children:["topLeft","topCenter","topRight","topFullWidth"]}),S.default.createElement("div",{className:z,ref:j,role:"presentation"},Ie({section:V,className:Pe,children:["left","center","right","fullWidth"]})),Ie({section:K,className:ae,style:ve,children:["stickyTopLeft","stickyTopCenter","stickyTopRight","stickyTopFullWidth"]}),Ie({section:T,className:U,style:Ee,children:["stickyBottomLeft","stickyBottomCenter","stickyBottomRight","stickyBottomFullWidth"]}),Ie({section:me,className:ee,style:Xe,children:["bottomLeft","bottomCenter","bottomRight","bottomFullWidth"]}))},Dr=(0,S.memo)(Ir),Wr=(e,t)=>{const{children:r,eFocusableElement:n,onTabKeyDown:s,gridCtrl:d,forceFocusOutWhenTabGuardsAreEmpty:c}=e,{context:o}=(0,ue.useContext)(ne),l=(0,ue.useRef)(null),a=(0,ue.useRef)(null),u=(0,ue.useRef)(),g=i=>{const C=i==null?void 0:parseInt(i,10).toString();[l,a].forEach(b=>{C===void 0?b.current?.removeAttribute("tabindex"):b.current?.setAttribute("tabindex",C)})};(0,ue.useImperativeHandle)(t,()=>({forceFocusOutOfContainer(i){u.current?.forceFocusOutOfContainer(i)}}));const w=(0,ue.useCallback)(()=>{const i=l.current,C=a.current;if(!i&&!C){u.current=o.destroyBean(u.current);return}if(i&&C){const b={setTabIndex:g};u.current=o.createBean(new He.TabGuardCtrl({comp:b,eTopGuard:i,eBottomGuard:C,eFocusableElement:n,onTabKeyDown:s,forceFocusOutWhenTabGuardsAreEmpty:c,focusInnerElement:R=>d.focusInnerElement(R)}))}},[]),v=(0,ue.useCallback)(i=>{l.current=i,w()},[w]),m=(0,ue.useCallback)(i=>{a.current=i,w()},[w]),f=i=>{const C=i==="top"?He.TabGuardClassNames.TAB_GUARD_TOP:He.TabGuardClassNames.TAB_GUARD_BOTTOM;return ue.default.createElement("div",{className:`${He.TabGuardClassNames.TAB_GUARD} ${C}`,role:"presentation",ref:i==="top"?v:m})};return ue.default.createElement(ue.default.Fragment,null,f("top"),r,f("bottom"))},Tr=(0,ue.forwardRef)(Wr),Nr=(0,ue.memo)(Tr),Hr=({context:e})=>{const[t,r]=(0,H.useState)(""),[n,s]=(0,H.useState)(""),[d,c]=(0,H.useState)(""),[o,l]=(0,H.useState)(null),[a,u]=(0,H.useState)(null),[g,w]=(0,H.useState)(!1),[v,m]=(0,H.useState)(),f=(0,H.useRef)(),i=(0,H.useRef)(null),C=(0,H.useRef)(),[b,R]=(0,H.useState)(null),P=(0,H.useRef)(()=>{}),E=(0,H.useRef)(),q=(0,H.useRef)([]),M=(0,H.useCallback)(()=>{},[]),k=(0,H.useMemo)(()=>e.isDestroyed()?null:e.getBeans(),[e]);we(" AG Grid ",i);const O=(0,H.useCallback)(N=>{if(i.current=N,f.current=N?e.createBean(new Ot.GridCtrl):e.destroyBean(f.current),!N||e.isDestroyed())return;const J=f.current;P.current=J.focusInnerElement.bind(J);const I={destroyGridUi:()=>{},setRtlClass:r,setGridThemeClass:s,forceFocusOutOfContainer:D=>{if(!D&&E.current?.isDisplayed()){E.current.forceFocusOutOfContainer(D);return}C.current?.forceFocusOutOfContainer(D)},updateLayoutClasses:c,getFocusableContainers:()=>{const D=[],W=i.current?.querySelector(".ag-root");return W&&D.push({getGui:()=>W}),q.current.forEach(K=>{K.isDisplayed()&&D.push(K)}),D},setCursor:l,setUserSelect:u};J.setComp(I,N,N),w(!0)},[]);(0,H.useEffect)(()=>{const N=f.current,J=i.current;if(!v||!k||!N||!b||!J)return;const I=[],{watermarkSelector:D,paginationSelector:W,sideBarSelector:K,statusBarSelector:T,gridHeaderDropZonesSelector:j}=N.getOptionalSelectors(),V=[];if(j){const _=e.createBean(new j.component),X=_.getGui();J.insertAdjacentElement("afterbegin",X),V.push(X),I.push(_)}if(K){const _=e.createBean(new K.component),X=_.getGui(),Q=b.querySelector(".ag-tab-guard-bottom");Q&&(Q.insertAdjacentElement("beforebegin",X),V.push(X)),I.push(_),q.current.push(_)}const me=_=>{const X=e.createBean(new _),Q=X.getGui();return J.insertAdjacentElement("beforeend",Q),V.push(Q),I.push(X),X};if(T&&me(T.component),W){const _=me(W.component);E.current=_,q.current.push(_)}return D&&me(D.component),()=>{e.destroyBeans(I),V.forEach(_=>{_.parentElement?.removeChild(_)})}},[v,b,k]);const p=(0,H.useMemo)(()=>fe("ag-root-wrapper",t,n,d),[t,n,d]),y=(0,H.useMemo)(()=>fe("ag-root-wrapper-body","ag-focus-managed",d),[d]),x=(0,H.useMemo)(()=>({userSelect:a??"",WebkitUserSelect:a??"",cursor:o??""}),[a,o]),oe=(0,H.useCallback)(N=>{C.current=N,m(N!==null)},[]);return H.default.createElement("div",{ref:O,className:p,style:x,role:"presentation"},H.default.createElement("div",{className:y,ref:R,role:"presentation"},g&&b&&k&&H.default.createElement(ne.Provider,{value:k},H.default.createElement(Nr,{ref:oe,eFocusableElement:b,onTabKeyDown:M,gridCtrl:f.current,forceFocusOutWhenTabGuardsAreEmpty:!0},H.default.createElement(Dr,null)))))},qr=(0,H.memo)(Hr),Ur=class extends qt.BeanStub{wireBeans(e){this.ctrlsService=e.ctrlsService}areHeaderCellsRendered(){return this.ctrlsService.getHeaderRowContainerCtrls().every(e=>e.getAllCtrls().every(t=>t.areCellsRendered()))}},Ct=e=>{const t=(0,F.useRef)(),r=(0,F.useRef)(null),n=(0,F.useRef)(null),s=(0,F.useRef)([]),d=(0,F.useRef)([]),c=(0,F.useRef)(e),o=(0,F.useRef)(),l=(0,F.useRef)(),a=(0,F.useRef)(!1),[u,g]=(0,F.useState)(void 0),[,w]=(0,F.useState)(0),v=(0,F.useCallback)(i=>{if(r.current=i,!i){s.current.forEach(p=>p()),s.current.length=0;return}const C=e.modules||[];n.current||(n.current=new ir(()=>w(p=>p+1),e.componentWrappingElement,e.maxComponentCreationTimeMs),s.current.push(()=>{n.current?.destroy(),n.current=null}));const b=(0,he._combineAttributesAndGridOptions)(e.gridOptions,e),R=()=>{if(a.current){const p=()=>o.current?.shouldQueueUpdates()?void 0:d.current.shift();let y=p();for(;y;)y(),y=p()}},P=new zr(R);o.current=P;const E=new Ur,q={providedBeanInstances:{frameworkComponentWrapper:new Vr(n.current,b.reactiveCustomComponents??(0,he._getGlobalGridOption)("reactiveCustomComponents")??!0),renderStatusService:E},modules:C,frameworkOverrides:P},M=p=>{g(p),p.createBean(E),s.current.push(()=>{p.destroy()}),p.getBean("ctrlsService").whenReady({addDestroyFunc:y=>{s.current.push(y)}},()=>{if(p.isDestroyed())return;const y=t.current;y&&e.setGridApi?.(y)})},k=p=>{p.getBean("ctrlsService").whenReady({addDestroyFunc:y=>{s.current.push(y)}},()=>{d.current.forEach(y=>y()),d.current.length=0,a.current=!0})},O=new he.GridCoreCreator;b.gridId??(b.gridId=l.current),t.current=O.create(i,b,M,k,q),s.current.push(()=>{t.current=void 0}),t.current&&(l.current=t.current.getGridId())},[]),m=(0,F.useMemo)(()=>({height:"100%",...e.containerStyle||{}}),[e.containerStyle]),f=(0,F.useCallback)(i=>{a.current&&!o.current?.shouldQueueUpdates()?i():d.current.push(i)},[]);return(0,F.useEffect)(()=>{const i=Lr(c.current,e);c.current=e,f(()=>{t.current&&(0,he._processOnChange)(i,t.current)})},[e]),F.default.createElement("div",{style:m,className:e.className,ref:v},u&&!u.isDestroyed()?F.default.createElement(qr,{context:u}):null,n.current?.getPortals()??null)};function Lr(e,t){const r={};return Object.keys(t).forEach(n=>{const s=t[n];e[n]!==s&&(r[n]=s)}),r}var Vr=class extends he.BaseComponentWrapper{constructor(e,t){super(),this.parent=e,this.reactiveCustomComponents=t}createWrapper(e,t){if(this.reactiveCustomComponents){const s=(d=>{switch(d){case"filter":return Zt;case"floatingFilterComponent":return er;case"dateComponent":return Qt;case"dragAndDropImageComponent":return Yt;case"loadingOverlayComponent":return tr;case"noRowsOverlayComponent":return sr;case"statusPanel":return nr;case"toolPanel":return or;case"menuItem":return rr;case"cellRenderer":return Kt}})(t.propertyName);if(s)return new s(e,this.parent,t)}else switch(t.propertyName){case"filter":case"floatingFilterComponent":case"dateComponent":case"dragAndDropImageComponent":case"loadingOverlayComponent":case"noRowsOverlayComponent":case"statusPanel":case"toolPanel":case"menuItem":case"cellRenderer":Ue();break}const r=!t.cellRenderer&&t.propertyName!=="toolPanel";return new pt(e,this.parent,t,r)}},jr=(0,F.forwardRef)((e,t)=>{const{ctrlsFactory:r,context:n,gos:s,resizeObserverService:d,rowModel:c}=(0,F.useContext)(ne),[o,l]=(0,F.useState)(()=>new pe),[a,u]=(0,F.useState)(()=>new pe),[g,w]=(0,F.useState)(),[v,m]=(0,F.useState)(),f=(0,F.useRef)(),i=(0,F.useRef)(null),C=(0,F.useRef)(),b=(0,F.useMemo)(()=>he.ModuleRegistry.__getGridRegisteredModules(e.api.getGridId()),[e]),R=(0,F.useMemo)(()=>o.toString()+" ag-details-row",[o]),P=(0,F.useMemo)(()=>a.toString()+" ag-details-grid",[a]);t&&(0,F.useImperativeHandle)(t,()=>({refresh(){return f.current?.refresh()??!1}})),e.template&&(0,he._warnOnce)("detailCellRendererParams.template is not supported by AG Grid React. To change the template, provide a Custom Detail Cell Renderer. See https://ag-grid.com/react-data-grid/master-detail-custom-detail/");const E=(0,F.useCallback)(M=>{if(i.current=M,!M){f.current=n.destroyBean(f.current),C.current?.();return}const k={addOrRemoveCssClass:(p,y)=>l(x=>x.setClass(p,y)),addOrRemoveDetailGridCssClass:(p,y)=>u(x=>x.setClass(p,y)),setDetailGrid:p=>w(p),setRowData:p=>m(p),getGui:()=>i.current},O=r.getInstance("detailCellRenderer");if(O&&(n.createBean(O),O.init(k,e),f.current=O,s.get("detailRowAutoHeight"))){const p=()=>{if(i.current==null)return;const y=i.current.clientHeight;y!=null&&y>0&&setTimeout(()=>{e.node.setRowHeight(y),((0,he._isClientSideRowModel)(s)||(0,he._isServerSideRowModel)(s))&&c.onRowHeightChanged()},0)};C.current=d.observeResize(M,p),p()}},[]),q=(0,F.useCallback)(M=>{f.current?.registerDetailWithMaster(M)},[]);return F.default.createElement("div",{className:R,ref:E},g&&F.default.createElement(Ct,{className:P,...g,modules:b,rowData:v,setGridApi:q}))}),zr=class extends he.VanillaFrameworkOverrides{constructor(e){super("react"),this.processQueuedUpdates=e,this.queueUpdates=!1,this.frameworkComponents={agGroupCellRenderer:lt,agGroupRowRenderer:lt,agDetailCellRenderer:jr},this.wrapIncoming=(t,r)=>r==="ensureVisible"?jt(t):t(),this.renderingEngine="react"}frameworkComponent(e){return this.frameworkComponents[e]}isFrameworkComponent(e){if(!e)return!1;const t=e.prototype;return!(t&&"getGui"in t)}getLockOnRefresh(){this.queueUpdates=!0}releaseLockOnRefresh(){this.queueUpdates=!1,this.processQueuedUpdates()}shouldQueueUpdates(){return this.queueUpdates}runWhenReadyAsync(){return Vt()}},$r=class extends st.Component{constructor(){super(...arguments),this.apiListeners=[],this.setGridApi=e=>{this.api=e,this.apiListeners.forEach(t=>t(e))}}registerApiListener(e){this.apiListeners.push(e)}componentWillUnmount(){this.apiListeners.length=0}render(){return st.default.createElement(Ct,{...this.props,setGridApi:this.setGridApi})}};function Oe(e){const{setMethods:t}=(0,Ut.useContext)(Be);t(e)}function Jr(e){Oe(e)}function Kr(e){return Oe(e)}function Qr(e){return Oe(e)}function Yr(e){Oe(e)}function Zr(e){Oe(e)}if(typeof ke.exports=="object"&&typeof Ve=="object"){var Xr=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of Object.getOwnPropertyNames(t))!Object.prototype.hasOwnProperty.call(e,s)&&s!==r&&Object.defineProperty(e,s,{get:()=>t[s],enumerable:!(n=Object.getOwnPropertyDescriptor(t,s))||n.enumerable});return e};ke.exports=Xr(ke.exports,Ve)}return ke.exports});
