body {
  --ag-legacy-styles-loaded: "true";
}

.ag-icon {
  font-family: var(--ag-icon-font-family);
  font-weight: var(--ag-icon-font-weight);
  color: var(--ag-icon-font-color);
  font-size: var(--ag-icon-size);
  line-height: var(--ag-icon-size);
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  position: relative;
}
.ag-icon::before {
  content: "";
  font-family: inherit;
}
.ag-icon::after {
  background: transparent var(--ag-icon-image, none) center/contain no-repeat;
  display: var(--ag-icon-image-display);
  opacity: var(--ag-icon-image-opacity, 0.9);
  position: absolute;
  inset: 0;
  content: "";
}

.ag-icon-aggregation {
  font-family: var(--ag-icon-font-family-aggregation, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-aggregation, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-aggregation, var(--ag-icon-font-color));
}

.ag-icon-aggregation::before {
  content: var(--ag-icon-font-code-aggregation, "\f101");
  display: var(--ag-icon-font-display-aggregation, var(--ag-icon-font-display));
}

.ag-icon-aggregation::after {
  background-image: var(--ag-icon-image-aggregation, var(--ag-icon-image));
  display: var(--ag-icon-image-display-aggregation, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-aggregation, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-arrows {
  font-family: var(--ag-icon-font-family-arrows, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-arrows, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-arrows, var(--ag-icon-font-color));
}

.ag-icon-arrows::before {
  content: var(--ag-icon-font-code-arrows, "\f102");
  display: var(--ag-icon-font-display-arrows, var(--ag-icon-font-display));
}

.ag-icon-arrows::after {
  background-image: var(--ag-icon-image-arrows, var(--ag-icon-image));
  display: var(--ag-icon-image-display-arrows, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-arrows, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-asc {
  font-family: var(--ag-icon-font-family-asc, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-asc, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-asc, var(--ag-icon-font-color));
}

.ag-icon-asc::before {
  content: var(--ag-icon-font-code-asc, "\f103");
  display: var(--ag-icon-font-display-asc, var(--ag-icon-font-display));
}

.ag-icon-asc::after {
  background-image: var(--ag-icon-image-asc, var(--ag-icon-image));
  display: var(--ag-icon-image-display-asc, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-asc, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-cancel {
  font-family: var(--ag-icon-font-family-cancel, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-cancel, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-cancel, var(--ag-icon-font-color));
}

.ag-icon-cancel::before {
  content: var(--ag-icon-font-code-cancel, "\f104");
  display: var(--ag-icon-font-display-cancel, var(--ag-icon-font-display));
}

.ag-icon-cancel::after {
  background-image: var(--ag-icon-image-cancel, var(--ag-icon-image));
  display: var(--ag-icon-image-display-cancel, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-cancel, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-chart {
  font-family: var(--ag-icon-font-family-chart, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-chart, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-chart, var(--ag-icon-font-color));
}

.ag-icon-chart::before {
  content: var(--ag-icon-font-code-chart, "\f105");
  display: var(--ag-icon-font-display-chart, var(--ag-icon-font-display));
}

.ag-icon-chart::after {
  background-image: var(--ag-icon-image-chart, var(--ag-icon-image));
  display: var(--ag-icon-image-display-chart, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-chart, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-checkbox-checked {
  font-family: var(--ag-icon-font-family-checkbox-checked, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-checkbox-checked, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-checkbox-checked, var(--ag-icon-font-color));
}

.ag-icon-checkbox-checked::before {
  content: var(--ag-icon-font-code-checkbox-checked, "\f106");
  display: var(--ag-icon-font-display-checkbox-checked, var(--ag-icon-font-display));
}

.ag-icon-checkbox-checked::after {
  background-image: var(--ag-icon-image-checkbox-checked, var(--ag-icon-image));
  display: var(--ag-icon-image-display-checkbox-checked, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-checkbox-checked, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-checkbox-indeterminate {
  font-family: var(--ag-icon-font-family-checkbox-indeterminate, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-checkbox-indeterminate, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-checkbox-indeterminate, var(--ag-icon-font-color));
}

.ag-icon-checkbox-indeterminate::before {
  content: var(--ag-icon-font-code-checkbox-indeterminate, "\f107");
  display: var(--ag-icon-font-display-checkbox-indeterminate, var(--ag-icon-font-display));
}

.ag-icon-checkbox-indeterminate::after {
  background-image: var(--ag-icon-image-checkbox-indeterminate, var(--ag-icon-image));
  display: var(--ag-icon-image-display-checkbox-indeterminate, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-checkbox-indeterminate, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-checkbox-unchecked {
  font-family: var(--ag-icon-font-family-checkbox-unchecked, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-checkbox-unchecked, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-checkbox-unchecked, var(--ag-icon-font-color));
}

.ag-icon-checkbox-unchecked::before {
  content: var(--ag-icon-font-code-checkbox-unchecked, "\f108");
  display: var(--ag-icon-font-display-checkbox-unchecked, var(--ag-icon-font-display));
}

.ag-icon-checkbox-unchecked::after {
  background-image: var(--ag-icon-image-checkbox-unchecked, var(--ag-icon-image));
  display: var(--ag-icon-image-display-checkbox-unchecked, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-checkbox-unchecked, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-color-picker {
  font-family: var(--ag-icon-font-family-color-picker, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-color-picker, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-color-picker, var(--ag-icon-font-color));
}

.ag-icon-color-picker::before {
  content: var(--ag-icon-font-code-color-picker, "\f109");
  display: var(--ag-icon-font-display-color-picker, var(--ag-icon-font-display));
}

.ag-icon-color-picker::after {
  background-image: var(--ag-icon-image-color-picker, var(--ag-icon-image));
  display: var(--ag-icon-image-display-color-picker, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-color-picker, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-columns {
  font-family: var(--ag-icon-font-family-columns, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-columns, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-columns, var(--ag-icon-font-color));
}

.ag-icon-columns::before {
  content: var(--ag-icon-font-code-columns, "\f10a");
  display: var(--ag-icon-font-display-columns, var(--ag-icon-font-display));
}

.ag-icon-columns::after {
  background-image: var(--ag-icon-image-columns, var(--ag-icon-image));
  display: var(--ag-icon-image-display-columns, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-columns, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-contracted {
  font-family: var(--ag-icon-font-family-contracted, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-contracted, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-contracted, var(--ag-icon-font-color));
}

.ag-icon-contracted::before {
  content: var(--ag-icon-font-code-contracted, "\f10b");
  display: var(--ag-icon-font-display-contracted, var(--ag-icon-font-display));
}

.ag-icon-contracted::after {
  background-image: var(--ag-icon-image-contracted, var(--ag-icon-image));
  display: var(--ag-icon-image-display-contracted, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-contracted, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-copy {
  font-family: var(--ag-icon-font-family-copy, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-copy, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-copy, var(--ag-icon-font-color));
}

.ag-icon-copy::before {
  content: var(--ag-icon-font-code-copy, "\f10c");
  display: var(--ag-icon-font-display-copy, var(--ag-icon-font-display));
}

.ag-icon-copy::after {
  background-image: var(--ag-icon-image-copy, var(--ag-icon-image));
  display: var(--ag-icon-image-display-copy, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-copy, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-cross {
  font-family: var(--ag-icon-font-family-cross, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-cross, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-cross, var(--ag-icon-font-color));
}

.ag-icon-cross::before {
  content: var(--ag-icon-font-code-cross, "\f10d");
  display: var(--ag-icon-font-display-cross, var(--ag-icon-font-display));
}

.ag-icon-cross::after {
  background-image: var(--ag-icon-image-cross, var(--ag-icon-image));
  display: var(--ag-icon-image-display-cross, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-cross, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-csv {
  font-family: var(--ag-icon-font-family-csv, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-csv, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-csv, var(--ag-icon-font-color));
}

.ag-icon-csv::before {
  content: var(--ag-icon-font-code-csv, "\f10e");
  display: var(--ag-icon-font-display-csv, var(--ag-icon-font-display));
}

.ag-icon-csv::after {
  background-image: var(--ag-icon-image-csv, var(--ag-icon-image));
  display: var(--ag-icon-image-display-csv, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-csv, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-cut {
  font-family: var(--ag-icon-font-family-cut, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-cut, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-cut, var(--ag-icon-font-color));
}

.ag-icon-cut::before {
  content: var(--ag-icon-font-code-cut, "\f10f");
  display: var(--ag-icon-font-display-cut, var(--ag-icon-font-display));
}

.ag-icon-cut::after {
  background-image: var(--ag-icon-image-cut, var(--ag-icon-image));
  display: var(--ag-icon-image-display-cut, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-cut, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-desc {
  font-family: var(--ag-icon-font-family-desc, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-desc, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-desc, var(--ag-icon-font-color));
}

.ag-icon-desc::before {
  content: var(--ag-icon-font-code-desc, "\f110");
  display: var(--ag-icon-font-display-desc, var(--ag-icon-font-display));
}

.ag-icon-desc::after {
  background-image: var(--ag-icon-image-desc, var(--ag-icon-image));
  display: var(--ag-icon-image-display-desc, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-desc, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-excel {
  font-family: var(--ag-icon-font-family-excel, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-excel, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-excel, var(--ag-icon-font-color));
}

.ag-icon-excel::before {
  content: var(--ag-icon-font-code-excel, "\f111");
  display: var(--ag-icon-font-display-excel, var(--ag-icon-font-display));
}

.ag-icon-excel::after {
  background-image: var(--ag-icon-image-excel, var(--ag-icon-image));
  display: var(--ag-icon-image-display-excel, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-excel, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-expanded {
  font-family: var(--ag-icon-font-family-expanded, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-expanded, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-expanded, var(--ag-icon-font-color));
}

.ag-icon-expanded::before {
  content: var(--ag-icon-font-code-expanded, "\f112");
  display: var(--ag-icon-font-display-expanded, var(--ag-icon-font-display));
}

.ag-icon-expanded::after {
  background-image: var(--ag-icon-image-expanded, var(--ag-icon-image));
  display: var(--ag-icon-image-display-expanded, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-expanded, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-eye-slash {
  font-family: var(--ag-icon-font-family-eye-slash, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-eye-slash, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-eye-slash, var(--ag-icon-font-color));
}

.ag-icon-eye-slash::before {
  content: var(--ag-icon-font-code-eye-slash, "\f113");
  display: var(--ag-icon-font-display-eye-slash, var(--ag-icon-font-display));
}

.ag-icon-eye-slash::after {
  background-image: var(--ag-icon-image-eye-slash, var(--ag-icon-image));
  display: var(--ag-icon-image-display-eye-slash, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-eye-slash, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-eye {
  font-family: var(--ag-icon-font-family-eye, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-eye, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-eye, var(--ag-icon-font-color));
}

.ag-icon-eye::before {
  content: var(--ag-icon-font-code-eye, "\f114");
  display: var(--ag-icon-font-display-eye, var(--ag-icon-font-display));
}

.ag-icon-eye::after {
  background-image: var(--ag-icon-image-eye, var(--ag-icon-image));
  display: var(--ag-icon-image-display-eye, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-eye, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-filter {
  font-family: var(--ag-icon-font-family-filter, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-filter, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-filter, var(--ag-icon-font-color));
}

.ag-icon-filter::before {
  content: var(--ag-icon-font-code-filter, "\f115");
  display: var(--ag-icon-font-display-filter, var(--ag-icon-font-display));
}

.ag-icon-filter::after {
  background-image: var(--ag-icon-image-filter, var(--ag-icon-image));
  display: var(--ag-icon-image-display-filter, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-filter, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-first {
  font-family: var(--ag-icon-font-family-first, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-first, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-first, var(--ag-icon-font-color));
}

.ag-icon-first::before {
  content: var(--ag-icon-font-code-first, "\f116");
  display: var(--ag-icon-font-display-first, var(--ag-icon-font-display));
}

.ag-icon-first::after {
  background-image: var(--ag-icon-image-first, var(--ag-icon-image));
  display: var(--ag-icon-image-display-first, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-first, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-grip {
  font-family: var(--ag-icon-font-family-grip, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-grip, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-grip, var(--ag-icon-font-color));
}

.ag-icon-grip::before {
  content: var(--ag-icon-font-code-grip, "\f117");
  display: var(--ag-icon-font-display-grip, var(--ag-icon-font-display));
}

.ag-icon-grip::after {
  background-image: var(--ag-icon-image-grip, var(--ag-icon-image));
  display: var(--ag-icon-image-display-grip, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-grip, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-group {
  font-family: var(--ag-icon-font-family-group, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-group, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-group, var(--ag-icon-font-color));
}

.ag-icon-group::before {
  content: var(--ag-icon-font-code-group, "\f118");
  display: var(--ag-icon-font-display-group, var(--ag-icon-font-display));
}

.ag-icon-group::after {
  background-image: var(--ag-icon-image-group, var(--ag-icon-image));
  display: var(--ag-icon-image-display-group, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-group, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-last {
  font-family: var(--ag-icon-font-family-last, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-last, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-last, var(--ag-icon-font-color));
}

.ag-icon-last::before {
  content: var(--ag-icon-font-code-last, "\f119");
  display: var(--ag-icon-font-display-last, var(--ag-icon-font-display));
}

.ag-icon-last::after {
  background-image: var(--ag-icon-image-last, var(--ag-icon-image));
  display: var(--ag-icon-image-display-last, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-last, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-left {
  font-family: var(--ag-icon-font-family-left, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-left, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-left, var(--ag-icon-font-color));
}

.ag-icon-left::before {
  content: var(--ag-icon-font-code-left, "\f11a");
  display: var(--ag-icon-font-display-left, var(--ag-icon-font-display));
}

.ag-icon-left::after {
  background-image: var(--ag-icon-image-left, var(--ag-icon-image));
  display: var(--ag-icon-image-display-left, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-left, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-linked {
  font-family: var(--ag-icon-font-family-linked, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-linked, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-linked, var(--ag-icon-font-color));
}

.ag-icon-linked::before {
  content: var(--ag-icon-font-code-linked, "\f11b");
  display: var(--ag-icon-font-display-linked, var(--ag-icon-font-display));
}

.ag-icon-linked::after {
  background-image: var(--ag-icon-image-linked, var(--ag-icon-image));
  display: var(--ag-icon-image-display-linked, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-linked, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-loading {
  font-family: var(--ag-icon-font-family-loading, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-loading, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-loading, var(--ag-icon-font-color));
}

.ag-icon-loading::before {
  content: var(--ag-icon-font-code-loading, "\f11c");
  display: var(--ag-icon-font-display-loading, var(--ag-icon-font-display));
}

.ag-icon-loading::after {
  background-image: var(--ag-icon-image-loading, var(--ag-icon-image));
  display: var(--ag-icon-image-display-loading, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-loading, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-maximize {
  font-family: var(--ag-icon-font-family-maximize, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-maximize, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-maximize, var(--ag-icon-font-color));
}

.ag-icon-maximize::before {
  content: var(--ag-icon-font-code-maximize, "\f11d");
  display: var(--ag-icon-font-display-maximize, var(--ag-icon-font-display));
}

.ag-icon-maximize::after {
  background-image: var(--ag-icon-image-maximize, var(--ag-icon-image));
  display: var(--ag-icon-image-display-maximize, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-maximize, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-menu {
  font-family: var(--ag-icon-font-family-menu, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-menu, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-menu, var(--ag-icon-font-color));
}

.ag-icon-menu::before {
  content: var(--ag-icon-font-code-menu, "\f11e");
  display: var(--ag-icon-font-display-menu, var(--ag-icon-font-display));
}

.ag-icon-menu::after {
  background-image: var(--ag-icon-image-menu, var(--ag-icon-image));
  display: var(--ag-icon-image-display-menu, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-menu, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-minimize {
  font-family: var(--ag-icon-font-family-minimize, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-minimize, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-minimize, var(--ag-icon-font-color));
}

.ag-icon-minimize::before {
  content: var(--ag-icon-font-code-minimize, "\f11f");
  display: var(--ag-icon-font-display-minimize, var(--ag-icon-font-display));
}

.ag-icon-minimize::after {
  background-image: var(--ag-icon-image-minimize, var(--ag-icon-image));
  display: var(--ag-icon-image-display-minimize, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-minimize, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-next {
  font-family: var(--ag-icon-font-family-next, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-next, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-next, var(--ag-icon-font-color));
}

.ag-icon-next::before {
  content: var(--ag-icon-font-code-next, "\f120");
  display: var(--ag-icon-font-display-next, var(--ag-icon-font-display));
}

.ag-icon-next::after {
  background-image: var(--ag-icon-image-next, var(--ag-icon-image));
  display: var(--ag-icon-image-display-next, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-next, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-none {
  font-family: var(--ag-icon-font-family-none, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-none, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-none, var(--ag-icon-font-color));
}

.ag-icon-none::before {
  content: var(--ag-icon-font-code-none, "\f121");
  display: var(--ag-icon-font-display-none, var(--ag-icon-font-display));
}

.ag-icon-none::after {
  background-image: var(--ag-icon-image-none, var(--ag-icon-image));
  display: var(--ag-icon-image-display-none, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-none, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-not-allowed {
  font-family: var(--ag-icon-font-family-not-allowed, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-not-allowed, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-not-allowed, var(--ag-icon-font-color));
}

.ag-icon-not-allowed::before {
  content: var(--ag-icon-font-code-not-allowed, "\f122");
  display: var(--ag-icon-font-display-not-allowed, var(--ag-icon-font-display));
}

.ag-icon-not-allowed::after {
  background-image: var(--ag-icon-image-not-allowed, var(--ag-icon-image));
  display: var(--ag-icon-image-display-not-allowed, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-not-allowed, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-paste {
  font-family: var(--ag-icon-font-family-paste, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-paste, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-paste, var(--ag-icon-font-color));
}

.ag-icon-paste::before {
  content: var(--ag-icon-font-code-paste, "\f123");
  display: var(--ag-icon-font-display-paste, var(--ag-icon-font-display));
}

.ag-icon-paste::after {
  background-image: var(--ag-icon-image-paste, var(--ag-icon-image));
  display: var(--ag-icon-image-display-paste, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-paste, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-pin {
  font-family: var(--ag-icon-font-family-pin, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-pin, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-pin, var(--ag-icon-font-color));
}

.ag-icon-pin::before {
  content: var(--ag-icon-font-code-pin, "\f124");
  display: var(--ag-icon-font-display-pin, var(--ag-icon-font-display));
}

.ag-icon-pin::after {
  background-image: var(--ag-icon-image-pin, var(--ag-icon-image));
  display: var(--ag-icon-image-display-pin, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-pin, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-pivot {
  font-family: var(--ag-icon-font-family-pivot, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-pivot, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-pivot, var(--ag-icon-font-color));
}

.ag-icon-pivot::before {
  content: var(--ag-icon-font-code-pivot, "\f125");
  display: var(--ag-icon-font-display-pivot, var(--ag-icon-font-display));
}

.ag-icon-pivot::after {
  background-image: var(--ag-icon-image-pivot, var(--ag-icon-image));
  display: var(--ag-icon-image-display-pivot, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-pivot, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-previous {
  font-family: var(--ag-icon-font-family-previous, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-previous, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-previous, var(--ag-icon-font-color));
}

.ag-icon-previous::before {
  content: var(--ag-icon-font-code-previous, "\f126");
  display: var(--ag-icon-font-display-previous, var(--ag-icon-font-display));
}

.ag-icon-previous::after {
  background-image: var(--ag-icon-image-previous, var(--ag-icon-image));
  display: var(--ag-icon-image-display-previous, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-previous, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-radio-button-off {
  font-family: var(--ag-icon-font-family-radio-button-off, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-radio-button-off, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-radio-button-off, var(--ag-icon-font-color));
}

.ag-icon-radio-button-off::before {
  content: var(--ag-icon-font-code-radio-button-off, "\f127");
  display: var(--ag-icon-font-display-radio-button-off, var(--ag-icon-font-display));
}

.ag-icon-radio-button-off::after {
  background-image: var(--ag-icon-image-radio-button-off, var(--ag-icon-image));
  display: var(--ag-icon-image-display-radio-button-off, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-radio-button-off, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-radio-button-on {
  font-family: var(--ag-icon-font-family-radio-button-on, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-radio-button-on, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-radio-button-on, var(--ag-icon-font-color));
}

.ag-icon-radio-button-on::before {
  content: var(--ag-icon-font-code-radio-button-on, "\f128");
  display: var(--ag-icon-font-display-radio-button-on, var(--ag-icon-font-display));
}

.ag-icon-radio-button-on::after {
  background-image: var(--ag-icon-image-radio-button-on, var(--ag-icon-image));
  display: var(--ag-icon-image-display-radio-button-on, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-radio-button-on, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-right {
  font-family: var(--ag-icon-font-family-right, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-right, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-right, var(--ag-icon-font-color));
}

.ag-icon-right::before {
  content: var(--ag-icon-font-code-right, "\f129");
  display: var(--ag-icon-font-display-right, var(--ag-icon-font-display));
}

.ag-icon-right::after {
  background-image: var(--ag-icon-image-right, var(--ag-icon-image));
  display: var(--ag-icon-image-display-right, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-right, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-save {
  font-family: var(--ag-icon-font-family-save, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-save, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-save, var(--ag-icon-font-color));
}

.ag-icon-save::before {
  content: var(--ag-icon-font-code-save, "\f12a");
  display: var(--ag-icon-font-display-save, var(--ag-icon-font-display));
}

.ag-icon-save::after {
  background-image: var(--ag-icon-image-save, var(--ag-icon-image));
  display: var(--ag-icon-image-display-save, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-save, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-small-down {
  font-family: var(--ag-icon-font-family-small-down, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-small-down, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-small-down, var(--ag-icon-font-color));
}

.ag-icon-small-down::before {
  content: var(--ag-icon-font-code-small-down, "\f12b");
  display: var(--ag-icon-font-display-small-down, var(--ag-icon-font-display));
}

.ag-icon-small-down::after {
  background-image: var(--ag-icon-image-small-down, var(--ag-icon-image));
  display: var(--ag-icon-image-display-small-down, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-small-down, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-small-left {
  font-family: var(--ag-icon-font-family-small-left, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-small-left, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-small-left, var(--ag-icon-font-color));
}

.ag-icon-small-left::before {
  content: var(--ag-icon-font-code-small-left, "\f12c");
  display: var(--ag-icon-font-display-small-left, var(--ag-icon-font-display));
}

.ag-icon-small-left::after {
  background-image: var(--ag-icon-image-small-left, var(--ag-icon-image));
  display: var(--ag-icon-image-display-small-left, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-small-left, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-small-right {
  font-family: var(--ag-icon-font-family-small-right, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-small-right, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-small-right, var(--ag-icon-font-color));
}

.ag-icon-small-right::before {
  content: var(--ag-icon-font-code-small-right, "\f12d");
  display: var(--ag-icon-font-display-small-right, var(--ag-icon-font-display));
}

.ag-icon-small-right::after {
  background-image: var(--ag-icon-image-small-right, var(--ag-icon-image));
  display: var(--ag-icon-image-display-small-right, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-small-right, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-small-up {
  font-family: var(--ag-icon-font-family-small-up, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-small-up, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-small-up, var(--ag-icon-font-color));
}

.ag-icon-small-up::before {
  content: var(--ag-icon-font-code-small-up, "\f12e");
  display: var(--ag-icon-font-display-small-up, var(--ag-icon-font-display));
}

.ag-icon-small-up::after {
  background-image: var(--ag-icon-image-small-up, var(--ag-icon-image));
  display: var(--ag-icon-image-display-small-up, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-small-up, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-tick {
  font-family: var(--ag-icon-font-family-tick, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-tick, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-tick, var(--ag-icon-font-color));
}

.ag-icon-tick::before {
  content: var(--ag-icon-font-code-tick, "\f12f");
  display: var(--ag-icon-font-display-tick, var(--ag-icon-font-display));
}

.ag-icon-tick::after {
  background-image: var(--ag-icon-image-tick, var(--ag-icon-image));
  display: var(--ag-icon-image-display-tick, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-tick, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-tree-closed {
  font-family: var(--ag-icon-font-family-tree-closed, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-tree-closed, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-tree-closed, var(--ag-icon-font-color));
}

.ag-icon-tree-closed::before {
  content: var(--ag-icon-font-code-tree-closed, "\f130");
  display: var(--ag-icon-font-display-tree-closed, var(--ag-icon-font-display));
}

.ag-icon-tree-closed::after {
  background-image: var(--ag-icon-image-tree-closed, var(--ag-icon-image));
  display: var(--ag-icon-image-display-tree-closed, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-tree-closed, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-tree-indeterminate {
  font-family: var(--ag-icon-font-family-tree-indeterminate, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-tree-indeterminate, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-tree-indeterminate, var(--ag-icon-font-color));
}

.ag-icon-tree-indeterminate::before {
  content: var(--ag-icon-font-code-tree-indeterminate, "\f131");
  display: var(--ag-icon-font-display-tree-indeterminate, var(--ag-icon-font-display));
}

.ag-icon-tree-indeterminate::after {
  background-image: var(--ag-icon-image-tree-indeterminate, var(--ag-icon-image));
  display: var(--ag-icon-image-display-tree-indeterminate, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-tree-indeterminate, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-tree-open {
  font-family: var(--ag-icon-font-family-tree-open, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-tree-open, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-tree-open, var(--ag-icon-font-color));
}

.ag-icon-tree-open::before {
  content: var(--ag-icon-font-code-tree-open, "\f132");
  display: var(--ag-icon-font-display-tree-open, var(--ag-icon-font-display));
}

.ag-icon-tree-open::after {
  background-image: var(--ag-icon-image-tree-open, var(--ag-icon-image));
  display: var(--ag-icon-image-display-tree-open, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-tree-open, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-unlinked {
  font-family: var(--ag-icon-font-family-unlinked, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-unlinked, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-unlinked, var(--ag-icon-font-color));
}

.ag-icon-unlinked::before {
  content: var(--ag-icon-font-code-unlinked, "\f133");
  display: var(--ag-icon-font-display-unlinked, var(--ag-icon-font-display));
}

.ag-icon-unlinked::after {
  background-image: var(--ag-icon-image-unlinked, var(--ag-icon-image));
  display: var(--ag-icon-image-display-unlinked, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-unlinked, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-up {
  font-family: var(--ag-icon-font-family-up, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-up, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-up, var(--ag-icon-font-color));
}

.ag-icon-up::before {
  content: var(--ag-icon-font-code-up, "\f134");
  display: var(--ag-icon-font-display-up, var(--ag-icon-font-display));
}

.ag-icon-up::after {
  background-image: var(--ag-icon-image-up, var(--ag-icon-image));
  display: var(--ag-icon-image-display-up, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-up, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-down {
  font-family: var(--ag-icon-font-family-down, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-down, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-down, var(--ag-icon-font-color));
}

.ag-icon-down::before {
  content: var(--ag-icon-font-code-down, "\f135");
  display: var(--ag-icon-font-display-down, var(--ag-icon-font-display));
}

.ag-icon-down::after {
  background-image: var(--ag-icon-image-down, var(--ag-icon-image));
  display: var(--ag-icon-image-display-down, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-down, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-plus {
  font-family: var(--ag-icon-font-family-plus, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-plus, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-plus, var(--ag-icon-font-color));
}

.ag-icon-plus::before {
  content: var(--ag-icon-font-code-plus, "\f136");
  display: var(--ag-icon-font-display-plus, var(--ag-icon-font-display));
}

.ag-icon-plus::after {
  background-image: var(--ag-icon-image-plus, var(--ag-icon-image));
  display: var(--ag-icon-image-display-plus, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-plus, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-minus {
  font-family: var(--ag-icon-font-family-minus, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-minus, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-minus, var(--ag-icon-font-color));
}

.ag-icon-minus::before {
  content: var(--ag-icon-font-code-minus, "\f137");
  display: var(--ag-icon-font-display-minus, var(--ag-icon-font-display));
}

.ag-icon-minus::after {
  background-image: var(--ag-icon-image-minus, var(--ag-icon-image));
  display: var(--ag-icon-image-display-minus, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-minus, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-menu-alt {
  font-family: var(--ag-icon-font-family-menu-alt, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-menu-alt, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-menu-alt, var(--ag-icon-font-color));
}

.ag-icon-menu-alt::before {
  content: var(--ag-icon-font-code-menu-alt, "\f138");
  display: var(--ag-icon-font-display-menu-alt, var(--ag-icon-font-display));
}

.ag-icon-menu-alt::after {
  background-image: var(--ag-icon-image-menu-alt, var(--ag-icon-image));
  display: var(--ag-icon-image-display-menu-alt, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-menu-alt, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-settings {
  font-family: var(--ag-icon-font-family-settings, var(--ag-icon-font-family));
  font-weight: var(--ag-icon-font-weight-settings, var(--ag-icon-font-weight));
  color: var(--ag-icon-font-color-settings, var(--ag-icon-font-color));
}

.ag-icon-settings::before {
  content: var(--ag-icon-font-code-settings, "\f139");
  display: var(--ag-icon-font-display-settings, var(--ag-icon-font-display));
}

.ag-icon-settings::after {
  background-image: var(--ag-icon-image-settings, var(--ag-icon-image));
  display: var(--ag-icon-image-display-settings, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-settings, var(--ag-icon-image-opacity, 0.9));
}

.ag-icon-row-drag::before {
  content: var(--ag-icon-font-code-grip);
}

.ag-left-arrow::before {
  content: var(--ag-icon-font-code-left);
}

.ag-right-arrow::before {
  content: var(--ag-icon-font-code-right);
}

[class*=ag-theme-] {
  --ag-foreground-color: #000;
  --ag-data-color: var(--ag-foreground-color);
  --ag-secondary-foreground-color: var(--ag-foreground-color);
  --ag-header-foreground-color: var(--ag-secondary-foreground-color);
  --ag-disabled-foreground-color: rgba(0, 0, 0, 0.5);
  --ag-background-color: #fff;
  --ag-header-background-color: transparent;
  --ag-tooltip-background-color: transparent;
  --ag-subheader-background-color: transparent;
  --ag-subheader-toolbar-background-color: transparent;
  --ag-control-panel-background-color: transparent;
  --ag-side-button-selected-background-color: var(--ag-control-panel-background-color);
  --ag-selected-row-background-color: #bbb;
  --ag-odd-row-background-color: var(--ag-background-color);
  --ag-modal-overlay-background-color: rgba(255, 255, 255, 0.66);
  --ag-menu-background-color: var(--ag-background-color);
  --ag-menu-border-color: var(--ag-border-color);
  --ag-panel-background-color: var(--ag-background-color);
  --ag-panel-border-color: var(--ag-border-color);
  --ag-row-hover-color: transparent;
  --ag-column-hover-color: transparent;
  --ag-range-selection-border-color: var(--ag-foreground-color);
  --ag-range-selection-border-style: solid;
  --ag-range-selection-background-color: rgba(0, 0, 0, 0.2);
  --ag-range-selection-background-color-2: var(--ag-range-selection-background-color);
  --ag-range-selection-background-color-3: var(--ag-range-selection-background-color);
  --ag-range-selection-background-color-4: var(--ag-range-selection-background-color);
  --ag-range-selection-highlight-color: var(--ag-range-selection-border-color);
  --ag-selected-tab-underline-color: var(--ag-range-selection-border-color);
  --ag-selected-tab-underline-width: 0;
  --ag-selected-tab-underline-transition-speed: 0s;
  --ag-range-selection-chart-category-background-color: rgba(0, 255, 132, 0.1);
  --ag-range-selection-chart-background-color: rgba(0, 88, 255, 0.1);
  --ag-header-cell-hover-background-color: transparent;
  --ag-header-cell-moving-background-color: var(--ag-background-color);
  --ag-value-change-value-highlight-background-color: rgba(22, 160, 133, 0.5);
  --ag-value-change-delta-up-color: #43a047;
  --ag-value-change-delta-down-color: #e53935;
  --ag-row-loading-skeleton-effect-color: rgba(66, 66, 66, 0.2);
  --ag-chip-background-color: transparent;
  --ag-chip-border-color: var(--ag-chip-background-color);
  --ag-borders: solid 1px;
  --ag-border-color: rgba(0, 0, 0, 0.25);
  --ag-borders-critical: var(--ag-borders);
  --ag-borders-secondary: var(--ag-borders);
  --ag-secondary-border-color: var(--ag-border-color);
  --ag-row-border-style: solid;
  --ag-row-border-color: var(--ag-secondary-border-color);
  --ag-row-border-width: 1px;
  --ag-cell-horizontal-border: solid transparent;
  --ag-borders-input: var(--ag-borders-secondary);
  --ag-input-border-color: var(--ag-secondary-border-color);
  --ag-borders-input-invalid: solid 2px;
  --ag-input-border-color-invalid: var(--ag-invalid-color);
  --ag-borders-side-button: var(--ag-borders);
  --ag-border-radius: 0px;
  --ag-wrapper-border-radius: var(--ag-border-radius);
  --ag-row-border-color: var(--ag-secondary-border-color);
  --ag-header-column-separator-display: none;
  --ag-header-column-separator-height: 100%;
  --ag-header-column-separator-width: 1px;
  --ag-header-column-separator-color: var(--ag-secondary-border-color);
  --ag-header-column-resize-handle-display: none;
  --ag-header-column-resize-handle-height: 50%;
  --ag-header-column-resize-handle-width: 1px;
  --ag-header-column-resize-handle-color: var(--ag-secondary-border-color);
  --ag-invalid-color: red;
  --ag-input-disabled-border-color: var(--ag-input-border-color);
  --ag-input-disabled-background-color: transparent;
  --ag-checkbox-background-color: transparent;
  --ag-checkbox-border-radius: var(--ag-border-radius);
  --ag-checkbox-checked-color: var(--ag-foreground-color);
  --ag-checkbox-unchecked-color: var(--ag-foreground-color);
  --ag-checkbox-indeterminate-color: var(--ag-checkbox-unchecked-color);
  --ag-toggle-button-off-border-color: var(--ag-checkbox-unchecked-color);
  --ag-toggle-button-off-background-color: var(--ag-checkbox-unchecked-color);
  --ag-toggle-button-on-border-color: var(--ag-checkbox-checked-color);
  --ag-toggle-button-on-background-color: var(--ag-checkbox-checked-color);
  --ag-toggle-button-switch-background-color: var(--ag-background-color);
  --ag-toggle-button-switch-border-color: var(--ag-toggle-button-off-border-color);
  --ag-toggle-button-border-width: 1px;
  --ag-toggle-button-height: var(--ag-icon-size);
  --ag-toggle-button-width: calc(var(--ag-toggle-button-height) * 2);
  --ag-input-focus-box-shadow: none;
  --ag-input-focus-border-color: none;
  --ag-minichart-selected-chart-color: var(--ag-checkbox-checked-color);
  --ag-minichart-selected-page-color: var(--ag-checkbox-checked-color);
  --ag-grid-size: 4px;
  --ag-icon-size: 12px;
  --ag-icon-font-weight: normal;
  --ag-icon-font-color: var(--ag-foreground-color);
  --ag-icon-image-display: block;
  --ag-widget-container-horizontal-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-container-vertical-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-horizontal-spacing: calc(var(--ag-grid-size) * 2);
  --ag-widget-vertical-spacing: var(--ag-grid-size);
  --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 3);
  --ag-cell-widget-spacing: var(--ag-cell-horizontal-padding);
  --ag-row-height: calc(var(--ag-grid-size) * 6 + 1px);
  --ag-header-height: var(--ag-row-height);
  --ag-list-item-height: calc(var(--ag-grid-size) * 5);
  --ag-column-select-indent-size: calc(var(--ag-grid-size) + var(--ag-icon-size));
  --ag-set-filter-indent-size: calc(var(--ag-grid-size) + var(--ag-icon-size));
  --ag-advanced-filter-builder-indent-size: calc(var(--ag-grid-size) * 2 + var(--ag-icon-size));
  --ag-row-group-indent-size: calc(var(--ag-cell-widget-spacing) + var(--ag-icon-size));
  --ag-filter-tool-panel-group-indent: 16px;
  --ag-tab-min-width: 220px;
  --ag-chart-menu-panel-width: var(--ag-tab-min-width);
  --ag-menu-min-width: 181px;
  --ag-side-bar-panel-width: 200px;
  --ag-font-family: "Helvetica Neue", sans-serif;
  --ag-font-size: 14px;
  --ag-card-radius: var(--ag-border-radius);
  --ag-card-shadow: none;
  --ag-popup-shadow: 5px 5px 10px rgba(0, 0, 0, 0.3);
  --ag-advanced-filter-join-pill-color: #f08e8d;
  --ag-advanced-filter-column-pill-color: #a6e194;
  --ag-advanced-filter-option-pill-color: #f3c08b;
  --ag-advanced-filter-value-pill-color: #85c0e4;
}

.ag-root-wrapper,
.ag-sticky-top,
.ag-sticky-bottom,
.ag-dnd-ghost {
  background-color: var(--ag-background-color);
}

.ag-sticky-bottom {
  border-top: var(--ag-row-border-style) var(--ag-row-border-color) var(--ag-row-border-width);
}

.ag-root-wrapper,
.ag-popup {
  --ag-indentation-level: 0;
}

[class*=ag-theme-] {
  -webkit-font-smoothing: antialiased;
  font-family: var(--ag-font-family);
  font-size: var(--ag-font-size);
  line-height: normal;
  color: var(--ag-foreground-color);
}

ag-grid,
ag-grid-angular,
ag-grid-ng2,
ag-grid-polymer,
ag-grid-aurelia {
  display: block;
}

.ag-aria-description-container {
  z-index: 9999;
  border: 0px;
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  width: 1px;
  position: absolute;
  overflow: hidden;
  padding: 0px;
  white-space: nowrap;
}

.ag-hidden {
  display: none !important;
}

.ag-invisible {
  visibility: hidden !important;
}

.ag-drag-handle {
  cursor: grab;
}

.ag-column-drop-wrapper {
  display: flex;
}

.ag-column-drop-horizontal-half-width {
  display: inline-block;
  width: 50% !important;
}

.ag-unselectable {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-selectable {
  -moz-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.ag-tab {
  position: relative;
}

.ag-tab-guard {
  position: absolute;
  width: 0;
  height: 0;
  display: block;
}

.ag-virtual-list-viewport .ag-tab-guard {
  position: sticky;
}

.ag-tab-guard-top {
  top: 1px;
}

.ag-tab-guard-bottom {
  bottom: 1px;
}

.ag-select-agg-func-popup {
  position: absolute;
}

.ag-input-wrapper,
.ag-picker-field-wrapper {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  line-height: normal;
  position: relative;
}

.ag-shake-left-to-right {
  animation-direction: alternate;
  animation-duration: 0.2s;
  animation-iteration-count: infinite;
  animation-name: ag-shake-left-to-right;
}

@keyframes ag-shake-left-to-right {
  from {
    padding-left: 6px;
    padding-right: 2px;
  }
  to {
    padding-left: 2px;
    padding-right: 6px;
  }
}
.ag-root-wrapper {
  cursor: default;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  white-space: normal;
}
.ag-root-wrapper.ag-layout-normal {
  height: 100%;
}

.ag-watermark {
  position: absolute;
  bottom: 20px;
  right: 25px;
  opacity: 0.7;
  transition: opacity 1s ease-out 3s;
  color: #9b9b9b;
}
.ag-watermark::before {
  content: "";
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
  background-size: 170px 40px;
  display: block;
  height: 40px;
  width: 170px;
}

.ag-watermark-text {
  opacity: 0.5;
  font-weight: bold;
  font-family: Impact, sans-serif;
  font-size: 19px;
  padding-left: 0.7rem;
}

.ag-root-wrapper-body {
  display: flex;
  flex-direction: row;
}
.ag-root-wrapper-body.ag-layout-normal {
  flex: 1 1 auto;
  height: 0;
  min-height: 0;
}

.ag-root {
  position: relative;
  display: flex;
  flex-direction: column;
}
.ag-root.ag-layout-normal, .ag-root.ag-layout-auto-height {
  overflow: hidden;
  flex: 1 1 auto;
  width: 0;
}
.ag-root.ag-layout-normal {
  height: 100%;
}

.ag-header-viewport,
.ag-floating-top-viewport,
.ag-body-viewport,
.ag-center-cols-viewport,
.ag-floating-bottom-viewport,
.ag-body-horizontal-scroll-viewport,
.ag-body-vertical-scroll-viewport,
.ag-virtual-list-viewport,
.ag-sticky-top-viewport,
.ag-sticky-bottom-viewport {
  position: relative;
  height: 100%;
  min-width: 0px;
  overflow: hidden;
  flex: 1 1 auto;
}

.ag-body-viewport,
.ag-center-cols-viewport,
.ag-header-viewport,
.ag-floating-top-viewport,
.ag-floating-bottom-viewport,
.ag-sticky-top-viewport,
.ag-sticky-bottom-viewport {
  overflow-x: auto;
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
}
.ag-body-viewport::-webkit-scrollbar,
.ag-center-cols-viewport::-webkit-scrollbar,
.ag-header-viewport::-webkit-scrollbar,
.ag-floating-top-viewport::-webkit-scrollbar,
.ag-floating-bottom-viewport::-webkit-scrollbar,
.ag-sticky-top-viewport::-webkit-scrollbar,
.ag-sticky-bottom-viewport::-webkit-scrollbar {
  display: none !important;
}

.ag-body-viewport {
  display: flex;
  overflow-x: hidden;
}
.ag-body-viewport.ag-layout-normal {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.ag-sticky-top-container,
.ag-sticky-bottom-container {
  min-height: 1px;
}

.ag-center-cols-viewport {
  min-height: 100%;
  width: 100%;
}

.ag-body-horizontal-scroll-viewport {
  overflow-x: scroll;
}

.ag-body-vertical-scroll-viewport {
  overflow-y: scroll;
}

.ag-virtual-list-viewport {
  overflow: auto;
  width: 100%;
}

.ag-header-container,
.ag-floating-top-container,
.ag-body-container,
.ag-pinned-right-cols-container,
.ag-center-cols-container,
.ag-pinned-left-cols-container,
.ag-floating-bottom-container,
.ag-body-horizontal-scroll-container,
.ag-body-vertical-scroll-container,
.ag-full-width-container,
.ag-floating-bottom-full-width-container,
.ag-virtual-list-container,
.ag-sticky-top-container,
.ag-sticky-bottom-container {
  position: relative;
}

.ag-header-container,
.ag-floating-top-container,
.ag-floating-bottom-container,
.ag-sticky-top-container,
.ag-sticky-bottom-container {
  height: 100%;
  white-space: nowrap;
}

.ag-center-cols-container {
  display: block;
}

.ag-pinned-right-cols-container {
  display: block;
}

.ag-body-horizontal-scroll-container {
  height: 100%;
}

.ag-body-vertical-scroll-container {
  width: 100%;
}

.ag-full-width-container,
.ag-floating-top-full-width-container,
.ag-floating-bottom-full-width-container,
.ag-sticky-top-full-width-container,
.ag-sticky-bottom-full-width-container {
  position: absolute;
  top: 0px;
  pointer-events: none;
}
.ag-ltr .ag-full-width-container,
.ag-ltr .ag-floating-top-full-width-container,
.ag-ltr .ag-floating-bottom-full-width-container,
.ag-ltr .ag-sticky-top-full-width-container,
.ag-ltr .ag-sticky-bottom-full-width-container {
  left: 0;
}
.ag-rtl .ag-full-width-container,
.ag-rtl .ag-floating-top-full-width-container,
.ag-rtl .ag-floating-bottom-full-width-container,
.ag-rtl .ag-sticky-top-full-width-container,
.ag-rtl .ag-sticky-bottom-full-width-container {
  right: 0;
}

.ag-full-width-container {
  width: 100%;
}

.ag-floating-bottom-full-width-container,
.ag-floating-top-full-width-container {
  display: inline-block;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.ag-virtual-list-container {
  overflow: hidden;
}

.ag-body {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  flex-direction: row !important;
  min-height: 0;
}

.ag-body-horizontal-scroll,
.ag-body-vertical-scroll {
  min-height: 0;
  min-width: 0;
  display: flex;
  position: relative;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible,
.ag-body-vertical-scroll.ag-scrollbar-invisible {
  position: absolute;
  bottom: 0;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar,
.ag-body-vertical-scroll.ag-scrollbar-invisible.ag-apple-scrollbar {
  opacity: 0;
  transition: opacity 400ms;
  visibility: hidden;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-scrolling, .ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-active,
.ag-body-vertical-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-scrolling,
.ag-body-vertical-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-active {
  visibility: visible;
  opacity: 1;
}

.ag-body-horizontal-scroll {
  width: 100%;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible {
  left: 0;
  right: 0;
}

.ag-body-vertical-scroll {
  height: 100%;
}
.ag-body-vertical-scroll.ag-scrollbar-invisible {
  top: 0;
  z-index: 10;
}
.ag-ltr .ag-body-vertical-scroll.ag-scrollbar-invisible {
  right: 0;
}
.ag-rtl .ag-body-vertical-scroll.ag-scrollbar-invisible {
  left: 0;
}

.ag-force-vertical-scroll {
  overflow-y: scroll !important;
}

.ag-horizontal-left-spacer,
.ag-horizontal-right-spacer {
  height: 100%;
  min-width: 0;
  overflow-x: scroll;
}
.ag-horizontal-left-spacer.ag-scroller-corner,
.ag-horizontal-right-spacer.ag-scroller-corner {
  overflow-x: hidden;
}

.ag-header,
.ag-pinned-left-header,
.ag-pinned-right-header {
  display: inline-block;
  overflow: hidden;
  position: relative;
}

.ag-header-cell-sortable .ag-header-cell-label {
  cursor: pointer;
}

.ag-header {
  display: flex;
  width: 100%;
  white-space: nowrap;
}

.ag-pinned-left-header {
  height: 100%;
}

.ag-pinned-right-header {
  height: 100%;
}

.ag-header-row {
  position: absolute;
}

.ag-header-row:not(.ag-header-row-column-group) {
  overflow: hidden;
}

.ag-header.ag-header-allow-overflow .ag-header-row {
  overflow: visible;
}

.ag-header-cell {
  display: inline-flex;
  align-items: center;
  position: absolute;
  height: 100%;
  overflow: hidden;
}

.ag-header-cell.ag-header-active .ag-header-cell-menu-button,
.ag-header-cell-filter-button {
  opacity: 1;
}

.ag-header-cell-menu-button:not(.ag-header-menu-always-show) {
  transition: opacity 0.2s;
  opacity: 0;
}

.ag-header-group-cell-label,
.ag-header-cell-label {
  display: flex;
  flex: 1 1 auto;
  align-self: stretch;
  align-items: center;
  overflow: hidden;
}

.ag-header-cell-label {
  text-overflow: ellipsis;
}

.ag-header-group-cell-label.ag-sticky-label {
  position: sticky;
  flex: none;
  max-width: 100%;
  overflow: visible;
}

.ag-header-group-text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-header-cell-text {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.ag-header-group-cell .ag-header-cell-comp-wrapper {
  display: flex;
}

.ag-header-cell:not(.ag-header-cell-auto-height) .ag-header-cell-comp-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
}

.ag-header-cell-comp-wrapper {
  width: 100%;
}

.ag-header-cell-wrap-text .ag-header-cell-comp-wrapper {
  white-space: normal;
}

.ag-header-cell-comp-wrapper-limited-height > div {
  overflow: hidden;
}

.ag-right-aligned-header .ag-header-cell-label {
  flex-direction: row-reverse;
}

.ag-header-cell-resize {
  position: absolute;
  z-index: 2;
  height: 100%;
  width: 8px;
  top: 0;
  cursor: ew-resize;
}
.ag-ltr .ag-header-cell-resize {
  right: -3px;
}
.ag-rtl .ag-header-cell-resize {
  left: -3px;
}

.ag-pinned-left-header .ag-header-cell-resize {
  right: -3px;
}

.ag-pinned-right-header .ag-header-cell-resize {
  left: -3px;
}

.ag-header-select-all {
  display: flex;
}

.ag-header-cell-menu-button,
.ag-header-cell-filter-button,
.ag-side-button-button,
.ag-panel-title-bar-button,
.ag-floating-filter-button-button {
  cursor: pointer;
}

.ag-column-moving .ag-cell {
  transition: left 0.2s;
}
.ag-column-moving .ag-header-cell {
  transition: left 0.2s;
}
.ag-column-moving .ag-header-group-cell {
  transition: left 0.2s, width 0.2s;
}

.ag-column-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1 1 auto;
}

.ag-column-select {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 3 1 0px;
}

.ag-column-select-header {
  position: relative;
  display: flex;
  flex: none;
}

.ag-column-select-header-icon {
  position: relative;
}

.ag-column-select-header-filter-wrapper {
  flex: 1 1 auto;
}

.ag-column-select-header-filter {
  width: 100%;
}

.ag-column-select-list {
  flex: 1 1 0px;
  overflow: hidden;
}

.ag-column-drop {
  position: relative;
  display: inline-flex;
  align-items: center;
  overflow: auto;
  width: 100%;
}

.ag-column-drop-list {
  display: flex;
  align-items: center;
}

.ag-column-drop-cell {
  position: relative;
  display: flex;
  align-items: center;
}

.ag-column-drop-cell-text {
  overflow: hidden;
  flex: 1 1 auto;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-column-drop-vertical {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  align-items: stretch;
  flex: 1 1 0px;
}

.ag-column-drop-vertical-title-bar {
  display: flex;
  align-items: center;
  flex: none;
}

.ag-column-drop-vertical-list {
  position: relative;
  align-items: stretch;
  flex-grow: 1;
  flex-direction: column;
  overflow-x: auto;
}
.ag-column-drop-vertical-list > * {
  flex: none;
}

.ag-column-drop-empty .ag-column-drop-vertical-list {
  overflow: hidden;
}

.ag-column-drop-vertical-empty-message {
  display: block;
}

.ag-column-drop.ag-column-drop-horizontal {
  white-space: nowrap;
  overflow: hidden;
}

.ag-column-drop-cell-button {
  cursor: pointer;
}

.ag-filter-toolpanel {
  flex: 1 1 0px;
  min-width: 0;
}

.ag-filter-toolpanel-header {
  position: relative;
}

.ag-filter-toolpanel-header,
.ag-filter-toolpanel-search {
  display: flex;
  align-items: center;
}
.ag-filter-toolpanel-header > *,
.ag-filter-toolpanel-search > * {
  display: flex;
  align-items: center;
}

.ag-filter-apply-panel {
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
}

.ag-row-animation .ag-row {
  transition: transform 0.4s, top 0.4s, opacity 0.2s;
}

.ag-row-animation .ag-row.ag-after-created {
  transition: transform 0.4s, top 0.4s, height 0.4s, opacity 0.2s;
}

.ag-row-no-animation .ag-row {
  transition: none;
}

.ag-row {
  white-space: nowrap;
  width: 100%;
}

.ag-row-loading {
  display: flex;
  align-items: center;
}

.ag-row-position-absolute {
  position: absolute;
}

.ag-row-position-relative {
  position: relative;
}

.ag-full-width-row {
  overflow: hidden;
  pointer-events: all;
}

.ag-row-inline-editing {
  z-index: 1;
}

.ag-row-dragging {
  z-index: 2;
}

.ag-stub-cell {
  display: flex;
  align-items: center;
}

.ag-cell {
  display: inline-block;
  position: absolute;
  white-space: nowrap;
  height: 100%;
}

.ag-cell-value {
  flex: 1 1 auto;
}

.ag-cell-value,
.ag-group-value {
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-cell-wrap-text {
  white-space: normal;
  word-break: break-word;
}

.ag-cell-wrapper {
  display: flex;
  align-items: center;
}
.ag-cell-wrapper.ag-row-group {
  align-items: flex-start;
}

.ag-sparkline-wrapper {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}

.ag-full-width-row .ag-cell-wrapper.ag-row-group {
  height: 100%;
  align-items: center;
}

.ag-cell-inline-editing {
  z-index: 1;
}
.ag-cell-inline-editing .ag-cell-wrapper,
.ag-cell-inline-editing .ag-cell-edit-wrapper,
.ag-cell-inline-editing .ag-cell-editor,
.ag-cell-inline-editing .ag-cell-editor .ag-wrapper,
.ag-cell-inline-editing .ag-cell-editor input {
  height: 100%;
  width: 100%;
  line-height: normal;
}

.ag-cell .ag-icon {
  display: inline-block;
  vertical-align: middle;
}

.ag-set-filter-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.ag-set-filter-item-checkbox {
  display: flex;
  width: 100%;
  height: 100%;
}

.ag-set-filter-group-icons {
  display: block;
}
.ag-set-filter-group-icons > * {
  cursor: pointer;
}

.ag-filter-body-wrapper {
  display: flex;
  flex-direction: column;
}

.ag-filter-filter {
  flex: 1 1 0px;
}

.ag-filter-condition {
  display: flex;
  justify-content: center;
}

.ag-floating-filter-body {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  height: 100%;
}

.ag-floating-filter-full-body {
  display: flex;
  flex: 1 1 auto;
  height: 100%;
  width: 100%;
  align-items: center;
  overflow: hidden;
}

.ag-floating-filter-full-body > div {
  flex: 1 1 auto;
}

.ag-floating-filter-input {
  align-items: center;
  display: flex;
  width: 100%;
}
.ag-floating-filter-input > * {
  flex: 1 1 auto;
}

.ag-floating-filter-button {
  display: flex;
  flex: none;
}

.ag-set-floating-filter-input input[disabled] {
  pointer-events: none;
}

.ag-dnd-ghost {
  display: inline-flex;
  align-items: center;
  cursor: move;
  white-space: nowrap;
}

.ag-overlay {
  height: 100%;
  left: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 2;
}

.ag-overlay-panel {
  display: flex;
  height: 100%;
  width: 100%;
}

.ag-overlay-wrapper {
  display: flex;
  flex: none;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.ag-overlay-loading-wrapper {
  pointer-events: all;
}

.ag-popup-child {
  z-index: 5;
  top: 0;
}

.ag-popup-editor {
  position: absolute;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-large-text-input {
  display: block;
}

.ag-virtual-list-item {
  position: absolute;
  width: 100%;
}

.ag-floating-top {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  position: relative;
  display: flex;
}

.ag-pinned-left-floating-top {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-pinned-right-floating-top {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-floating-bottom {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  position: relative;
  display: flex;
}

.ag-pinned-left-floating-bottom {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-pinned-right-floating-bottom {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-sticky-top,
.ag-sticky-bottom {
  position: absolute;
  display: flex;
  width: 100%;
  overflow: hidden;
  height: 0px;
}

.ag-sticky-bottom {
  box-sizing: content-box !important;
}

.ag-pinned-left-sticky-top,
.ag-pinned-right-sticky-top {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.ag-sticky-top-full-width-container,
.ag-sticky-bottom-full-width-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.ag-dialog,
.ag-panel {
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.ag-panel-title-bar {
  display: flex;
  flex: none;
  align-items: center;
  cursor: default;
}

.ag-panel-title-bar-title {
  flex: 1 1 auto;
}

.ag-panel-title-bar-buttons {
  display: flex;
}

.ag-panel-title-bar-button {
  cursor: pointer;
}

.ag-panel-content-wrapper {
  display: flex;
  flex: 1 1 auto;
  position: relative;
  overflow: hidden;
}

.ag-dialog {
  position: absolute;
}

.ag-resizer {
  position: absolute;
  pointer-events: none;
  z-index: 1;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.ag-resizer.ag-resizer-topLeft {
  top: 0;
  left: 0;
  height: 5px;
  width: 5px;
  cursor: nwse-resize;
}
.ag-resizer.ag-resizer-top {
  top: 0;
  left: 5px;
  right: 5px;
  height: 5px;
  cursor: ns-resize;
}
.ag-resizer.ag-resizer-topRight {
  top: 0;
  right: 0;
  height: 5px;
  width: 5px;
  cursor: nesw-resize;
}
.ag-resizer.ag-resizer-right {
  top: 5px;
  right: 0;
  bottom: 5px;
  width: 5px;
  cursor: ew-resize;
}
.ag-resizer.ag-resizer-bottomRight {
  bottom: 0;
  right: 0;
  height: 5px;
  width: 5px;
  cursor: nwse-resize;
}
.ag-resizer.ag-resizer-bottom {
  bottom: 0;
  left: 5px;
  right: 5px;
  height: 5px;
  cursor: ns-resize;
}
.ag-resizer.ag-resizer-bottomLeft {
  bottom: 0;
  left: 0;
  height: 5px;
  width: 5px;
  cursor: nesw-resize;
}
.ag-resizer.ag-resizer-left {
  left: 0;
  top: 5px;
  bottom: 5px;
  width: 5px;
  cursor: ew-resize;
}

.ag-tooltip {
  position: absolute;
  z-index: 99999;
}

.ag-tooltip-custom {
  position: absolute;
  z-index: 99999;
}

.ag-tooltip:not(.ag-tooltip-interactive),
.ag-tooltip-custom:not(.ag-tooltip-interactive) {
  pointer-events: none;
}

.ag-value-slide-out {
  margin-right: 5px;
  opacity: 1;
  transition: opacity 3s, margin-right 3s;
  transition-timing-function: linear;
}

.ag-value-slide-out-end {
  margin-right: 10px;
  opacity: 0;
}

.ag-opacity-zero {
  opacity: 0 !important;
}

.ag-menu {
  max-height: 100%;
  overflow-y: auto;
  position: absolute;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-menu-column-select-wrapper {
  height: 265px;
  overflow: auto;
}
.ag-menu-column-select-wrapper .ag-column-select {
  height: 100%;
}

.ag-dialog .ag-panel-content-wrapper .ag-column-select {
  user-select: none;
}

.ag-menu-list {
  display: table;
  width: 100%;
}

.ag-menu-option,
.ag-menu-separator {
  display: table-row;
}

.ag-menu-option-part,
.ag-menu-separator-part {
  display: table-cell;
  vertical-align: middle;
}

.ag-menu-option-text {
  white-space: nowrap;
}

.ag-menu-option-custom {
  display: contents;
}

.ag-compact-menu-option {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
}

.ag-compact-menu-option-text {
  white-space: nowrap;
  flex: 1 1 auto;
}

.ag-pill-container {
  display: flex;
  gap: 0.25rem;
  flex-wrap: nowrap;
}

.ag-pill {
  display: flex;
  white-space: nowrap;
  padding: 0 0.25rem;
  align-items: center;
}

.ag-pill .ag-pill-button {
  border: none;
  padding: 0;
}

.ag-rich-select {
  cursor: default;
  outline: none;
  height: 100%;
}

.ag-rich-select-value {
  display: flex;
  align-items: center;
  height: 100%;
}
.ag-rich-select-value .ag-picker-field-display {
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-rich-select-value .ag-picker-field-display.ag-display-as-placeholder {
  opacity: 0.5;
}

.ag-rich-select-list {
  position: relative;
}
.ag-rich-select-list .ag-loading-text {
  min-height: 2rem;
}

.ag-rich-select-row {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  height: 100%;
}

.ag-rich-select-field-input {
  flex: 1 1 auto;
}
.ag-rich-select-field-input .ag-input-field-input {
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  text-overflow: ellipsis;
}
.ag-rich-select-field-input .ag-input-field-input::placeholder {
  opacity: 0.8;
}

.ag-autocomplete {
  align-items: center;
  display: flex;
}
.ag-autocomplete > * {
  flex: 1 1 auto;
}

.ag-autocomplete-list-popup {
  position: absolute;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-autocomplete-list {
  position: relative;
}

.ag-autocomplete-virtual-list-item {
  display: flex;
}

.ag-autocomplete-row {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  overflow: hidden;
}

.ag-autocomplete-row-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-paging-panel {
  align-items: center;
  display: flex;
  justify-content: flex-end;
}

.ag-paging-page-summary-panel {
  display: flex;
  align-items: center;
}

.ag-paging-button {
  position: relative;
}

.ag-disabled .ag-paging-page-summary-panel {
  pointer-events: none;
}

.ag-tool-panel-wrapper {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  cursor: default;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-column-select-column,
.ag-column-select-column-group,
.ag-select-agg-func-item {
  position: relative;
  align-items: center;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  height: 100%;
}
.ag-column-select-column > *,
.ag-column-select-column-group > *,
.ag-select-agg-func-item > * {
  flex: none;
}

.ag-select-agg-func-item,
.ag-column-select-column-label {
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-column-select-checkbox {
  display: flex;
}

.ag-tool-panel-horizontal-resize {
  cursor: ew-resize;
  height: 100%;
  position: absolute;
  top: 0;
  width: 5px;
  z-index: 1;
}

.ag-ltr .ag-side-bar-left .ag-tool-panel-horizontal-resize {
  right: -3px;
}
.ag-rtl .ag-side-bar-left .ag-tool-panel-horizontal-resize {
  left: -3px;
}

.ag-ltr .ag-side-bar-right .ag-tool-panel-horizontal-resize {
  left: -3px;
}
.ag-rtl .ag-side-bar-right .ag-tool-panel-horizontal-resize {
  right: -3px;
}

.ag-details-row {
  width: 100%;
}

.ag-details-row-fixed-height {
  height: 100%;
}

.ag-details-grid {
  width: 100%;
}

.ag-details-grid-fixed-height {
  height: 100%;
}

.ag-header-group-cell {
  display: flex;
  align-items: center;
  height: 100%;
  position: absolute;
}

.ag-header-group-cell-no-group.ag-header-span-height {
  display: none;
}

.ag-cell-label-container {
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
  align-items: center;
  height: 100%;
  width: 100%;
}

.ag-header-group-cell-label,
.ag-cell-label-container {
  padding: 5px 0px;
}

.ag-right-aligned-header .ag-cell-label-container {
  flex-direction: row;
}
.ag-right-aligned-header .ag-header-cell-text {
  text-align: end;
}

.ag-side-bar {
  display: flex;
  flex-direction: row-reverse;
}

.ag-side-bar-left {
  order: -1;
  flex-direction: row;
}

.ag-side-button-button {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  white-space: nowrap;
  outline: none;
  cursor: pointer;
}

.ag-side-button-label {
  writing-mode: vertical-lr;
}

.ag-status-bar {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
}

.ag-status-panel {
  display: inline-flex;
}

.ag-status-name-value {
  white-space: nowrap;
}

.ag-status-bar-left {
  display: inline-flex;
}

.ag-status-bar-center {
  display: inline-flex;
}

.ag-status-bar-right {
  display: inline-flex;
}

.ag-icon {
  display: block;
  speak: none;
}

.ag-group {
  position: relative;
  width: 100%;
}

.ag-group-title-bar {
  display: flex;
  align-items: center;
}

.ag-group-title {
  display: inline;
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ag-group-title-bar .ag-group-title {
  cursor: default;
}

.ag-group-toolbar {
  display: flex;
  align-items: center;
}

.ag-group-container {
  display: flex;
}

.ag-disabled .ag-group-container {
  pointer-events: none;
}

.ag-group-container-horizontal {
  flex-direction: row;
  flex-wrap: wrap;
}

.ag-group-container-vertical {
  flex-direction: column;
}

.ag-column-group-icons {
  display: block;
}
.ag-column-group-icons > * {
  cursor: pointer;
}

.ag-group-item-alignment-stretch .ag-group-item {
  align-items: stretch;
}

.ag-group-item-alignment-start .ag-group-item {
  align-items: flex-start;
}

.ag-group-item-alignment-end .ag-group-item {
  align-items: flex-end;
}

.ag-toggle-button-icon {
  transition: right 0.3s;
  position: absolute;
  top: -1px;
}

.ag-input-field,
.ag-select {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.ag-input-field-input {
  flex: 1 1 auto;
}

.ag-floating-filter-input .ag-input-field-input[type=date] {
  width: 1px;
}

.ag-range-field {
  display: flex;
  align-items: center;
}

.ag-angle-select {
  display: flex;
  align-items: center;
}

.ag-angle-select-wrapper {
  display: flex;
}

.ag-angle-select-parent-circle {
  display: block;
  position: relative;
}

.ag-angle-select-child-circle {
  position: absolute;
}

.ag-slider-wrapper {
  display: flex;
}
.ag-slider-wrapper .ag-input-field {
  flex: 1 1 auto;
}

.ag-picker-field-display {
  flex: 1 1 auto;
}

.ag-picker-field {
  display: flex;
  align-items: center;
}

.ag-picker-field-icon {
  display: flex;
  border: 0;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.ag-picker-field-wrapper {
  overflow: hidden;
}

.ag-label-align-right .ag-label {
  order: 1;
}
.ag-label-align-right > * {
  flex: none;
}

.ag-label-align-top {
  flex-direction: column;
  align-items: flex-start;
}
.ag-label-align-top > * {
  align-self: stretch;
}

.ag-label-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.ag-color-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.ag-spectrum-color {
  flex: 1 1 auto;
  position: relative;
  overflow: visible;
  cursor: default;
}

.ag-spectrum-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ag-spectrum-val {
  cursor: pointer;
}

.ag-spectrum-dragger {
  position: absolute;
  pointer-events: none;
  cursor: pointer;
}

.ag-spectrum-hue,
.ag-spectrum-alpha {
  cursor: default;
}

.ag-spectrum-hue-background {
  background: linear-gradient(to left, #ff0000 3%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
  width: 100%;
  height: 100%;
}

.ag-spectrum-alpha {
  --ag-spectrum-alpha-background-checked: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="4" height="4"><rect x="0" y="0" width="4" height="4" fill="%23fff"/><path d="M0 0H2V4H4V2H0Z" fill="%23b2b2b2"/></svg>');
}

.ag-spectrum-alpha-background {
  background: linear-gradient(to right, var(--ag-internal-spectrum-alpha-color-from), var(--ag-internal-spectrum-alpha-color-to)), var(--ag-spectrum-alpha-background-checked) top left/4px 4px;
  width: 100%;
  height: 100%;
}

.ag-spectrum-tool {
  cursor: pointer;
  position: relative;
}

.ag-spectrum-slider {
  position: absolute;
  pointer-events: none;
}

.ag-spectrum-alpha .ag-spectrum-slider {
  background: linear-gradient(to bottom, var(--ag-internal-spectrum-alpha-color), var(--ag-internal-spectrum-alpha-color)) white;
}

.ag-recent-colors {
  display: flex;
}

.ag-recent-color {
  cursor: pointer;
}

.ag-pill-select {
  display: flex;
  flex-direction: column;
}
.ag-pill-select .ag-column-drop {
  flex: unset;
}

.ag-ltr {
  direction: ltr;
}
.ag-ltr .ag-body,
.ag-ltr .ag-floating-top,
.ag-ltr .ag-floating-bottom,
.ag-ltr .ag-header,
.ag-ltr .ag-sticky-top,
.ag-ltr .ag-sticky-bottom,
.ag-ltr .ag-body-viewport,
.ag-ltr .ag-body-horizontal-scroll {
  flex-direction: row;
}

.ag-rtl {
  direction: rtl;
}
.ag-rtl .ag-body,
.ag-rtl .ag-floating-top,
.ag-rtl .ag-floating-bottom,
.ag-rtl .ag-header,
.ag-rtl .ag-sticky-top,
.ag-rtl .ag-sticky-bottom,
.ag-rtl .ag-body-viewport,
.ag-rtl .ag-body-horizontal-scroll {
  flex-direction: row-reverse;
}
.ag-rtl .ag-icon-contracted,
.ag-rtl .ag-icon-expanded,
.ag-rtl .ag-icon-tree-closed {
  display: block;
  transform: rotate(180deg);
}

.ag-body .ag-body-viewport {
  -webkit-overflow-scrolling: touch;
}

.ag-measurement-container {
  width: 0;
  overflow: hidden;
  visibility: hidden;
}
.ag-measurement-container div {
  position: absolute;
}

.ag-layout-print.ag-body {
  display: block;
  height: unset;
}
.ag-layout-print.ag-root-wrapper {
  display: inline-block;
}
.ag-layout-print .ag-body-vertical-scroll {
  display: none;
}
.ag-layout-print .ag-body-horizontal-scroll {
  display: none;
}
.ag-layout-print.ag-force-vertical-scroll {
  overflow-y: visible !important;
}

@media print {
  .ag-root-wrapper.ag-layout-print {
    display: table;
  }
  .ag-root-wrapper.ag-layout-print .ag-root-wrapper-body,
  .ag-root-wrapper.ag-layout-print .ag-root,
  .ag-root-wrapper.ag-layout-print .ag-body-viewport,
  .ag-root-wrapper.ag-layout-print .ag-center-cols-container,
  .ag-root-wrapper.ag-layout-print .ag-center-cols-viewport,
  .ag-root-wrapper.ag-layout-print .ag-body-horizontal-scroll-viewport,
  .ag-root-wrapper.ag-layout-print .ag-virtual-list-viewport {
    height: auto !important;
    overflow: hidden !important;
    display: block !important;
  }
  .ag-root-wrapper.ag-layout-print .ag-row,
  .ag-root-wrapper.ag-layout-print .ag-cell {
    break-inside: avoid;
  }
}
[class^=ag-],
[class^=ag-]:focus,
[class^=ag-]:after,
[class^=ag-]:before {
  box-sizing: border-box;
  outline: none;
}

[class^=ag-]::-ms-clear {
  display: none;
}

.ag-checkbox .ag-input-wrapper,
.ag-radio-button .ag-input-wrapper {
  overflow: visible;
}

.ag-range-field .ag-input-wrapper {
  height: 100%;
}

.ag-toggle-button {
  flex: none;
  width: unset;
  min-width: unset;
}

.ag-button {
  border-radius: 0px;
  color: var(--ag-foreground-color);
}

.ag-button:hover {
  background-color: transparent;
}

.ag-ltr .ag-label-align-right .ag-label {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-label-align-right .ag-label {
  margin-right: var(--ag-grid-size);
}

input[class^=ag-] {
  margin: 0;
  background-color: var(--ag-background-color);
}

textarea[class^=ag-],
select[class^=ag-] {
  background-color: var(--ag-background-color);
}

input[class^=ag-]:not([type]),
input[class^=ag-][type=text],
input[class^=ag-][type=number],
input[class^=ag-][type=tel],
input[class^=ag-][type=date],
input[class^=ag-][type=datetime-local],
textarea[class^=ag-] {
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  font-family: inherit;
  border: var(--ag-borders-input) var(--ag-input-border-color);
}
input[class^=ag-]:not([type]):disabled,
input[class^=ag-][type=text]:disabled,
input[class^=ag-][type=number]:disabled,
input[class^=ag-][type=tel]:disabled,
input[class^=ag-][type=date]:disabled,
input[class^=ag-][type=datetime-local]:disabled,
textarea[class^=ag-]:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
input[class^=ag-]:not([type]):focus,
input[class^=ag-][type=text]:focus,
input[class^=ag-][type=number]:focus,
input[class^=ag-][type=tel]:focus,
input[class^=ag-][type=date]:focus,
input[class^=ag-][type=datetime-local]:focus,
textarea[class^=ag-]:focus {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-input-focus-border-color);
}
input[class^=ag-]:not([type]):invalid,
input[class^=ag-][type=text]:invalid,
input[class^=ag-][type=number]:invalid,
input[class^=ag-][type=tel]:invalid,
input[class^=ag-][type=date]:invalid,
input[class^=ag-][type=datetime-local]:invalid,
textarea[class^=ag-]:invalid {
  border: var(--ag-borders-input-invalid) var(--ag-input-border-color-invalid);
}

input[class^=ag-][type=number]:not(.ag-number-field-input-stepper) {
  -moz-appearance: textfield;
}
input[class^=ag-][type=number]:not(.ag-number-field-input-stepper)::-webkit-outer-spin-button, input[class^=ag-][type=number]:not(.ag-number-field-input-stepper)::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[class^=ag-][type=range] {
  padding: 0;
}

input[class^=ag-][type=button]:focus,
button[class^=ag-]:focus {
  box-shadow: var(--ag-input-focus-box-shadow);
}

.ag-drag-handle {
  color: var(--ag-secondary-foreground-color);
}

.ag-list-item,
.ag-virtual-list-item {
  height: var(--ag-list-item-height);
}

.ag-virtual-list-item:focus-visible {
  outline: none;
}
.ag-virtual-list-item:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-select-list {
  background-color: var(--ag-background-color);
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: var(--ag-border-radius);
  border: var(--ag-borders) var(--ag-border-color);
}

.ag-list-item {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-list-item.ag-active-item {
  background-color: var(--ag-row-hover-color);
}

.ag-select-list-item {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}
.ag-ltr .ag-select-list-item {
  padding-left: calc(var(--ag-cell-horizontal-padding) / 2);
}
.ag-rtl .ag-select-list-item {
  padding-right: calc(var(--ag-cell-horizontal-padding) / 2);
}
.ag-select-list-item span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ag-row-drag,
.ag-selection-checkbox,
.ag-group-expanded,
.ag-group-contracted {
  color: var(--ag-secondary-foreground-color);
}
.ag-ltr .ag-row-drag,
.ag-ltr .ag-selection-checkbox,
.ag-ltr .ag-group-expanded,
.ag-ltr .ag-group-contracted {
  margin-right: var(--ag-cell-widget-spacing);
}
.ag-rtl .ag-row-drag,
.ag-rtl .ag-selection-checkbox,
.ag-rtl .ag-group-expanded,
.ag-rtl .ag-group-contracted {
  margin-left: var(--ag-cell-widget-spacing);
}

.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value) {
  --ag-internal-calculated-line-height: var(
      --ag-line-height,
      calc(var(--ag-row-height) - var(--ag-row-border-width))
  );
  --ag-internal-padded-row-height: calc(var(--ag-row-height) - var(--ag-row-border-width));
  height: min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
  display: flex;
  align-items: center;
  flex: none;
}

.ag-group-expanded,
.ag-group-contracted {
  cursor: pointer;
}

.ag-group-title-bar-icon {
  cursor: pointer;
  flex: none;
  color: var(--ag-secondary-foreground-color);
}

.ag-ltr .ag-group-child-count {
  margin-left: 2px;
}
.ag-rtl .ag-group-child-count {
  margin-right: 2px;
}

.ag-group-title-bar {
  background-color: var(--ag-subheader-background-color);
  padding: var(--ag-grid-size);
}

.ag-group-toolbar {
  padding: var(--ag-grid-size);
  background-color: var(--ag-subheader-toolbar-background-color);
}

.ag-disabled-group-title-bar,
.ag-disabled-group-container {
  opacity: 0.5;
}

.group-item {
  margin: calc(var(--ag-grid-size) * 0.5) 0;
}

.ag-label {
  white-space: nowrap;
}
.ag-ltr .ag-label {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-label {
  margin-left: var(--ag-grid-size);
}

.ag-label-align-top .ag-label {
  margin-bottom: calc(var(--ag-grid-size) * 0.5);
}

.ag-angle-select[disabled] {
  color: var(--ag-disabled-foreground-color);
  pointer-events: none;
}
.ag-angle-select[disabled] .ag-angle-select-field {
  opacity: 0.4;
}

.ag-ltr .ag-slider-field,
.ag-ltr .ag-angle-select-field {
  margin-right: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-slider-field,
.ag-rtl .ag-angle-select-field {
  margin-left: calc(var(--ag-grid-size) * 2);
}

.ag-angle-select-parent-circle {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: solid 1px;
  border-color: var(--ag-border-color);
  background-color: var(--ag-background-color);
}

.ag-angle-select-child-circle {
  top: 4px;
  left: 12px;
  width: 6px;
  height: 6px;
  margin-left: -3px;
  margin-top: -4px;
  border-radius: 3px;
  background-color: var(--ag-secondary-foreground-color);
}

.ag-picker-field-wrapper {
  border: var(--ag-borders);
  border-color: var(--ag-border-color);
  border-radius: 5px;
  background-color: var(--ag-background-color);
}
.ag-picker-field-wrapper:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
.ag-picker-field-wrapper.ag-picker-has-focus, .ag-picker-field-wrapper:focus-within {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-input-focus-border-color);
}

.ag-picker-field-button {
  background-color: var(--ag-background-color);
  color: var(--ag-secondary-foreground-color);
}

.ag-dialog.ag-color-dialog {
  border-radius: 5px;
}

.ag-color-picker .ag-picker-field-wrapper {
  padding-left: var(--ag-grid-size);
  padding-right: var(--ag-grid-size);
}
.ag-color-picker .ag-picker-field-display {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-height: var(--ag-list-item-height);
}

.ag-ltr .ag-color-picker-color,
.ag-ltr .ag-color-picker-value {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-color-picker-color,
.ag-rtl .ag-color-picker-value {
  margin-left: var(--ag-grid-size);
}

.ag-color-panel {
  padding: var(--ag-grid-size);
}

.ag-spectrum-color {
  background-color: rgb(255, 0, 0);
  border-radius: 2px;
}

.ag-spectrum-tools {
  padding: 10px;
}

.ag-spectrum-sat {
  background-image: linear-gradient(to right, white, rgba(204, 154, 129, 0));
}

.ag-spectrum-val {
  background-image: linear-gradient(to top, black, rgba(204, 154, 129, 0));
}

.ag-spectrum-dragger {
  border-radius: 12px;
  height: 12px;
  width: 12px;
  border: 2px solid white;
  background: black;
  box-shadow: 0 0 2px 0px rgba(0, 0, 0, 0.24);
}

.ag-spectrum-hue-background {
  border-radius: 2px;
}

.ag-spectrum-alpha-background {
  border-radius: 2px;
}

.ag-spectrum-tool {
  margin-bottom: 10px;
  height: 11px;
  border-radius: 2px;
}

.ag-spectrum-slider {
  margin-top: -12px;
  width: 13px;
  height: 13px;
  border-radius: 13px;
  border: 2px solid white;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.37);
}

.ag-recent-colors {
  margin-top: 10px;
}

.ag-recent-color {
  margin: 0 3px;
}
.ag-recent-color:first-child {
  margin-left: 0;
}
.ag-recent-color:last-child {
  margin-right: 0;
}

.ag-spectrum-color:focus-visible:not(:disabled):not([readonly]),
.ag-spectrum-slider:focus-visible:not(:disabled):not([readonly]),
.ag-recent-color:focus-visible:not(:disabled):not([readonly]) {
  box-shadow: var(--ag-input-focus-box-shadow);
}

.ag-ltr .ag-color-input input[class^=ag-][type=text].ag-input-field-input {
  padding-left: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
}
.ag-rtl .ag-color-input input[class^=ag-][type=text].ag-input-field-input {
  padding-right: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
}
.ag-color-input .ag-color-input-color {
  position: absolute;
}
.ag-ltr .ag-color-input .ag-color-input-color {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-color-input .ag-color-input-color {
  margin-right: var(--ag-grid-size);
}

.ag-color-picker-color,
.ag-color-input-color {
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  border: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  border-radius: 2px;
}

.ag-dnd-ghost {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  overflow: hidden;
  text-overflow: ellipsis;
  border: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  color: var(--ag-secondary-foreground-color);
  height: var(--ag-header-height);
  line-height: var(--ag-header-height);
  margin: 0;
  padding: 0 calc(var(--ag-grid-size) * 2);
  transform: translateY(calc(var(--ag-grid-size) * 2));
}

.ag-dnd-ghost-icon {
  margin-right: var(--ag-grid-size);
  color: var(--ag-foreground-color);
}

.ag-popup-child:not(.ag-tooltip-custom) {
  box-shadow: var(--ag-popup-shadow);
}

.ag-select .ag-picker-field-wrapper {
  min-height: var(--ag-list-item-height);
  cursor: default;
}
.ag-ltr .ag-select .ag-picker-field-wrapper {
  padding-left: calc(var(--ag-cell-horizontal-padding) / 2);
}
.ag-rtl .ag-select .ag-picker-field-wrapper {
  padding-right: calc(var(--ag-cell-horizontal-padding) / 2);
}
.ag-ltr .ag-select .ag-picker-field-wrapper {
  padding-right: var(--ag-grid-size);
}
.ag-rtl .ag-select .ag-picker-field-wrapper {
  padding-left: var(--ag-grid-size);
}
.ag-select.ag-disabled .ag-picker-field-wrapper:focus {
  box-shadow: none;
}
.ag-select:not(.ag-cell-editor, .ag-label-align-top) {
  min-height: var(--ag-list-item-height);
}
.ag-select .ag-picker-field-display {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-select .ag-picker-field-icon {
  display: flex;
  align-items: center;
}
.ag-select.ag-disabled {
  opacity: 0.5;
}

.ag-rich-select-value,
.ag-rich-select-list {
  background-color: var(--ag-background-color);
}

.ag-rich-select-list {
  width: 100%;
  height: auto;
  border-radius: var(--ag-border-radius);
  border: var(--ag-borders) var(--ag-border-color);
}
.ag-rich-select-list .ag-loading-text {
  padding: var(--ag-widget-vertical-spacing) var(--ag-widget-horizontal-spacing);
}

.ag-rich-select-value {
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  padding-top: 0;
  padding-bottom: 0;
}
.ag-ltr .ag-rich-select-value {
  padding-left: calc(var(--ag-cell-horizontal-padding) / 2);
}
.ag-rtl .ag-rich-select-value {
  padding-right: calc(var(--ag-cell-horizontal-padding) / 2);
}
.ag-ltr .ag-rich-select-value {
  padding-right: var(--ag-grid-size);
}
.ag-rtl .ag-rich-select-value {
  padding-left: var(--ag-grid-size);
}

.ag-ltr .ag-rich-select-field-input {
  left: calc(var(--ag-cell-horizontal-padding));
}
.ag-rtl .ag-rich-select-field-input {
  right: calc(var(--ag-cell-horizontal-padding));
}

.ag-popup-editor .ag-rich-select-value {
  height: var(--ag-row-height);
  min-width: 200px;
}

.ag-rich-select-virtual-list-item {
  cursor: default;
  height: var(--ag-list-item-height);
}
.ag-rich-select-virtual-list-item:focus-visible::after {
  content: none;
}

.ag-ltr .ag-rich-select-row {
  padding-left: calc(var(--ag-cell-horizontal-padding) / 2);
}
.ag-rtl .ag-rich-select-row {
  padding-right: calc(var(--ag-cell-horizontal-padding) / 2);
}

.ag-rich-select-row-selected {
  background-color: var(--ag-selected-row-background-color);
}

.ag-rich-select-row:hover,
.ag-rich-select-row-highlighted {
  background-image: linear-gradient(var(--ag-row-hover-color), var(--ag-row-hover-color));
}

.ag-rich-select-row-text-highlight {
  font-weight: bold;
}

.ag-autocomplete {
  width: 100%;
}

.ag-autocomplete-list {
  width: 100%;
  min-width: 200px;
  height: calc(var(--ag-row-height) * 6.5);
}

.ag-autocomplete-virtual-list-item {
  cursor: default;
  height: var(--ag-list-item-height);
}
.ag-autocomplete-virtual-list-item:focus-visible::after {
  content: none;
}
.ag-autocomplete-virtual-list-item:hover {
  background-color: var(--ag-row-hover-color);
}

.ag-autocomplete-row-label {
  margin: 0px var(--ag-widget-container-horizontal-padding);
}

.ag-autocomplete-row-selected {
  background-color: var(--ag-selected-row-background-color);
}

.ag-pill {
  border: 1px solid var(--ag-chip-border-color);
  border-radius: var(--ag-border-radius);
  background-color: var(--ag-chip-background-color);
}

.ag-ltr .ag-pill .ag-pill-button {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-pill .ag-pill-button {
  margin-right: var(--ag-grid-size);
}

.ag-pill:focus-visible {
  border-color: var(--ag-input-focus-border-color);
}

.ag-pill .ag-pill-button:hover {
  cursor: pointer;
}

.ag-dragging-range-handle .ag-dialog,
.ag-dragging-fill-handle .ag-dialog {
  opacity: 0.7;
  pointer-events: none;
}

.ag-dialog {
  border-radius: var(--ag-border-radius);
  border: var(--ag-borders) var(--ag-border-color);
  box-shadow: var(--ag-popup-shadow);
}

.ag-panel {
  background-color: var(--ag-panel-background-color);
  border-color: var(--ag-panel-border-color);
}

.ag-panel-title-bar {
  color: var(--ag-header-foreground-color);
  height: var(--ag-header-height);
  padding: var(--ag-grid-size) var(--ag-cell-horizontal-padding);
  border-bottom: var(--ag-borders) var(--ag-border-color);
}

.ag-ltr .ag-panel-title-bar-button {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-panel-title-bar-button {
  margin-right: var(--ag-grid-size);
}

.ag-tooltip {
  background-color: var(--ag-tooltip-background-color);
  color: var(--ag-foreground-color);
  padding: var(--ag-grid-size);
  border: var(--ag-borders) var(--ag-border-color);
  border-radius: var(--ag-card-radius);
  white-space: normal;
}

.ag-tooltip.ag-tooltip-animate,
.ag-tooltip-custom.ag-tooltip-animate {
  transition: opacity 1s;
}
.ag-tooltip.ag-tooltip-animate.ag-tooltip-hiding,
.ag-tooltip-custom.ag-tooltip-animate.ag-tooltip-hiding {
  opacity: 0;
}

.ag-ltr .ag-column-select-column,
.ag-ltr .ag-column-select-column-group {
  padding-left: calc(var(--ag-indentation-level) * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-column,
.ag-rtl .ag-column-select-column-group {
  padding-right: calc(var(--ag-indentation-level) * var(--ag-column-select-indent-size));
}

.ag-column-select-header-icon {
  cursor: pointer;
}

.ag-column-select-header-icon:focus-visible {
  outline: none;
}
.ag-column-select-header-icon:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 0px;
  left: 0px;
  display: block;
  width: calc(100% - 0px);
  height: calc(100% - 0px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-ltr .ag-column-group-icons:not(:last-child),
.ag-ltr .ag-column-select-header-icon:not(:last-child),
.ag-ltr .ag-column-select-header-checkbox:not(:last-child),
.ag-ltr .ag-column-select-header-filter-wrapper:not(:last-child),
.ag-ltr .ag-column-select-checkbox:not(:last-child),
.ag-ltr .ag-column-select-column-drag-handle:not(:last-child),
.ag-ltr .ag-column-select-column-group-drag-handle:not(:last-child),
.ag-ltr .ag-column-select-column-label:not(:last-child) {
  margin-right: var(--ag-widget-horizontal-spacing);
}
.ag-rtl .ag-column-group-icons:not(:last-child),
.ag-rtl .ag-column-select-header-icon:not(:last-child),
.ag-rtl .ag-column-select-header-checkbox:not(:last-child),
.ag-rtl .ag-column-select-header-filter-wrapper:not(:last-child),
.ag-rtl .ag-column-select-checkbox:not(:last-child),
.ag-rtl .ag-column-select-column-drag-handle:not(:last-child),
.ag-rtl .ag-column-select-column-group-drag-handle:not(:last-child),
.ag-rtl .ag-column-select-column-label:not(:last-child) {
  margin-left: var(--ag-widget-horizontal-spacing);
}

.ag-column-select-virtual-list-item:focus-visible {
  outline: none;
}
.ag-column-select-virtual-list-item:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-column-select-column-group:not(:last-child),
.ag-column-select-column:not(:last-child) {
  margin-bottom: var(--ag-widget-vertical-spacing);
}

.ag-column-select-column-readonly,
.ag-column-select-column-group-readonly {
  color: var(--ag-disabled-foreground-color);
  pointer-events: none;
}

.ag-ltr .ag-column-select-add-group-indent {
  margin-left: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
}
.ag-rtl .ag-column-select-add-group-indent {
  margin-right: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
}

.ag-column-select-virtual-list-viewport {
  padding: calc(var(--ag-widget-container-vertical-padding) * 0.5) 0px;
}

.ag-column-select-virtual-list-item {
  padding: 0 var(--ag-widget-container-horizontal-padding);
}

.ag-checkbox-edit {
  padding-left: var(--ag-cell-horizontal-padding);
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-pill-select .ag-column-drop {
  border-bottom: 0;
  min-height: unset;
}
.ag-pill-select .ag-column-drop-list {
  padding: 0;
}
.ag-pill-select .ag-select {
  padding-top: var(--ag-grid-size);
}
.ag-pill-select .ag-picker-field-wrapper {
  background-color: transparent;
  border: 0;
}
.ag-pill-select .ag-picker-field-display {
  cursor: pointer;
}

.ag-rtl {
  text-align: right;
}

.ag-root-wrapper {
  border-radius: var(--ag-wrapper-border-radius);
  border: var(--ag-borders) var(--ag-border-color);
}

.ag-row > .ag-cell-wrapper.ag-row-group {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * var(--ag-indentation-level));
}

.ag-cell-wrapper.ag-row-group,
.ag-cell-wrapper.ag-row-group-leaf-indent,
.ag-cell-wrapper.ag-pivot-leaf-group {
  padding-left: calc(var(--ag-indentation-level) * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-group-leaf-indent {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-group-leaf-indent {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-row:not(.ag-row-level-0) .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}

.ag-value-change-delta {
  padding-right: 2px;
}

.ag-value-change-delta-up {
  color: var(--ag-value-change-delta-up-color);
}

.ag-value-change-delta-down {
  color: var(--ag-value-change-delta-down-color);
}

.ag-value-change-value {
  background-color: transparent;
  border-radius: 1px;
  padding-left: 1px;
  padding-right: 1px;
  transition: background-color 1s;
}

.ag-value-change-value-highlight {
  background-color: var(--ag-value-change-value-highlight-background-color);
  transition: background-color 0.1s;
}

.ag-cell-data-changed {
  background-color: var(--ag-value-change-value-highlight-background-color) !important;
}

.ag-cell-data-changed-animation {
  background-color: transparent;
}

.ag-cell-highlight {
  background-color: var(--ag-range-selection-highlight-color) !important;
}

.ag-row {
  height: var(--ag-row-height);
  background-color: var(--ag-background-color);
  color: var(--ag-data-color);
  border-bottom: var(--ag-row-border-style) var(--ag-row-border-color) var(--ag-row-border-width);
}

.ag-row-highlight-above::after,
.ag-row-highlight-below::after {
  content: "";
  position: absolute;
  width: calc(100% - 1px);
  height: 1px;
  background-color: var(--ag-range-selection-border-color);
  left: 1px;
}

.ag-row-highlight-above::after {
  top: 0px;
}

.ag-row-highlight-below::after {
  bottom: 0px;
}

.ag-row-odd {
  background-color: var(--ag-odd-row-background-color);
}

.ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-left-spacer:not(.ag-scroller-corner) {
  border-right: var(--ag-borders-critical) var(--ag-border-color);
}
.ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-right-spacer:not(.ag-scroller-corner) {
  border-left: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-row-selected::before {
  content: "";
  background-color: var(--ag-selected-row-background-color);
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ag-row-hover:not(.ag-full-width-row)::before,
.ag-row-hover.ag-full-width-row.ag-row-group::before {
  content: "";
  background-color: var(--ag-row-hover-color);
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.ag-row-hover.ag-full-width-row.ag-row-group > * {
  position: relative;
}

.ag-row-hover.ag-row-selected::before {
  background-color: var(--ag-row-hover-color);
  background-image: linear-gradient(var(--ag-selected-row-background-color), var(--ag-selected-row-background-color));
}

.ag-column-hover {
  background-color: var(--ag-column-hover-color);
}

.ag-ltr .ag-right-aligned-cell {
  text-align: right;
}
.ag-rtl .ag-right-aligned-cell {
  text-align: left;
}

.ag-ltr .ag-right-aligned-cell .ag-cell-value,
.ag-ltr .ag-right-aligned-cell .ag-group-value {
  margin-left: auto;
}
.ag-rtl .ag-right-aligned-cell .ag-cell-value,
.ag-rtl .ag-right-aligned-cell .ag-group-value {
  margin-right: auto;
}

.ag-ltr .ag-right-aligned-cell .ag-skeleton-effect {
  margin-left: auto;
}
.ag-rtl .ag-right-aligned-cell .ag-skeleton-effect {
  margin-right: auto;
}

.ag-cell,
.ag-full-width-row .ag-cell-wrapper.ag-row-group {
  --ag-internal-calculated-line-height: var(
      --ag-line-height,
      calc(var(--ag-row-height) - var(--ag-row-border-width))
  );
  --ag-internal-padded-row-height: calc(var(--ag-row-height) - var(--ag-row-border-width));
  border: 1px solid transparent;
  line-height: min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
  padding-left: calc(var(--ag-cell-horizontal-padding) - 1px + var(--ag-row-group-indent-size) * var(--ag-indentation-level));
  padding-right: calc(var(--ag-cell-horizontal-padding) - 1px);
  -webkit-font-smoothing: subpixel-antialiased;
}

.ag-row > .ag-cell-wrapper {
  padding-left: calc(var(--ag-cell-horizontal-padding) - 1px);
  padding-right: calc(var(--ag-cell-horizontal-padding) - 1px);
}

.ag-row-dragging {
  cursor: move;
  opacity: 0.5;
}

.ag-cell-inline-editing {
  border: 1px solid var(--ag-border-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: 0;
  background-color: var(--ag-control-panel-background-color);
}

.ag-popup-editor .ag-large-text,
.ag-autocomplete-list-popup {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  background-color: var(--ag-control-panel-background-color);
  padding: 0;
}

.ag-large-text-input {
  height: auto;
  padding: var(--ag-cell-horizontal-padding);
}

.ag-rtl .ag-large-text-input textarea {
  resize: none;
}

.ag-details-row {
  padding: calc(var(--ag-grid-size) * 5);
  background-color: var(--ag-background-color);
}

.ag-layout-auto-height .ag-center-cols-viewport,
.ag-layout-auto-height .ag-center-cols-container,
.ag-layout-print .ag-center-cols-viewport,
.ag-layout-print .ag-center-cols-container {
  min-height: 50px;
}

.ag-overlay-loading-wrapper {
  background-color: var(--ag-modal-overlay-background-color);
}

.ag-overlay-loading-center {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
}

.ag-skeleton-container {
  width: 100%;
  height: 100%;
  align-content: center;
}

.ag-skeleton-effect {
  background-color: var(--ag-row-loading-skeleton-effect-color);
  width: 100%;
  height: 1em;
  border-radius: 0.25rem;
  animation: ag-skeleton-loading 1.5s ease-in-out 0.5s infinite;
}

@keyframes ag-skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}
.ag-loading {
  display: flex;
  height: 100%;
  align-items: center;
}
.ag-ltr .ag-loading {
  padding-left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-loading {
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-ltr .ag-loading-icon {
  padding-right: var(--ag-cell-widget-spacing);
}
.ag-rtl .ag-loading-icon {
  padding-left: var(--ag-cell-widget-spacing);
}

.ag-icon-loading {
  animation-name: spin;
  animation-duration: 1000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.ag-floating-top {
  border-bottom: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-floating-bottom {
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-ltr .ag-cell {
  border-right: var(--ag-cell-horizontal-border);
}
.ag-rtl .ag-cell {
  border-left: var(--ag-cell-horizontal-border);
}
.ag-ltr .ag-cell {
  border-right-width: 1px;
}
.ag-rtl .ag-cell {
  border-left-width: 1px;
}

.ag-cell.ag-cell-first-right-pinned:not(.ag-cell-range-left):not(.ag-cell-range-single-cell) {
  border-left: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
  border-right: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-cell-range-selected:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing) {
  background-color: var(--ag-range-selection-background-color);
}
.ag-cell-range-selected:not(.ag-cell-focus).ag-cell-range-chart,
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing).ag-cell-range-chart {
  background-color: var(--ag-range-selection-chart-background-color) !important;
}
.ag-cell-range-selected:not(.ag-cell-focus).ag-cell-range-chart.ag-cell-range-chart-category,
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing).ag-cell-range-chart.ag-cell-range-chart-category {
  background-color: var(--ag-range-selection-chart-category-background-color) !important;
}

.ag-cell-range-selected-1:not(.ag-cell-focus),
.ag-root:not(.ag-context-menu-open) .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-1:not(.ag-cell-inline-editing) {
  background-color: var(--ag-range-selection-background-color);
}

.ag-cell-range-selected-2:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-2 {
  background-color: var(--ag-range-selection-background-color-2);
}

.ag-cell-range-selected-3:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-3 {
  background-color: var(--ag-range-selection-background-color-3);
}

.ag-cell-range-selected-4:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-4 {
  background-color: var(--ag-range-selection-background-color-4);
}

.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-top {
  border-top-color: var(--ag-range-selection-border-color);
  border-top-style: var(--ag-range-selection-border-style);
}
.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-right {
  border-right-color: var(--ag-range-selection-border-color);
  border-right-style: var(--ag-range-selection-border-style);
}
.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-bottom {
  border-bottom-color: var(--ag-range-selection-border-color);
  border-bottom-style: var(--ag-range-selection-border-style);
}
.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-left {
  border-left-color: var(--ag-range-selection-border-color);
  border-left-style: var(--ag-range-selection-border-style);
}

.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-ltr .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-ltr .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-ltr .ag-cell-range-single-cell,
.ag-ltr .ag-cell-range-single-cell.ag-cell-range-handle,
.ag-rtl .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-rtl .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-rtl .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-rtl .ag-cell-range-single-cell,
.ag-rtl .ag-cell-range-single-cell.ag-cell-range-handle {
  border: 1px solid;
  border-color: var(--ag-range-selection-border-color);
  border-style: var(--ag-range-selection-border-style);
  outline: initial;
}

.ag-cell.ag-selection-fill-top,
.ag-cell.ag-selection-fill-top.ag-cell-range-selected {
  border-top: 1px dashed;
  border-top-color: var(--ag-range-selection-border-color);
}

.ag-ltr .ag-cell.ag-selection-fill-right,
.ag-ltr .ag-cell.ag-selection-fill-right.ag-cell-range-selected {
  border-right: 1px dashed var(--ag-range-selection-border-color) !important;
}
.ag-rtl .ag-cell.ag-selection-fill-right,
.ag-rtl .ag-cell.ag-selection-fill-right.ag-cell-range-selected {
  border-left: 1px dashed var(--ag-range-selection-border-color) !important;
}

.ag-cell.ag-selection-fill-bottom,
.ag-cell.ag-selection-fill-bottom.ag-cell-range-selected {
  border-bottom: 1px dashed;
  border-bottom-color: var(--ag-range-selection-border-color);
}

.ag-ltr .ag-cell.ag-selection-fill-left,
.ag-ltr .ag-cell.ag-selection-fill-left.ag-cell-range-selected {
  border-left: 1px dashed var(--ag-range-selection-border-color) !important;
}
.ag-rtl .ag-cell.ag-selection-fill-left,
.ag-rtl .ag-cell.ag-selection-fill-left.ag-cell-range-selected {
  border-right: 1px dashed var(--ag-range-selection-border-color) !important;
}

.ag-fill-handle,
.ag-range-handle {
  position: absolute;
  width: 6px;
  height: 6px;
  bottom: -1px;
  background-color: var(--ag-range-selection-border-color);
}
.ag-ltr .ag-fill-handle,
.ag-ltr .ag-range-handle {
  right: -1px;
}
.ag-rtl .ag-fill-handle,
.ag-rtl .ag-range-handle {
  left: -1px;
}

.ag-fill-handle {
  cursor: cell;
}

.ag-range-handle {
  cursor: nwse-resize;
}

.ag-cell-inline-editing {
  border-color: var(--ag-input-focus-border-color) !important;
}

.ag-menu {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  background-color: var(--ag-menu-background-color);
  border-color: var(--ag-menu-border-color);
  padding: 0;
}

.ag-menu.ag-tabs {
  min-width: var(--ag-tab-min-width);
}

.ag-menu-list {
  cursor: default;
  padding: var(--ag-grid-size) 0;
}

.ag-menu-separator {
  height: calc(var(--ag-grid-size) * 2 + 1px);
}

.ag-menu-separator-part::after {
  content: "";
  display: block;
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-menu-option-active,
.ag-compact-menu-option-active {
  background-color: var(--ag-row-hover-color);
}

.ag-menu-option-part,
.ag-compact-menu-option-part {
  line-height: var(--ag-icon-size);
  padding: calc(var(--ag-grid-size) + 2px) 0;
}

.ag-menu-option-disabled,
.ag-compact-menu-option-disabled {
  opacity: 0.5;
}

.ag-menu-option-icon,
.ag-compact-menu-option-icon {
  width: var(--ag-icon-size);
}
.ag-ltr .ag-menu-option-icon,
.ag-ltr .ag-compact-menu-option-icon {
  padding-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-menu-option-icon,
.ag-rtl .ag-compact-menu-option-icon {
  padding-right: calc(var(--ag-grid-size) * 2);
}

.ag-menu-option-text,
.ag-compact-menu-option-text {
  padding-left: calc(var(--ag-grid-size) * 2);
  padding-right: calc(var(--ag-grid-size) * 2);
}

.ag-ltr .ag-menu-option-shortcut,
.ag-ltr .ag-compact-menu-option-shortcut {
  padding-right: var(--ag-grid-size);
}
.ag-rtl .ag-menu-option-shortcut,
.ag-rtl .ag-compact-menu-option-shortcut {
  padding-left: var(--ag-grid-size);
}

.ag-ltr .ag-menu-option-popup-pointer,
.ag-ltr .ag-compact-menu-option-popup-pointer {
  padding-right: var(--ag-grid-size);
}
.ag-rtl .ag-menu-option-popup-pointer,
.ag-rtl .ag-compact-menu-option-popup-pointer {
  padding-left: var(--ag-grid-size);
}

.ag-tabs-header {
  display: flex;
}

.ag-tabs-header-wrapper {
  display: flex;
}
.ag-tabs-header-wrapper .ag-tabs-header {
  flex: 1;
}

.ag-tabs-close-button-wrapper {
  border: 0;
  border-right: var(--ag-borders) var(--ag-border-color);
  padding: var(--ag-grid-size);
}

.ag-tabs-close-button {
  border: 0;
  background-color: unset;
  cursor: pointer;
  padding: 0;
}

.ag-tab {
  border-bottom: var(--ag-selected-tab-underline-width) solid transparent;
  transition: border-bottom var(--ag-selected-tab-underline-transition-speed);
  display: flex;
  flex: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.ag-tab:focus-visible {
  outline: none;
}
.ag-tab:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-tab-selected {
  border-bottom-color: var(--ag-selected-tab-underline-color);
}

.ag-menu-header {
  color: var(--ag-secondary-foreground-color);
}

.ag-filter-separator {
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-filter-select .ag-picker-field-wrapper {
  width: 0;
}

.ag-filter-condition-operator {
  height: 17px;
}

.ag-ltr .ag-filter-condition-operator-or {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-filter-condition-operator-or {
  margin-right: calc(var(--ag-grid-size) * 2);
}

.ag-set-filter-select-all {
  padding-top: var(--ag-widget-container-vertical-padding);
}

.ag-set-filter-list,
.ag-filter-no-matches {
  height: calc(var(--ag-list-item-height) * 6);
}

.ag-set-filter-tree-list {
  height: calc(var(--ag-list-item-height) * 10);
}

.ag-set-filter-filter {
  margin-top: var(--ag-widget-container-vertical-padding);
  margin-left: var(--ag-widget-container-horizontal-padding);
  margin-right: var(--ag-widget-container-horizontal-padding);
}

.ag-filter-to {
  margin-top: var(--ag-widget-vertical-spacing);
}

.ag-mini-filter {
  margin: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
}

.ag-set-filter {
  --ag-indentation-level: 0;
}

.ag-ltr .ag-set-filter-item {
  padding-left: calc(var(--ag-widget-container-horizontal-padding) + var(--ag-indentation-level) * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-item {
  padding-right: calc(var(--ag-widget-container-horizontal-padding) + var(--ag-indentation-level) * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-add-group-indent {
  margin-left: calc(var(--ag-icon-size) + var(--ag-widget-container-horizontal-padding));
}
.ag-rtl .ag-set-filter-add-group-indent {
  margin-right: calc(var(--ag-icon-size) + var(--ag-widget-container-horizontal-padding));
}

.ag-ltr .ag-set-filter-group-icons {
  margin-right: var(--ag-widget-container-horizontal-padding);
}
.ag-rtl .ag-set-filter-group-icons {
  margin-left: var(--ag-widget-container-horizontal-padding);
}

.ag-filter-menu .ag-set-filter-list {
  min-width: 200px;
}

.ag-filter-virtual-list-item:focus-visible {
  outline: none;
}
.ag-filter-virtual-list-item:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-filter-apply-panel {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-filter-apply-panel-button {
  line-height: 1.5;
}
.ag-ltr .ag-filter-apply-panel-button {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-filter-apply-panel-button {
  margin-right: calc(var(--ag-grid-size) * 2);
}

.ag-simple-filter-body-wrapper {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  padding-bottom: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
  overflow-y: auto;
  min-height: calc(var(--ag-list-item-height) + var(--ag-widget-container-vertical-padding) + var(--ag-widget-vertical-spacing));
}
.ag-simple-filter-body-wrapper > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}
.ag-simple-filter-body-wrapper .ag-resizer-wrapper {
  margin: 0;
}

.ag-menu:not(.ag-tabs) .ag-filter .ag-filter-body-wrapper,
.ag-menu:not(.ag-tabs) .ag-filter > *:not(.ag-filter-wrapper) {
  min-width: calc(var(--ag-menu-min-width) - 2px);
}

.ag-filter-no-matches {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
}

.ag-multi-filter-menu-item {
  margin: var(--ag-grid-size) 0;
}

.ag-multi-filter-group-title-bar {
  padding: calc(var(--ag-grid-size) * 2) var(--ag-grid-size);
  background-color: transparent;
}

.ag-group-filter-field-select-wrapper {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  padding-bottom: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
}
.ag-group-filter-field-select-wrapper > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}

.ag-multi-filter-group-title-bar:focus-visible {
  outline: none;
}
.ag-multi-filter-group-title-bar:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-side-bar {
  position: relative;
}

.ag-tool-panel-wrapper {
  width: var(--ag-side-bar-panel-width);
  background-color: var(--ag-control-panel-background-color);
}

.ag-side-buttons {
  padding-top: calc(var(--ag-grid-size) * 4);
  width: calc(var(--ag-icon-size) + 4px);
  position: relative;
  overflow: hidden;
}

button.ag-side-button-button {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  background: transparent;
  padding: calc(var(--ag-grid-size) * 2) 0 calc(var(--ag-grid-size) * 2) 0;
  width: 100%;
  margin: 0;
  min-height: calc(var(--ag-grid-size) * 18);
  background-position-y: center;
  background-position-x: center;
  background-repeat: no-repeat;
  border: none;
  border-top: var(--ag-borders-side-button) var(--ag-border-color);
  border-bottom: var(--ag-borders-side-button) var(--ag-border-color);
}
button.ag-side-button-button:focus {
  box-shadow: none;
}

.ag-side-button-button:focus-visible {
  outline: none;
}
.ag-side-button-button:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-selected button.ag-side-button-button {
  background-color: var(--ag-side-button-selected-background-color);
}

.ag-side-button-icon-wrapper {
  margin-bottom: 3px;
}

.ag-ltr .ag-side-bar-left,
.ag-rtl .ag-side-bar-right {
  border-right: var(--ag-borders) var(--ag-border-color);
}
.ag-ltr .ag-side-bar-left .ag-tool-panel-wrapper,
.ag-rtl .ag-side-bar-right .ag-tool-panel-wrapper {
  border-left: var(--ag-borders) var(--ag-border-color);
}
.ag-ltr .ag-side-bar-left .ag-side-button-button,
.ag-rtl .ag-side-bar-right .ag-side-button-button {
  border-right: var(--ag-selected-tab-underline-width) solid transparent;
  transition: border-right var(--ag-selected-tab-underline-transition-speed);
}
.ag-ltr .ag-side-bar-left .ag-selected .ag-side-button-button,
.ag-rtl .ag-side-bar-right .ag-selected .ag-side-button-button {
  border-right-color: var(--ag-selected-tab-underline-color);
}

.ag-rtl .ag-side-bar-left,
.ag-ltr .ag-side-bar-right {
  border-left: var(--ag-borders) var(--ag-border-color);
}
.ag-rtl .ag-side-bar-left .ag-tool-panel-wrapper,
.ag-ltr .ag-side-bar-right .ag-tool-panel-wrapper {
  border-right: var(--ag-borders) var(--ag-border-color);
}
.ag-rtl .ag-side-bar-left .ag-side-button-button,
.ag-ltr .ag-side-bar-right .ag-side-button-button {
  border-left: var(--ag-selected-tab-underline-width) solid transparent;
  transition: border-left var(--ag-selected-tab-underline-transition-speed);
}
.ag-rtl .ag-side-bar-left .ag-selected .ag-side-button-button,
.ag-ltr .ag-side-bar-right .ag-selected .ag-side-button-button {
  border-left-color: var(--ag-selected-tab-underline-color);
}

.ag-filter-toolpanel-header {
  height: calc(var(--ag-grid-size) * 6);
}

.ag-filter-toolpanel-header,
.ag-filter-toolpanel-search {
  padding: 0 var(--ag-grid-size);
}

.ag-filter-toolpanel-header:focus-visible {
  outline: none;
}
.ag-filter-toolpanel-header:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-filter-toolpanel-group:not(.ag-has-filter) > .ag-group-title-bar .ag-filter-toolpanel-group-instance-header-icon {
  display: none;
}

.ag-filter-toolpanel-group-level-0-header {
  height: calc(var(--ag-grid-size) * 8);
}

.ag-filter-toolpanel-group-item {
  margin-top: calc(var(--ag-grid-size) * 0.5);
  margin-bottom: calc(var(--ag-grid-size) * 0.5);
}

.ag-filter-toolpanel-search {
  height: var(--ag-header-height);
}

.ag-filter-toolpanel-search-input {
  flex-grow: 1;
  height: calc(var(--ag-grid-size) * 4);
}
.ag-ltr .ag-filter-toolpanel-search-input {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-search-input {
  margin-left: var(--ag-grid-size);
}

.ag-filter-toolpanel-group-level-0 {
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-ltr .ag-filter-toolpanel-expand,
.ag-ltr .ag-filter-toolpanel-group-title-bar-icon {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-expand,
.ag-rtl .ag-filter-toolpanel-group-title-bar-icon {
  margin-left: var(--ag-grid-size);
}

.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}

.ag-ltr .ag-filter-toolpanel-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * var(--ag-indentation-level, 0) + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * var(--ag-indentation-level, 0) + var(--ag-grid-size));
}

.ag-filter-toolpanel-instance-filter {
  border-bottom: var(--ag-borders) var(--ag-border-color);
  border-top: var(--ag-borders) var(--ag-border-color);
  margin-top: var(--ag-grid-size);
}

.ag-ltr .ag-filter-toolpanel-group-instance-header-icon,
.ag-ltr .ag-filter-toolpanel-instance-header-icon {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-group-instance-header-icon,
.ag-rtl .ag-filter-toolpanel-instance-header-icon {
  margin-right: var(--ag-grid-size);
}

.ag-set-filter-group-icons {
  color: var(--ag-secondary-foreground-color);
}

.ag-pivot-mode-panel {
  min-height: var(--ag-header-height);
  height: var(--ag-header-height);
  display: flex;
}

.ag-pivot-mode-select {
  display: flex;
  align-items: center;
}
.ag-ltr .ag-pivot-mode-select {
  margin-left: var(--ag-widget-container-horizontal-padding);
}
.ag-rtl .ag-pivot-mode-select {
  margin-right: var(--ag-widget-container-horizontal-padding);
}

.ag-column-select-header:focus-visible {
  outline: none;
}
.ag-column-select-header:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-column-select-header {
  height: var(--ag-header-height);
  align-items: center;
  padding: 0 var(--ag-widget-container-horizontal-padding);
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-column-panel-column-select {
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-column-group-icons,
.ag-column-select-header-icon {
  color: var(--ag-secondary-foreground-color);
}

.ag-column-select-list .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
}
.ag-column-select-list .ag-item-highlight-top::after {
  top: 0;
}
.ag-column-select-list .ag-item-highlight-bottom::after {
  bottom: 0;
}

.ag-header,
.ag-advanced-filter-header {
  background-color: var(--ag-header-background-color);
  border-bottom: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-header-row {
  color: var(--ag-header-foreground-color);
  height: var(--ag-header-height);
}

.ag-pinned-right-header {
  border-left: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-pinned-left-header {
  border-right: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-ltr .ag-header-cell:not(.ag-right-aligned-header) .ag-header-label-icon,
.ag-ltr .ag-header-cell:not(.ag-right-aligned-header) .ag-header-menu-icon {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-header-cell:not(.ag-right-aligned-header) .ag-header-label-icon,
.ag-rtl .ag-header-cell:not(.ag-right-aligned-header) .ag-header-menu-icon {
  margin-right: var(--ag-grid-size);
}

.ag-ltr .ag-header-cell.ag-right-aligned-header .ag-header-label-icon,
.ag-ltr .ag-header-cell.ag-right-aligned-header .ag-header-menu-icon {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-header-cell.ag-right-aligned-header .ag-header-label-icon,
.ag-rtl .ag-header-cell.ag-right-aligned-header .ag-header-menu-icon {
  margin-left: var(--ag-grid-size);
}

.ag-header-cell,
.ag-header-group-cell {
  padding-left: var(--ag-cell-horizontal-padding);
  padding-right: var(--ag-cell-horizontal-padding);
}
.ag-header-cell.ag-header-cell-moving,
.ag-header-group-cell.ag-header-cell-moving {
  background-color: var(--ag-header-cell-moving-background-color);
}

.ag-ltr .ag-header-group-cell-label.ag-sticky-label {
  left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-header-group-cell-label.ag-sticky-label {
  right: var(--ag-cell-horizontal-padding);
}

.ag-header-cell:focus-visible {
  outline: none;
}
.ag-header-cell:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-header-group-cell:focus-visible {
  outline: none;
}
.ag-header-group-cell:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-advanced-filter-header-cell:focus-visible {
  outline: none;
}
.ag-advanced-filter-header-cell:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-header-icon {
  color: var(--ag-secondary-foreground-color);
}

.ag-header-expand-icon {
  cursor: pointer;
}
.ag-ltr .ag-header-expand-icon {
  margin-left: 4px;
}
.ag-rtl .ag-header-expand-icon {
  margin-right: 4px;
}

.ag-header-row:not(:first-child) .ag-header-cell:not(.ag-header-span-height.ag-header-span-total, .ag-header-parent-hidden),
.ag-header-row:not(:first-child) .ag-header-group-cell.ag-header-group-cell-with-group {
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-header-group-cell:not(.ag-column-resizing) + .ag-header-group-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover, .ag-header-group-cell:not(.ag-column-resizing) + .ag-header-group-cell:not(.ag-column-hover).ag-column-resizing,
.ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover,
.ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover).ag-column-resizing,
.ag-header-group-cell:first-of-type:not(.ag-header-cell-moving):hover,
.ag-header-group-cell:first-of-type.ag-column-resizing,
.ag-header-cell:not(.ag-column-hover):first-of-type:not(.ag-header-cell-moving):hover,
.ag-header-cell:not(.ag-column-hover):first-of-type.ag-column-resizing {
  background-color: var(--ag-header-cell-hover-background-color);
}

.ag-header-cell::before,
.ag-header-group-cell:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
  content: "";
  position: absolute;
  z-index: 1;
  display: var(--ag-header-column-separator-display);
  width: var(--ag-header-column-separator-width);
  height: var(--ag-header-column-separator-height);
  top: calc(50% - var(--ag-header-column-separator-height) * 0.5);
  background-color: var(--ag-header-column-separator-color);
}
.ag-ltr .ag-header-cell::before,
.ag-ltr .ag-header-group-cell:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
  right: 0;
}
.ag-rtl .ag-header-cell::before,
.ag-rtl .ag-header-group-cell:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
  left: 0;
}

.ag-header-highlight-before::after,
.ag-header-highlight-after::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
}

.ag-header-highlight-before::after {
  left: 0px;
}

.ag-header-highlight-after::after {
  right: 0px;
}

.ag-pinned-left-header .ag-header-highlight-after::after {
  right: 1px;
}

.ag-header-cell-resize {
  display: flex;
  align-items: center;
}

.ag-header-cell-resize::after {
  content: "";
  position: absolute;
  z-index: 1;
  display: var(--ag-header-column-resize-handle-display);
  width: var(--ag-header-column-resize-handle-width);
  height: var(--ag-header-column-resize-handle-height);
  top: calc(50% - var(--ag-header-column-resize-handle-height) * 0.5);
  background-color: var(--ag-header-column-resize-handle-color);
}
.ag-header-cell.ag-header-span-height .ag-header-cell-resize::after {
  height: calc(100% - var(--ag-grid-size) * 4);
  top: calc(var(--ag-grid-size) * 2);
}

.ag-ltr .ag-header-viewport .ag-header-cell-resize::after {
  left: calc(50% - var(--ag-header-column-resize-handle-width));
}
.ag-rtl .ag-header-viewport .ag-header-cell-resize::after {
  right: calc(50% - var(--ag-header-column-resize-handle-width));
}

.ag-pinned-left-header .ag-header-cell-resize::after {
  left: calc(50% - var(--ag-header-column-resize-handle-width));
}

.ag-pinned-right-header .ag-header-cell-resize::after {
  left: 50%;
}

.ag-ltr .ag-header-select-all {
  margin-right: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-header-select-all {
  margin-left: var(--ag-cell-horizontal-padding);
}

.ag-ltr .ag-floating-filter-button {
  margin-left: var(--ag-cell-widget-spacing);
}
.ag-rtl .ag-floating-filter-button {
  margin-right: var(--ag-cell-widget-spacing);
}

.ag-floating-filter-button-button {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  appearance: none;
  background: transparent;
  border: none;
  height: var(--ag-icon-size);
  padding: 0;
  width: var(--ag-icon-size);
}

.ag-filter-loading {
  background-color: var(--ag-control-panel-background-color);
  height: 100%;
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  position: absolute;
  width: 100%;
  z-index: 1;
}

.ag-paging-panel {
  border-top: 1px solid;
  border-top-color: var(--ag-border-color);
  color: var(--ag-secondary-foreground-color);
  height: var(--ag-header-height);
}
.ag-paging-panel > * {
  margin: 0 var(--ag-cell-horizontal-padding);
}
.ag-paging-panel > .ag-paging-page-size .ag-wrapper {
  min-width: calc(var(--ag-grid-size) * 10);
}

.ag-paging-button {
  cursor: pointer;
}

.ag-paging-button.ag-disabled {
  cursor: default;
  color: var(--ag-disabled-foreground-color);
}

.ag-paging-button:focus-visible {
  outline: none;
}
.ag-paging-button:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 0px;
  left: 0px;
  display: block;
  width: calc(100% - 0px);
  height: calc(100% - 0px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-paging-button,
.ag-paging-description {
  margin: 0 var(--ag-grid-size);
}

.ag-status-bar {
  border-top: var(--ag-borders) var(--ag-border-color);
  color: var(--ag-disabled-foreground-color);
  padding-right: calc(var(--ag-grid-size) * 4);
  padding-left: calc(var(--ag-grid-size) * 4);
  line-height: 1.5;
}

.ag-status-name-value-value {
  color: var(--ag-foreground-color);
}

.ag-status-bar-center {
  text-align: center;
}

.ag-status-name-value {
  margin-left: var(--ag-grid-size);
  margin-right: var(--ag-grid-size);
  padding-top: calc(var(--ag-grid-size) * 2);
  padding-bottom: calc(var(--ag-grid-size) * 2);
}

.ag-column-drop-cell {
  background: var(--ag-chip-background-color);
  border-radius: calc(var(--ag-grid-size) * 4);
  height: calc(var(--ag-grid-size) * 4);
  padding: 0 calc(var(--ag-grid-size) * 0.5);
  border: 1px solid var(--ag-chip-border-color);
}

.ag-column-drop-cell:focus-visible {
  outline: none;
}
.ag-column-drop-cell:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 2px;
  left: 2px;
  display: block;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-column-drop-cell-text {
  margin: 0 var(--ag-grid-size);
}

.ag-column-drop-cell-button {
  min-width: calc(var(--ag-grid-size) * 4);
  margin: 0 calc(var(--ag-grid-size) * 0.5);
  color: var(--ag-secondary-foreground-color);
}

.ag-column-drop-cell-drag-handle {
  margin-left: calc(var(--ag-grid-size) * 2);
}

.ag-column-drop-cell-ghost {
  opacity: 0.5;
}

.ag-column-drop-horizontal {
  background-color: var(--ag-header-background-color);
  color: var(--ag-secondary-foreground-color);
  height: var(--ag-header-height);
  border-bottom: var(--ag-borders) var(--ag-border-color);
}
.ag-ltr .ag-column-drop-horizontal {
  padding-left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-column-drop-horizontal {
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-ltr .ag-column-drop-horizontal-half-width:not(:last-child) {
  border-right: var(--ag-borders) var(--ag-border-color);
}
.ag-rtl .ag-column-drop-horizontal-half-width:not(:last-child) {
  border-left: var(--ag-borders) var(--ag-border-color);
}

.ag-column-drop-horizontal-cell-separator {
  margin: 0 var(--ag-grid-size);
  color: var(--ag-secondary-foreground-color);
}

.ag-column-drop-horizontal-empty-message {
  color: var(--ag-disabled-foreground-color);
}

.ag-ltr .ag-column-drop-horizontal-icon {
  margin-right: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-column-drop-horizontal-icon {
  margin-left: var(--ag-cell-horizontal-padding);
}

.ag-column-drop-vertical-list {
  padding-bottom: var(--ag-grid-size);
  padding-right: var(--ag-grid-size);
  padding-left: var(--ag-grid-size);
}

.ag-column-drop-vertical-cell {
  margin-top: var(--ag-grid-size);
}

.ag-column-drop-vertical {
  min-height: 50px;
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}
.ag-column-drop-vertical.ag-last-column-drop {
  border-bottom: none;
}

.ag-column-drop-vertical-icon {
  margin-left: var(--ag-grid-size);
  margin-right: var(--ag-grid-size);
}

.ag-column-drop-vertical-empty-message {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  color: var(--ag-disabled-foreground-color);
  margin-top: var(--ag-grid-size);
}

.ag-select-agg-func-popup {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  background: var(--ag-background-color);
  height: calc(var(--ag-grid-size) * 5 * 3.5);
  padding: 0;
}

.ag-select-agg-func-virtual-list-item {
  cursor: default;
}
.ag-ltr .ag-select-agg-func-virtual-list-item {
  padding-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-select-agg-func-virtual-list-item {
  padding-right: calc(var(--ag-grid-size) * 2);
}
.ag-select-agg-func-virtual-list-item:hover {
  background-color: var(--ag-selected-row-background-color);
}

.ag-select-agg-func-virtual-list-item:focus-visible {
  outline: none;
}
.ag-select-agg-func-virtual-list-item:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-sort-indicator-container {
  display: flex;
}

.ag-ltr .ag-sort-indicator-icon {
  padding-left: var(--ag-grid-size);
}
.ag-rtl .ag-sort-indicator-icon {
  padding-right: var(--ag-grid-size);
}

.ag-chart {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
}

.ag-chart-components-wrapper {
  position: relative;
  display: flex;
  flex: 1 1 auto;
}

.ag-chart-canvas-wrapper {
  position: relative;
  flex: 1 1 auto;
}

.ag-chart-menu {
  position: absolute;
  top: 16px;
  display: flex;
  flex-direction: column;
}
.ag-ltr .ag-chart-menu {
  right: 20px;
}
.ag-rtl .ag-chart-menu {
  left: 20px;
}

.ag-chart-docked-container {
  position: relative;
  min-width: var(--ag-chart-menu-panel-width);
}

.ag-chart-menu-hidden ~ .ag-chart-docked-container {
  display: none;
}

.ag-chart-tabbed-menu {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ag-chart-tabbed-menu-header {
  flex: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}

.ag-chart-tabbed-menu-body {
  display: flex;
  flex: 1 1 auto;
  align-items: stretch;
  overflow: hidden;
}

.ag-chart-tab {
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.ag-chart-settings {
  overflow-x: hidden;
}

.ag-chart-settings-wrapper {
  position: relative;
  flex-direction: column;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.ag-chart-settings-nav-bar {
  display: flex;
  align-items: center;
  width: 100%;
  height: 30px;
  padding: 0 10px;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-chart-settings-card-selector {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex: 1 1 auto;
  height: 100%;
  padding: 0 10px;
}

.ag-chart-settings-card-item {
  cursor: pointer;
  width: 10px;
  height: 10px;
  background-color: #000;
  position: relative;
}
.ag-chart-settings-card-item.ag-not-selected {
  opacity: 0.2;
}
.ag-chart-settings-card-item::before {
  content: " ";
  display: block;
  position: absolute;
  background-color: transparent;
  left: 50%;
  top: 50%;
  margin-left: -10px;
  margin-top: -10px;
  width: 20px;
  height: 20px;
}

.ag-chart-settings-prev,
.ag-chart-settings-next {
  position: relative;
  flex: none;
}
.ag-chart-settings-prev:focus-within,
.ag-chart-settings-next:focus-within {
  box-shadow: var(--ag-input-focus-box-shadow);
  border-radius: 1px;
}

.ag-chart-settings-prev-button,
.ag-chart-settings-next-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  opacity: 0;
}

.ag-chart-settings-mini-charts-container {
  position: relative;
  flex: 1 1 auto;
  overflow-x: hidden;
  overflow-y: auto;
}

.ag-chart-settings-mini-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  overflow: hidden;
}
.ag-chart-settings-mini-wrapper.ag-animating {
  transition: left 0.3s;
  transition-timing-function: ease-in-out;
}

.ag-chart-mini-thumbnail {
  cursor: pointer;
}

.ag-chart-mini-thumbnail-canvas {
  display: block;
}

.ag-chart-data-wrapper,
.ag-chart-format-wrapper,
.ag-chart-advanced-settings-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding-bottom: 16px;
}

.ag-chart-data-wrapper,
.ag-chart-advanced-settings-wrapper {
  height: 100%;
  overflow-y: auto;
}

.ag-chart-advanced-settings {
  background-color: var(--ag-control-panel-background-color);
}

.ag-chart-advanced-settings-wrapper,
.ag-chart-advanced-settings {
  width: 100%;
}

.ag-chart-advanced-settings-wrapper {
  padding-bottom: 0;
}

.ag-chart-data-section,
.ag-chart-format-section,
.ag-chart-advanced-settings-section {
  display: flex;
  margin: 0;
}

.ag-chart-advanced-settings-section {
  padding-top: var(--ag-grid-size);
  padding-bottom: var(--ag-grid-size);
}
.ag-chart-advanced-settings-section:not(:last-child) {
  border-bottom: 1px solid var(--ag-secondary-border-color);
}

.ag-chart-empty-text {
  display: flex;
  top: 0;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  background-color: var(--ag-background-color);
}

.ag-chart .ag-chart-menu {
  display: none;
}

.ag-chart-menu-hidden:hover .ag-chart-menu {
  display: block;
}

.ag-chart .ag-chart-menu-wrapper .ag-chart-menu {
  display: flex;
  flex-direction: row;
  top: 8px;
  gap: 20px;
  width: auto;
}
.ag-ltr .ag-chart .ag-chart-menu-wrapper .ag-chart-menu {
  right: calc(var(--ag-cell-horizontal-padding) + var(--ag-grid-size) - 4px);
  justify-content: right;
}
.ag-rtl .ag-chart .ag-chart-menu-wrapper .ag-chart-menu {
  left: calc(var(--ag-cell-horizontal-padding) + var(--ag-grid-size) - 4px);
  justify-content: left;
}

.ag-charts-font-size-color {
  display: flex;
  align-self: stretch;
  justify-content: space-between;
}

.ag-charts-data-group-item {
  position: relative;
}

.ag-charts-data-group-item:not(:last-child) {
  margin-bottom: var(--ag-grid-size);
}

.ag-chart-menu {
  border-radius: var(--ag-card-radius);
  background: var(--ag-background-color);
}

.ag-chart-menu-icon {
  opacity: 0.5;
  margin: 2px 0;
  cursor: pointer;
  border-radius: var(--ag-card-radius);
  color: var(--ag-secondary-foreground-color);
}
.ag-chart-menu-icon:hover {
  opacity: 1;
}

.ag-chart-menu-toolbar-button {
  border: 0;
  background-color: unset;
  padding: 0 2px;
  border-radius: 1px;
}

.ag-chart-mini-thumbnail {
  border: 1px solid var(--ag-secondary-border-color);
  border-radius: 5px;
}
.ag-chart-mini-thumbnail.ag-selected {
  border-color: var(--ag-minichart-selected-chart-color);
  border-width: 2px;
}
.ag-chart-mini-thumbnail:focus-visible {
  outline: none;
  border-color: var(--ag-minichart-selected-chart-color);
  box-shadow: var(--ag-input-focus-box-shadow);
}

.ag-chart-settings-card-item {
  background: var(--ag-foreground-color);
  width: 8px;
  height: 8px;
  border-radius: 4px;
}
.ag-chart-settings-card-item.ag-selected {
  background-color: var(--ag-minichart-selected-page-color);
}

.ag-chart-data-column-drag-handle {
  margin-left: var(--ag-grid-size);
}

.ag-charts-settings-group-title-bar,
.ag-charts-data-group-title-bar,
.ag-charts-format-top-level-group-title-bar {
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  position: relative;
}

.ag-charts-advanced-settings-top-level-group-title-bar {
  position: relative;
  background-color: unset;
}

.ag-charts-data-group-title-bar:focus-visible {
  outline: none;
}
.ag-charts-data-group-title-bar:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-charts-format-top-level-group-title-bar:focus-visible {
  outline: none;
}
.ag-charts-format-top-level-group-title-bar:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-charts-data-group-title-bar .ag-charts-data-group-title,
.ag-charts-format-top-level-group-title-bar .ag-charts-format-top-level-group-title {
  cursor: pointer;
}

.ag-charts-data-group-container {
  padding: calc(var(--ag-widget-container-vertical-padding) * 0.5) var(--ag-widget-container-horizontal-padding);
}
.ag-charts-data-group-container .ag-charts-data-group-item:not(.ag-charts-format-sub-level-group):not(.ag-pill-select):not(.ag-select) {
  height: var(--ag-list-item-height);
}
.ag-charts-data-group-container .ag-charts-data-group-item.ag-picker-field {
  margin-top: var(--ag-grid-size);
}
.ag-charts-data-group-container .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
}
.ag-charts-data-group-container .ag-item-highlight-top::after {
  top: 0;
}
.ag-charts-data-group-container .ag-item-highlight-bottom::after {
  bottom: 0;
}

.ag-charts-format-top-level-group-container,
.ag-charts-advanced-settings-top-level-group-container {
  padding: var(--ag-grid-size);
}
.ag-ltr .ag-charts-format-top-level-group-container,
.ag-ltr .ag-charts-advanced-settings-top-level-group-container {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-charts-format-top-level-group-container,
.ag-rtl .ag-charts-advanced-settings-top-level-group-container {
  margin-right: calc(var(--ag-grid-size) * 2);
}

.ag-charts-format-top-level-group-item,
.ag-charts-advanced-settings-top-level-group-item {
  margin: var(--ag-grid-size) 0;
}

.ag-charts-format-sub-level-group-container {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  padding-bottom: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
}
.ag-charts-format-sub-level-group-container > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}

.ag-charts-format-sub-level-no-header-group-container > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}

.ag-charts-format-sub-level-group-container .ag-charts-format-sub-level-group-item.ag-font-panel-no-header {
  margin: 0;
}

.ag-charts-settings-group-container {
  padding: var(--ag-grid-size);
  row-gap: 8px;
  display: grid;
  grid-template-columns: 60px 1fr 60px 1fr 60px;
}
.ag-charts-settings-group-container .ag-chart-mini-thumbnail:nth-child(3n+1) {
  grid-column: 1;
}
.ag-charts-settings-group-container .ag-chart-mini-thumbnail:nth-child(3n+2) {
  grid-column: 3;
}
.ag-charts-settings-group-container .ag-chart-mini-thumbnail:nth-child(3n+3) {
  grid-column: 5;
}

.ag-chart-menu-panel {
  background-color: var(--ag-control-panel-background-color);
}
.ag-ltr .ag-chart-menu-panel {
  border-left: solid 1px var(--ag-border-color);
}
.ag-rtl .ag-chart-menu-panel {
  border-right: solid 1px var(--ag-border-color);
}

.ag-charts-theme-default {
  --ag-charts-align: none !important;
  --ag-charts-justify: none !important;
}

.ag-charts-wrapper .ag-charts-proxy-legend-toolbar button:focus {
  box-shadow: none;
}

.ag-charts-wrapper .ag-charts-proxy-legend-toolbar button:focus-visible {
  box-shadow: var(--ag-input-focus-box-shadow);
}

.ag-date-time-list-page-title-bar {
  display: flex;
}

.ag-date-time-list-page-title {
  flex-grow: 1;
  text-align: center;
}

.ag-date-time-list-page-column-labels-row,
.ag-date-time-list-page-entries-row {
  display: flex;
}

.ag-date-time-list-page-column-label,
.ag-date-time-list-page-entry {
  flex-basis: 0;
  flex-grow: 1;
}

.ag-date-time-list-page-entry {
  cursor: pointer;
  text-align: center;
}

.ag-date-time-list-page-column-label {
  text-align: center;
}

.ag-advanced-filter-header {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: var(--ag-cell-horizontal-padding);
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-advanced-filter {
  display: flex;
  align-items: center;
  width: 100%;
}

.ag-advanced-filter-apply-button,
.ag-advanced-filter-builder-button {
  line-height: normal;
  white-space: nowrap;
}
.ag-ltr .ag-advanced-filter-apply-button,
.ag-ltr .ag-advanced-filter-builder-button {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-advanced-filter-apply-button,
.ag-rtl .ag-advanced-filter-builder-button {
  margin-right: calc(var(--ag-grid-size) * 2);
}

.ag-advanced-filter-builder-button {
  display: flex;
  align-items: center;
  border: 0;
  background-color: unset;
  color: var(--ag-foreground-color);
  font-size: var(--ag-font-size);
  font-weight: 600;
}
.ag-advanced-filter-builder-button:hover:not(:disabled) {
  background-color: var(--ag-row-hover-color);
}
.ag-advanced-filter-builder-button:not(:disabled) {
  cursor: pointer;
}

.ag-advanced-filter-builder-button-label {
  margin-left: var(--ag-grid-size);
}

.ag-advanced-filter-builder {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 100%;
  background-color: var(--ag-control-panel-background-color);
  display: flex;
  flex-direction: column;
}

.ag-advanced-filter-builder-list {
  flex: 1;
  overflow: auto;
}
.ag-advanced-filter-builder-list .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
}
.ag-advanced-filter-builder-list .ag-item-highlight-top::after {
  top: 0;
}
.ag-advanced-filter-builder-list .ag-item-highlight-bottom::after {
  bottom: 0;
}

.ag-advanced-filter-builder-button-panel {
  display: flex;
  justify-content: flex-end;
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-advanced-filter-builder .ag-advanced-filter-builder-button-panel .ag-advanced-filter-builder-apply-button,
.ag-advanced-filter-builder .ag-advanced-filter-builder-button-panel .ag-advanced-filter-builder-cancel-button {
  margin-left: calc(var(--ag-grid-size) * 2);
}

.ag-advanced-filter-builder-item-wrapper {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  padding-left: calc(var(--ag-icon-size) / 2);
  padding-right: var(--ag-icon-size);
}

.ag-virtual-list-viewport .ag-advanced-filter-builder-item-wrapper .ag-tab-guard {
  position: absolute;
}

.ag-advanced-filter-builder-item-tree-lines > * {
  width: var(--ag-advanced-filter-builder-indent-size);
}

.ag-advanced-filter-builder-item-tree-lines .ag-advanced-filter-builder-item-tree-line-root {
  width: var(--ag-icon-size);
}
.ag-advanced-filter-builder-item-tree-lines .ag-advanced-filter-builder-item-tree-line-root::before {
  top: 50%;
  height: 50%;
}

.ag-advanced-filter-builder-item-tree-line-horizontal,
.ag-advanced-filter-builder-item-tree-line-vertical,
.ag-advanced-filter-builder-item-tree-line-vertical-top,
.ag-advanced-filter-builder-item-tree-line-vertical-bottom {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}
.ag-advanced-filter-builder-item-tree-line-horizontal::before, .ag-advanced-filter-builder-item-tree-line-horizontal::after,
.ag-advanced-filter-builder-item-tree-line-vertical::before,
.ag-advanced-filter-builder-item-tree-line-vertical::after,
.ag-advanced-filter-builder-item-tree-line-vertical-top::before,
.ag-advanced-filter-builder-item-tree-line-vertical-top::after,
.ag-advanced-filter-builder-item-tree-line-vertical-bottom::before,
.ag-advanced-filter-builder-item-tree-line-vertical-bottom::after {
  content: "";
  position: absolute;
  height: 100%;
}

.ag-advanced-filter-builder-item-tree-line-horizontal::after {
  height: 50%;
  width: calc(var(--ag-advanced-filter-builder-indent-size) - var(--ag-icon-size));
  top: 0;
  left: calc(var(--ag-icon-size) / 2);
  border-bottom: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-tree-line-vertical::before {
  width: calc(var(--ag-advanced-filter-builder-indent-size) - var(--ag-icon-size) / 2);
  top: 0;
  left: calc(var(--ag-icon-size) / 2);
  border-left: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-tree-line-vertical-top::before {
  height: 50%;
  width: calc(var(--ag-advanced-filter-builder-indent-size) - var(--ag-icon-size) / 2);
  top: 0;
  left: calc(var(--ag-icon-size) / 2);
  border-left: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-tree-line-vertical-bottom::before {
  height: calc((100% - 1.5 * var(--ag-icon-size)) / 2);
  width: calc(var(--ag-icon-size) / 2);
  top: calc((100% + 1.5 * var(--ag-icon-size)) / 2);
  left: calc(var(--ag-icon-size) / 2);
  border-left: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-condition {
  padding-top: var(--ag-grid-size);
  padding-bottom: var(--ag-grid-size);
}

.ag-advanced-filter-builder-item,
.ag-advanced-filter-builder-item-condition,
.ag-advanced-filter-builder-pill-wrapper,
.ag-advanced-filter-builder-pill,
.ag-advanced-filter-builder-item-buttons,
.ag-advanced-filter-builder-item-tree-lines {
  display: flex;
  align-items: center;
  height: 100%;
}

.ag-advanced-filter-builder-pill-wrapper {
  margin: 0px var(--ag-grid-size);
}

.ag-advanced-filter-builder-pill {
  position: relative;
  border-radius: var(--ag-border-radius);
  padding: var(--ag-grid-size) calc(var(--ag-grid-size) * 2);
  min-height: calc(100% - var(--ag-grid-size) * 3);
  min-width: calc(var(--ag-grid-size) * 2);
}
.ag-advanced-filter-builder-pill .ag-picker-field-display {
  margin-right: var(--ag-grid-size);
}
.ag-advanced-filter-builder-pill .ag-advanced-filter-builder-value-number {
  font-family: monospace;
  font-weight: 700;
}
.ag-advanced-filter-builder-pill .ag-advanced-filter-builder-value-empty {
  color: var(--ag-disabled-foreground-color);
}

.ag-advanced-filter-builder-pill:focus-visible {
  outline: none;
}
.ag-advanced-filter-builder-pill:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: -4px;
  left: -4px;
  display: block;
  width: calc(100% - -8px);
  height: calc(100% - -8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-advanced-filter-builder-item-button:focus-visible {
  outline: none;
}
.ag-advanced-filter-builder-item-button:focus-visible::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: -4px;
  left: -4px;
  display: block;
  width: calc(100% - -8px);
  height: calc(100% - -8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-advanced-filter-builder-pill-display {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.ag-advanced-filter-builder-join-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-join-pill-color);
  cursor: pointer;
}

.ag-advanced-filter-builder-column-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-column-pill-color);
  cursor: pointer;
}

.ag-advanced-filter-builder-option-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-option-pill-color);
  cursor: pointer;
}

.ag-advanced-filter-builder-value-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-value-pill-color);
  cursor: text;
  max-width: 140px;
}
.ag-advanced-filter-builder-value-pill .ag-advanced-filter-builder-pill-display {
  display: block;
}

.ag-advanced-filter-builder-item-buttons > * {
  margin: 0 calc(var(--ag-grid-size) * 0.5);
}

.ag-advanced-filter-builder-item-button {
  position: relative;
  cursor: pointer;
  color: var(--ag-secondary-foreground-color);
  opacity: 50%;
}

.ag-advanced-filter-builder-item-button-disabled {
  color: var(--ag-disabled-foreground-color);
  cursor: default;
}

.ag-advanced-filter-builder-virtual-list-container {
  top: var(--ag-grid-size);
}

.ag-advanced-filter-builder-virtual-list-item {
  display: flex;
  cursor: default;
  height: var(--ag-list-item-height);
}
.ag-advanced-filter-builder-virtual-list-item:hover {
  background-color: var(--ag-row-hover-color);
}
.ag-advanced-filter-builder-virtual-list-item:hover .ag-advanced-filter-builder-item-button {
  opacity: 100%;
}

.ag-advanced-filter-builder-virtual-list-item-highlight .ag-advanced-filter-builder-item-button:focus-visible,
.ag-advanced-filter-builder-validation .ag-advanced-filter-builder-invalid {
  opacity: 100%;
}

.ag-advanced-filter-builder-invalid {
  margin: 0 var(--ag-grid-size);
  color: var(--ag-invalid-color);
  cursor: default;
}

.ag-input-field-input {
  width: 100%;
  min-width: 0;
}

.ag-checkbox-input-wrapper {
  font-family: var(--ag-icon-font-family);
  font-weight: var(--ag-icon-font-weight);
  color: var(--ag-icon-font-color);
  font-size: var(--ag-icon-size);
  line-height: var(--ag-icon-size);
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-checkbox-background-color);
  border-radius: var(--ag-checkbox-border-radius);
  display: inline-block;
  vertical-align: middle;
  flex: none;
}
.ag-checkbox-input-wrapper input,
.ag-checkbox-input-wrapper input {
  -webkit-appearance: none;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.ag-checkbox-input-wrapper:focus-within, .ag-checkbox-input-wrapper:active {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
}
.ag-checkbox-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-checkbox-input-wrapper::after {
  content: var(--ag-icon-font-code-checkbox-unchecked, "\f108");
  font-family: inherit;
  color: var(--ag-checkbox-unchecked-color);
  display: var(--ag-icon-font-display-checkbox-unchecked, var(--ag-icon-font-display));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-checkbox-input-wrapper.ag-checked::after {
  content: var(--ag-icon-font-code-checkbox-checked, "\f106");
  color: var(--ag-checkbox-checked-color);
  display: var(--ag-icon-font-display-checkbox-checked, var(--ag-icon-font-display));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-checkbox-input-wrapper.ag-indeterminate::after {
  content: var(--ag-icon-font-code-checkbox-indeterminate, "\f107");
  color: var(--ag-checkbox-indeterminate-color);
  display: var(--ag-icon-font-display-checkbox-indeterminate, var(--ag-icon-font-display));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-checkbox-input-wrapper::before {
  content: "";
  background: transparent center/contain no-repeat;
  position: absolute;
  inset: 0;
  background-image: var(--ag-icon-image-checkbox-unchecked, var(--ag-icon-image));
  display: var(--ag-icon-image-display-checkbox-unchecked, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-checkbox-unchecked, var(--ag-icon-image-opacity, 0.9));
}
.ag-checkbox-input-wrapper.ag-checked::before {
  background-image: var(--ag-icon-image-checkbox-checked, var(--ag-icon-image));
  display: var(--ag-icon-image-display-checkbox-checked, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-checkbox-checked, var(--ag-icon-image-opacity, 0.9));
}
.ag-checkbox-input-wrapper.ag-indeterminate::before {
  background-image: var(--ag-icon-image-checkbox-indeterminate, var(--ag-icon-image));
  display: var(--ag-icon-image-display-checkbox-indeterminate, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-checkbox-indeterminate, var(--ag-icon-image-opacity, 0.9));
}

.ag-toggle-button-input-wrapper {
  box-sizing: border-box;
  width: var(--ag-toggle-button-width);
  min-width: var(--ag-toggle-button-width);
  max-width: var(--ag-toggle-button-width);
  height: var(--ag-toggle-button-height);
  background-color: var(--ag-toggle-button-off-background-color);
  border-radius: calc(var(--ag-toggle-button-height) * 0.5);
  position: relative;
  flex: none;
  border: var(--ag-toggle-button-border-width) solid;
  border-color: var(--ag-toggle-button-off-border-color);
}
.ag-toggle-button-input-wrapper input {
  opacity: 0;
  height: 100%;
  width: 100%;
}
.ag-toggle-button-input-wrapper:focus-within {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
}
.ag-toggle-button-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-toggle-button-input-wrapper.ag-checked {
  background-color: var(--ag-toggle-button-on-background-color);
  border-color: var(--ag-toggle-button-on-border-color);
}
.ag-toggle-button-input-wrapper::before {
  content: " ";
  position: absolute;
  top: calc(0px - var(--ag-toggle-button-border-width));
  left: calc(0px - var(--ag-toggle-button-border-width));
  display: block;
  box-sizing: border-box;
  height: var(--ag-toggle-button-height);
  width: var(--ag-toggle-button-height);
  background-color: var(--ag-toggle-button-switch-background-color);
  border-radius: 100%;
  transition: left 100ms;
  border: var(--ag-toggle-button-border-width) solid;
  border-color: var(--ag-toggle-button-switch-border-color);
}
.ag-toggle-button-input-wrapper.ag-checked::before {
  left: calc(100% - var(--ag-toggle-button-height) + var(--ag-toggle-button-border-width));
  border-color: var(--ag-toggle-button-on-border-color);
}

.ag-radio-button-input-wrapper {
  font-family: var(--ag-icon-font-family);
  font-weight: var(--ag-icon-font-weight);
  color: var(--ag-icon-font-color);
  font-size: var(--ag-icon-size);
  line-height: var(--ag-icon-size);
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-checkbox-background-color);
  border-radius: var(--ag-checkbox-border-radius);
  display: inline-block;
  vertical-align: middle;
  flex: none;
  border-radius: var(--ag-icon-size);
}
.ag-radio-button-input-wrapper input,
.ag-radio-button-input-wrapper input {
  -webkit-appearance: none;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.ag-radio-button-input-wrapper:focus-within, .ag-radio-button-input-wrapper:active {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
}
.ag-radio-button-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-radio-button-input-wrapper::after {
  content: var(--ag-icon-font-code-radio-button-off, "\f127");
  color: var(--ag-checkbox-unchecked-color);
  display: var(--ag-icon-font-display-radio-button-off, var(--ag-icon-font-display));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-radio-button-input-wrapper.ag-checked::after {
  content: var(--ag-icon-font-code-radio-button-on, "\f128");
  color: var(--ag-checkbox-checked-color);
  display: var(--ag-icon-font-display-radio-button-on, var(--ag-icon-font-display));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-radio-button-input-wrapper::before {
  content: "";
  background: transparent center/contain no-repeat;
  position: absolute;
  inset: 0;
  background-image: var(--ag-icon-image-radio-button-off, var(--ag-icon-image));
  display: var(--ag-icon-image-display-radio-button-off, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-radio-button-off, var(--ag-icon-image-opacity, 0.9));
}
.ag-radio-button-input-wrapper.ag-checked::before {
  background-image: var(--ag-icon-image-radio-button-on, var(--ag-icon-image));
  display: var(--ag-icon-image-display-radio-button-on, var(--ag-icon-image-display));
  opacity: var(--ag-icon-image-opacity-radio-button-on, var(--ag-icon-image-opacity, 0.9));
}

input[class^=ag-][type=range] {
  -webkit-appearance: none;
  width: 100%;
  height: 100%;
  background: none;
  overflow: visible;
}
input[class^=ag-][type=range]::-webkit-slider-runnable-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: var(--ag-border-color);
  border-radius: var(--ag-border-radius);
  border-radius: var(--ag-checkbox-border-radius);
}
input[class^=ag-][type=range]::-moz-range-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: var(--ag-border-color);
  border-radius: var(--ag-border-radius);
  border-radius: var(--ag-checkbox-border-radius);
}
input[class^=ag-][type=range]::-ms-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: var(--ag-border-color);
  border-radius: var(--ag-border-radius);
  border-radius: var(--ag-checkbox-border-radius);
  color: transparent;
  width: calc(100% - 2px);
}
input[class^=ag-][type=range]::-webkit-slider-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-background-color);
  border: 1px solid;
  border-color: var(--ag-checkbox-unchecked-color);
  border-radius: var(--ag-icon-size);
  transform: translateY(calc(var(--ag-icon-size) * -0.5 + 1.5px));
}
input[class^=ag-][type=range]::-ms-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-background-color);
  border: 1px solid;
  border-color: var(--ag-checkbox-unchecked-color);
  border-radius: var(--ag-icon-size);
}
input[class^=ag-][type=range]::-moz-ag-range-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-background-color);
  border: 1px solid;
  border-color: var(--ag-checkbox-unchecked-color);
  border-radius: var(--ag-icon-size);
}
input[class^=ag-][type=range]:focus {
  outline: none;
}
input[class^=ag-][type=range]:focus::-webkit-slider-thumb {
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-checkbox-checked-color);
}
input[class^=ag-][type=range]:focus::-ms-thumb {
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-checkbox-checked-color);
}
input[class^=ag-][type=range]:focus::-moz-ag-range-thumb {
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-checkbox-checked-color);
}
input[class^=ag-][type=range]:active::-webkit-slider-runnable-track {
  background-color: var(--ag-input-focus-border-color);
}
input[class^=ag-][type=range]:active::-moz-ag-range-track {
  background-color: var(--ag-input-focus-border-color);
}
input[class^=ag-][type=range]:active::-ms-track {
  background-color: var(--ag-input-focus-border-color);
}
input[class^=ag-][type=range]:disabled {
  opacity: 0.5;
}
