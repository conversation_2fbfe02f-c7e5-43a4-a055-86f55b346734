export declare const FilterCoreModule: import("../interfaces/iModule").Module;
export declare const FilterApiModule: import("../interfaces/iModule").Module;
export declare const ColumnFilterModule: import("../interfaces/iModule").Module;
export declare const ColumnFilterApiModule: import("../interfaces/iModule").Module;
export declare const FloatingFilterCoreModule: import("../interfaces/iModule").Module;
export declare const FloatingFilterModule: import("../interfaces/iModule").Module;
export declare const ReadOnlyFloatingFilterModule: import("../interfaces/iModule").Module;
export declare const SimpleFilterModule: import("../interfaces/iModule").Module;
export declare const SimpleFloatingFilterModule: import("../interfaces/iModule").Module;
export declare const QuickFilterCoreModule: import("../interfaces/iModule").Module;
export declare const QuickFilterApiModule: import("../interfaces/iModule").Module;
export declare const QuickFilterModule: import("../interfaces/iModule").Module;
export declare const FilterModule: import("../interfaces/iModule").Module;
