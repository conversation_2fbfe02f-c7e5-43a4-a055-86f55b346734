import type { UserCompDetails } from '../components/framework/userComponentFactory';
import { BeanStub } from '../context/beanStub';
import type { BeanCollection, BeanName } from '../context/context';
import type { AgColumn } from '../entities/agColumn';
import type { ColDef } from '../entities/colDef';
import type { CoreDataTypeDefinition, DataTypeFormatValueFunc } from '../entities/dataType';
import type { RowNode } from '../entities/rowNode';
import type { FilterChangedEventSourceType } from '../events';
import type { FilterModel, IFilter, IFilterComp, IFilterParams } from '../interfaces/iFilter';
import { AgPromise } from '../utils/promise';
export declare class ColumnFilterService extends BeanStub {
    beanName: BeanName;
    private valueService;
    private columnModel;
    private rowModel;
    private userComponentFactory;
    private rowRenderer;
    private dataTypeService?;
    private filterManager?;
    wireBeans(beans: BeanCollection): void;
    private allColumnFilters;
    private allColumnListeners;
    private activeAggregateFilters;
    private activeColumnFilters;
    private processingFilterChange;
    private filterModelUpdateQueue;
    private columnFilterModelUpdateQueue;
    private initialFilterModel;
    postConstruct(): void;
    setFilterModel(model: FilterModel | null, source?: FilterChangedEventSourceType): void;
    private setModelOnFilterWrapper;
    getFilterModel(excludeInitialState?: boolean): FilterModel;
    private getModelFromFilterWrapper;
    private getModelFromInitialState;
    isColumnFilterPresent(): boolean;
    isAggregateFilterPresent(): boolean;
    disableColumnFilters(): boolean;
    doAggregateFiltersPass(node: RowNode, filterToSkip?: IFilterComp): boolean;
    private updateActiveFilters;
    private updateFilterFlagInColumns;
    private forEachColumnFilter;
    doColumnFiltersPass(node: RowNode, filterToSkip?: IFilterComp, targetAggregates?: boolean): boolean;
    private callOnFilterChangedOutsideRenderCycle;
    updateBeforeFilterChanged(params?: {
        filterInstance?: IFilterComp;
        additionalEventAttributes?: any;
    }): AgPromise<void>;
    updateAfterFilterChanged(): void;
    isSuppressFlashingCellsBecauseFiltering(): boolean;
    private onNewRowsLoaded;
    private createValueGetter;
    private createGetValue;
    isFilterActive(column: AgColumn): boolean;
    getOrCreateFilterWrapper(column: AgColumn): FilterWrapper | null;
    private cachedFilter;
    private getDefaultFilter;
    getDefaultFloatingFilter(column: AgColumn): string;
    private createFilterInstance;
    createFilterParams(column: AgColumn, colDef: ColDef): IFilterParams;
    private createFilterWrapper;
    private onColumnsChanged;
    private updateDependentFilters;
    isFilterAllowed(column: AgColumn): boolean;
    getFloatingFilterCompDetails(column: AgColumn, showParentFilter: () => void): UserCompDetails | undefined;
    getCurrentFloatingFilterParentModel(column: AgColumn): any;
    destroyFilter(column: AgColumn, source?: 'api' | 'columnChanged' | 'paramsUpdated'): void;
    private disposeColumnListener;
    private disposeFilterWrapper;
    private filterModifiedCallbackFactory;
    private filterChangedCallbackFactory;
    private checkDestroyFilter;
    private setColumnFilterWrapper;
    areFilterCompsDifferent(oldCompDetails: UserCompDetails | null, newCompDetails: UserCompDetails | null): boolean;
    hasFloatingFilters(): boolean;
    getFilterInstance<TFilter extends IFilter>(key: string | AgColumn, callback?: (filter: TFilter | null) => void): undefined;
    getColumnFilterInstance<TFilter extends IFilter>(key: string | AgColumn): Promise<TFilter | null | undefined>;
    private getFilterInstanceImpl;
    private processFilterModelUpdateQueue;
    getColumnFilterModel(key: string | AgColumn): any;
    setColumnFilterModel(key: string | AgColumn, model: any): Promise<void>;
    private getFilterWrapper;
    setColDefPropertiesForDataType(colDef: ColDef, dataTypeDefinition: CoreDataTypeDefinition, formatValue: DataTypeFormatValueFunc): void;
    destroy(): void;
}
export interface FilterWrapper {
    compiledElement: any;
    column: AgColumn;
    filterPromise: AgPromise<IFilterComp> | null;
    filter?: IFilterComp;
    compDetails: UserCompDetails | null;
}
