import type { NamedBean } from '../context/bean';
import { BeanStub } from '../context/beanStub';
import type { BeanCollection } from '../context/context';
import type { RowNode } from '../entities/rowNode';
export type QuickFilterServiceEvent = 'quickFilterChanged';
export declare class QuickFilterService extends BeanStub<QuickFilterServiceEvent> implements NamedBean {
    beanName: "quickFilterService";
    private valueService;
    private columnModel;
    private rowModel;
    private pivotResultColsService;
    wireBeans(beans: BeanCollection): void;
    private colsForQuickFilter;
    private quickFilter;
    private quickFilterParts;
    private parser?;
    private matcher?;
    postConstruct(): void;
    refreshQuickFilterCols(): void;
    isQuickFilterPresent(): boolean;
    doesRowPassQuickFilter(node: RowNode): boolean;
    resetQuickFilterCache(): void;
    private setQuickFilterParts;
    private parseQuickFilter;
    private setQuickFilter;
    private setQuickFilterParserAndMatcher;
    private onQuickFilterColumnConfigChanged;
    private doesRowPassQuickFilterNoCache;
    private doesRowPassQuickFilterCache;
    private doesRowPassQuickFilterMatcher;
    private checkGenerateQuickFilterAggregateText;
    private getQuickFilterTextForColumn;
    private getQuickFilterAggregateText;
}
