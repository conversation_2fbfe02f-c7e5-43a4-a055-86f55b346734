export interface ComponentType {
    propertyName: string;
    cellRenderer: boolean;
}
export declare const DateComponent: ComponentType;
export declare const DragAndDropImageComponent: ComponentType;
export declare const HeaderComponent: ComponentType;
export declare const HeaderGroupComponent: ComponentType;
export declare const CellRendererComponent: ComponentType;
export declare const EditorRendererComponent: ComponentType;
export declare const LoadingCellRendererComponent: ComponentType;
export declare const CellEditorComponent: ComponentType;
export declare const InnerRendererComponent: ComponentType;
export declare const LoadingOverlayComponent: ComponentType;
export declare const NoRowsOverlayComponent: ComponentType;
export declare const TooltipComponent: ComponentType;
export declare const FilterComponent: ComponentType;
export declare const FloatingFilterComponent: ComponentType;
export declare const ToolPanelComponent: ComponentType;
export declare const StatusPanelComponent: ComponentType;
export declare const FullWidth: ComponentType;
export declare const FullWidthLoading: ComponentType;
export declare const FullWidthGroup: ComponentType;
export declare const FullWidthDetail: ComponentType;
export declare const MenuItemComponent: ComponentType;
