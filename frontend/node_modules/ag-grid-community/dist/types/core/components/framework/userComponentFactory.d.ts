import type { Named<PERSON><PERSON> } from '../../context/bean';
import { BeanStub } from '../../context/beanStub';
import type { BeanCollection } from '../../context/context';
import type { IDragAndDropImageParams } from '../../dragAndDrop/dragAndDropImageComponent';
import type { ColDef, ColGroupDef } from '../../entities/colDef';
import type { GridOptions } from '../../entities/gridOptions';
import type { IFloatingFilterParams } from '../../filter/floating/floatingFilter';
import type { IHeaderParams } from '../../headerRendering/cells/column/headerComp';
import type { IHeaderGroupParams } from '../../headerRendering/cells/columnGroup/headerGroupComp';
import type { IDateParams } from '../../interfaces/dateComponent';
import type { GroupCellRendererParams } from '../../interfaces/groupCellRenderer';
import type { ICellEditorParams } from '../../interfaces/iCellEditor';
import type { AgGridCommon, WithoutGridCommon } from '../../interfaces/iCommon';
import type { IFilterDef, IFilterParams } from '../../interfaces/iFilter';
import type { IFrameworkOverrides } from '../../interfaces/iFrameworkOverrides';
import type { RichSelectParams } from '../../interfaces/iRichCellEditorParams';
import type { SetFilterParams } from '../../interfaces/iSetFilter';
import type { ToolPanelDef } from '../../interfaces/iSideBar';
import type { IStatusPanelParams, StatusPanelDef } from '../../interfaces/iStatusPanel';
import type { IToolPanelParams } from '../../interfaces/iToolPanel';
import type { IMenuItemParams, MenuItemDef } from '../../interfaces/menuItem';
import type { ICellRendererParams, ISetFilterCellRendererParams } from '../../rendering/cellRenderers/iCellRenderer';
import type { ILoadingOverlayParams } from '../../rendering/overlays/loadingOverlayComponent';
import type { INoRowsOverlayParams } from '../../rendering/overlays/noRowsOverlayComponent';
import type { ITooltipParams } from '../../rendering/tooltipComponent';
import { AgPromise } from '../../utils/promise';
import type { ComponentType } from './componentTypes';
export type DefinitionObject = GridOptions | ColDef | ColGroupDef | IFilterDef | SetFilterParams | RichSelectParams | ToolPanelDef | StatusPanelDef | MenuItemDef;
export interface UserCompDetails {
    componentClass: any;
    componentFromFramework: boolean;
    params: any;
    type: ComponentType;
    popupFromSelector?: boolean;
    popupPositionFromSelector?: 'over' | 'under';
    newAgStackInstance: () => AgPromise<any>;
}
export declare class UserComponentFactory extends BeanStub implements NamedBean {
    beanName: "userComponentFactory";
    private gridOptions;
    private agComponentUtils;
    private componentMetadataProvider;
    private userComponentRegistry;
    private frameworkComponentWrapper?;
    wireBeans(beans: BeanCollection): void;
    getDragAndDropImageCompDetails(params: WithoutGridCommon<IDragAndDropImageParams>): UserCompDetails;
    getHeaderCompDetails(colDef: ColDef, params: WithoutGridCommon<IHeaderParams>): UserCompDetails | undefined;
    getHeaderGroupCompDetails(params: WithoutGridCommon<IHeaderGroupParams>): UserCompDetails | undefined;
    getFullWidthCellRendererDetails(params: WithoutGridCommon<ICellRendererParams>): UserCompDetails;
    getFullWidthLoadingCellRendererDetails(params: WithoutGridCommon<ICellRendererParams>): UserCompDetails;
    getFullWidthGroupCellRendererDetails(params: WithoutGridCommon<ICellRendererParams>): UserCompDetails;
    getFullWidthDetailCellRendererDetails(params: WithoutGridCommon<ICellRendererParams>): UserCompDetails;
    getInnerRendererDetails(def: GroupCellRendererParams, params: WithoutGridCommon<ICellRendererParams>): UserCompDetails | undefined;
    getFullWidthGroupRowInnerCellRenderer(def: any, params: WithoutGridCommon<ICellRendererParams>): UserCompDetails | undefined;
    getCellRendererDetails(def: ColDef, params: WithoutGridCommon<ICellRendererParams>): UserCompDetails | undefined;
    getEditorRendererDetails<TDefinition, TEditorParams extends AgGridCommon<any, any>>(def: TDefinition, params: WithoutGridCommon<TEditorParams>): UserCompDetails | undefined;
    getLoadingCellRendererDetails(def: ColDef, params: WithoutGridCommon<ICellRendererParams>): UserCompDetails | undefined;
    getCellEditorDetails(def: ColDef, params: WithoutGridCommon<ICellEditorParams>): UserCompDetails | undefined;
    getFilterDetails(def: IFilterDef, params: WithoutGridCommon<IFilterParams>, defaultFilter: string): UserCompDetails | undefined;
    getDateCompDetails(params: WithoutGridCommon<IDateParams>): UserCompDetails;
    getLoadingOverlayCompDetails(params: WithoutGridCommon<ILoadingOverlayParams>): UserCompDetails;
    getNoRowsOverlayCompDetails(params: WithoutGridCommon<INoRowsOverlayParams>): UserCompDetails;
    getTooltipCompDetails(params: WithoutGridCommon<ITooltipParams>): UserCompDetails;
    getSetFilterCellRendererDetails<TData, V>(def: SetFilterParams<TData, V>, params: WithoutGridCommon<ISetFilterCellRendererParams>): UserCompDetails | undefined;
    getFloatingFilterCompDetails(def: IFilterDef, params: WithoutGridCommon<IFloatingFilterParams<any>>, defaultFloatingFilter: string | null): UserCompDetails | undefined;
    getToolPanelCompDetails(toolPanelDef: ToolPanelDef, params: WithoutGridCommon<IToolPanelParams>): UserCompDetails;
    getStatusPanelCompDetails(def: StatusPanelDef, params: WithoutGridCommon<IStatusPanelParams>): UserCompDetails;
    getMenuItemCompDetails(def: MenuItemDef, params: WithoutGridCommon<IMenuItemParams>): UserCompDetails;
    private getCompDetails;
    static getCompKeys<TDefinition = DefinitionObject>(frameworkOverrides: IFrameworkOverrides, defObject: TDefinition, type: ComponentType, params?: any): {
        compName?: string;
        jsComp: any;
        fwComp: any;
        paramsFromSelector: any;
        popupFromSelector?: boolean;
        popupPositionFromSelector?: 'over' | 'under';
    };
    private newAgStackInstance;
    mergeParamsWithApplicationProvidedParams<TDefinition = DefinitionObject>(defObject: TDefinition, type: ComponentType, paramsFromGrid: any, paramsFromSelector?: any, defaultCompParams?: any): any;
    private initComponent;
}
