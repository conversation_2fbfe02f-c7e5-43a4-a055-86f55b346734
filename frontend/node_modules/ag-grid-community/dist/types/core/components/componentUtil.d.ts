import type { GridApi } from '../api/gridApi';
import type { GridOptions } from '../entities/gridOptions';
export declare class ComponentUtil {
    static VUE_OMITTED_PROPERTY: string;
    static PUBLIC_EVENTS: readonly ["columnEverythingChanged", "newColumnsLoaded", "columnPivotModeChanged", "pivotMaxColumnsExceeded", "columnRowGroupChanged", "expandOrCollapseAll", "columnPivotChanged", "gridColumnsChanged", "columnValueChanged", "columnMoved", "columnVisible", "columnPinned", "columnGroupOpened", "columnResized", "displayedColumnsChanged", "virtualColumnsChanged", "columnHeaderMouseOver", "columnHeaderMouseLeave", "columnHeaderClicked", "columnHeaderContextMenu", "asyncTransactionsFlushed", "rowGroupOpened", "rowDataUpdated", "pinnedRowDataChanged", "rangeSelectionChanged", "cellSelectionChanged", "chartCreated", "chartRangeSelectionChanged", "chartOptionsChanged", "chartDestroyed", "toolPanelVisibleChanged", "toolPanelSizeChanged", "modelUpdated", "cutStart", "cutEnd", "pasteStart", "pasteEnd", "fillStart", "fillEnd", "cellSelectionDeleteStart", "cellSelectionDeleteEnd", "rangeDeleteStart", "rangeDeleteEnd", "undoStarted", "undoEnded", "redoStarted", "redoEnded", "cellClicked", "cellDoubleClicked", "cellMouseDown", "cellContextMenu", "cellValueChanged", "cellEditRequest", "rowValueChanged", "headerFocused", "cellFocused", "rowSelected", "selectionChanged", "tooltipShow", "tooltipHide", "cellKeyDown", "cellMouseOver", "cellMouseOut", "filterChanged", "filterModified", "filterOpened", "advancedFilterBuilderVisibleChanged", "sortChanged", "virtualRowRemoved", "rowClicked", "rowDoubleClicked", "gridReady", "gridPreDestroyed", "gridSizeChanged", "viewportChanged", "firstDataRendered", "dragStarted", "dragStopped", "dragCancelled", "rowEditingStarted", "rowEditingStopped", "cellEditingStarted", "cellEditingStopped", "bodyScroll", "bodyScrollEnd", "paginationChanged", "componentStateChanged", "storeRefreshed", "stateUpdated", "columnMenuVisibleChanged", "contextMenuVisibleChanged", "rowDragEnter", "rowDragMove", "rowDragLeave", "rowDragEnd", "rowDragCancel"];
    static getCallbackForEvent(eventName: string): string;
    static EVENT_CALLBACKS: string[];
    static BOOLEAN_PROPERTIES: ("suppressMovableColumns" | "suppressMenuHide" | "suppressFieldDotNotation" | "api" | "sideBar" | "suppressContextMenu" | "preventDefaultOnContextMenu" | "allowContextMenuWithControlKey" | "enableBrowserTooltips" | "tooltipMouseTrack" | "tooltipInteraction" | "copyHeadersToClipboard" | "copyGroupHeadersToClipboard" | "suppressCopyRowsToClipboard" | "suppressCopySingleCellRanges" | "suppressLastEmptyLineOnPaste" | "suppressClipboardPaste" | "suppressClipboardApi" | "suppressCutToClipboard" | "maintainColumnOrder" | "enableStrictPivotColumnOrder" | "allowDragFromColumnsToolPanel" | "suppressColumnMoveAnimation" | "suppressMoveWhenColumnDragging" | "suppressDragLeaveHidesColumns" | "suppressRowGroupHidesColumns" | "suppressAutoSize" | "skipHeaderOnAutoSize" | "singleClickEdit" | "suppressClickEdit" | "readOnlyEdit" | "stopEditingWhenCellsLoseFocus" | "enterNavigatesVertically" | "enterNavigatesVerticallyAfterEdit" | "enableCellEditingOnBackspace" | "undoRedoCellEditing" | "suppressCsvExport" | "suppressExcelExport" | "cacheQuickFilter" | "includeHiddenColumnsInQuickFilter" | "applyQuickFilterBeforePivotOrAgg" | "excludeChildrenWhenTreeDataFiltering" | "enableAdvancedFilter" | "includeHiddenColumnsInAdvancedFilter" | "suppressAdvancedFilterEval" | "suppressSetFilterByDefault" | "enableCharts" | "masterDetail" | "keepDetailRows" | "detailRowAutoHeight" | "valueCache" | "valueCacheNeverExpires" | "enableCellExpressions" | "suppressTouch" | "suppressFocusAfterRefresh" | "suppressAsyncEvents" | "suppressBrowserResizeObserver" | "suppressPropertyNamesCheck" | "suppressChangeDetection" | "debug" | "loading" | "suppressLoadingOverlay" | "suppressNoRowsOverlay" | "pagination" | "paginationPageSizeSelector" | "paginationAutoPageSize" | "paginateChildRows" | "suppressPaginationPanel" | "pivotMode" | "pivotSuppressAutoColumn" | "suppressExpandablePivotGroups" | "functionsReadOnly" | "suppressAggFuncInHeader" | "alwaysAggregateAtRootLevel" | "aggregateOnlyChangedColumns" | "suppressAggFilteredOnly" | "removePivotHeaderRowWhenSingleValueColumn" | "animateRows" | "enableCellChangeFlash" | "allowShowChangeAfterFilter" | "ensureDomOrder" | "enableRtl" | "suppressColumnVirtualisation" | "suppressMaxRenderedRowRestriction" | "suppressRowVirtualisation" | "rowDragManaged" | "suppressRowDrag" | "suppressMoveWhenRowDragging" | "rowDragEntireRow" | "rowDragMultiRow" | "embedFullWidthRows" | "suppressGroupMaintainValueType" | "groupMaintainOrder" | "groupSelectsChildren" | "groupAggFiltering" | "groupIncludeFooter" | "groupIncludeTotalFooter" | "suppressStickyTotalRow" | "groupSuppressBlankHeader" | "groupSelectsFiltered" | "showOpenedGroup" | "groupRemoveSingleChildren" | "groupRemoveLowestSingleChildren" | "groupHideOpenParents" | "groupAllowUnbalanced" | "suppressMakeColumnVisibleAfterUnGroup" | "treeData" | "rowGroupPanelSuppressSort" | "suppressGroupRowsSticky" | "suppressModelUpdateAfterUpdateTransaction" | "suppressServerSideInfiniteScroll" | "suppressServerSideFullWidthLoadingRow" | "purgeClosedRowNodes" | "serverSideSortAllLevels" | "serverSideEnableClientSideSort" | "serverSideOnlyRefreshFilteredGroups" | "serverSideSortOnServer" | "serverSideFilterOnServer" | "alwaysShowHorizontalScroll" | "alwaysShowVerticalScroll" | "debounceVerticalScrollbar" | "suppressHorizontalScroll" | "suppressScrollOnNewData" | "suppressScrollWhenPopupsAreOpen" | "suppressAnimationFrame" | "suppressMiddleClickScrolls" | "suppressPreventDefaultOnMouseWheel" | "cellSelection" | "rowMultiSelectWithClick" | "suppressRowDeselection" | "suppressRowClickSelection" | "suppressCellFocus" | "suppressHeaderFocus" | "suppressMultiRangeSelection" | "enableCellTextSelection" | "enableRangeSelection" | "enableRangeHandle" | "enableFillHandle" | "suppressClearOnFillReduction" | "accentedSort" | "unSortIcon" | "suppressMultiSort" | "alwaysMultiSort" | "suppressMaintainUnsortedOrder" | "suppressRowHoverHighlight" | "suppressRowTransform" | "columnHoverHighlight" | "deltaSort" | "enableGroupEdit" | "reactiveCustomComponents" | "loadThemeGoogleFonts" | "resetRowDataOnUpdate")[];
    static ALL_PROPERTIES: (keyof GridOptions<any>)[];
    static ALL_PROPERTIES_AND_CALLBACKS: string[];
    static ALL_PROPERTIES_AND_CALLBACKS_SET: Set<string>;
}
/** Combines component props / attributes with the provided gridOptions returning a new combined gridOptions object */
export declare function _combineAttributesAndGridOptions(gridOptions: GridOptions | undefined, component: any): GridOptions;
export declare function _processOnChange(changes: any, api: GridApi): void;
