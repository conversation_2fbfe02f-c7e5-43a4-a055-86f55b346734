import type { BeanCollection } from '../context/context';
import type { FocusableContainer } from '../interfaces/iFocusableContainer';
import type { ComponentSelector } from '../widgets/component';
import { TabGuardComp } from '../widgets/tabGuardComp';
export declare class PaginationComp extends TabGuardComp implements FocusableContainer {
    private rowModel;
    private paginationService;
    private focusService;
    private ariaAnnouncementService;
    wireBeans(beans: BeanCollection): void;
    private readonly btFirst;
    private readonly btPrevious;
    private readonly btNext;
    private readonly btLast;
    private readonly lbRecordCount;
    private readonly lbFirstRowOnPage;
    private readonly lbLastRowOnPage;
    private readonly lbCurrent;
    private readonly lbTotal;
    private readonly pageSizeComp;
    private previousAndFirstButtonsDisabled;
    private nextButtonDisabled;
    private lastButtonDisabled;
    private areListenersSetup;
    private allowFocusInnerElement;
    private ariaRowStatus;
    private ariaPageStatus;
    constructor();
    postConstruct(): void;
    setAllowFocus(allowFocus: boolean): void;
    private onPaginationChanged;
    private onPageSizeRelatedOptionsChange;
    private setupListeners;
    private onBtFirst;
    private formatNumber;
    private getTemplate;
    private onBtNext;
    private onBtPrevious;
    private onBtLast;
    private enableOrDisableButtons;
    private toggleButtonDisabled;
    private isZeroPagesToDisplay;
    private updateLabels;
    private announceAriaStatus;
    private setTotalLabelsToZero;
}
export declare const PaginationSelector: ComponentSelector;
