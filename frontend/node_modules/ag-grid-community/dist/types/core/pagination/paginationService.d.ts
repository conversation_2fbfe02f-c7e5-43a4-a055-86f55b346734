import type { NamedBean } from '../context/bean';
import { BeanStub } from '../context/beanStub';
import type { BeanCollection } from '../context/context';
import type { RowNode } from '../entities/rowNode';
import type { RowPosition } from '../entities/rowPositionUtils';
import type { ComponentSelector } from '../widgets/component';
export declare class PaginationService extends BeanStub implements NamedBean {
    beanName: "paginationService";
    private rowModel;
    private pageBoundsService;
    wireBeans(beans: BeanCollection): void;
    private active;
    private paginateChildRows;
    private pageSizeAutoCalculated?;
    private pageSizeFromPageSizeSelector?;
    private pageSizeFromInitialState?;
    private pageSizeFromGridOptions?;
    private defaultPageSize;
    private totalPages;
    private currentPage;
    private topDisplayedRowIndex;
    private bottomDisplayedRowIndex;
    private masterRowCount;
    postConstruct(): void;
    getPaginationSelector(): ComponentSelector;
    private isPaginateChildRows;
    private onPaginationGridOptionChanged;
    private onPageSizeGridOptionChanged;
    goToPage(page: number): void;
    isRowPresent(rowNode: RowNode): boolean;
    private getPageForIndex;
    goToPageWithIndex(index: any): void;
    isRowInPage(row: RowPosition): boolean;
    getCurrentPage(): number;
    goToNextPage(): void;
    goToPreviousPage(): void;
    goToFirstPage(): void;
    goToLastPage(): void;
    getPageSize(): number;
    getTotalPages(): number;
    /** This is only for state setting before data has been loaded */
    setPage(page: number): void;
    private get pageSize();
    calculatePages(): void;
    unsetAutoCalculatedPageSize(): void;
    setPageSize(size: number | undefined, source: 'autoCalculated' | 'pageSizeSelector' | 'initialState' | 'gridOptions'): void;
    private setZeroRows;
    private adjustCurrentPageIfInvalid;
    private calculatePagesMasterRowsOnly;
    getMasterRowCount(): number;
    private calculatePagesAllRows;
    private calculatedPagesNotActive;
    private dispatchPaginationChangedEvent;
}
