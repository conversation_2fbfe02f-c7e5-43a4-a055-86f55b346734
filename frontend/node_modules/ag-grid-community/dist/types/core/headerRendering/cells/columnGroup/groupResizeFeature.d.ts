import { BeanStub } from '../../../context/beanStub';
import type { BeanCollection } from '../../../context/context';
import type { AgColumn } from '../../../entities/agColumn';
import type { AgColumnGroup } from '../../../entities/agColumnGroup';
import type { ColumnEventType } from '../../../events';
import type { ColumnPinnedType } from '../../../interfaces/iColumn';
import type { IHeaderResizeFeature } from '../abstractCell/abstractHeaderCellCtrl';
import type { IHeaderGroupCellComp } from './headerGroupCellCtrl';
interface ColumnSizeAndRatios {
    columnsToResize: AgColumn[];
    resizeStartWidth: number;
    resizeRatios: number[];
    groupAfterColumns?: AgColumn[];
    groupAfterStartWidth?: number;
    groupAfterRatios?: number[];
}
export declare class GroupResizeFeature extends BeanStub implements IHeaderResizeFeature {
    private horizontalResizeService;
    private autoWidthCalculator;
    private visibleColsService;
    private columnSizeService;
    private columnAutosizeService;
    wireBeans(beans: BeanCollection): void;
    private eResize;
    private columnGroup;
    private comp;
    private pinned;
    private resizeCols?;
    private resizeStartWidth;
    private resizeRatios?;
    private resizeTakeFromCols?;
    private resizeTakeFromStartWidth?;
    private resizeTakeFromRatios?;
    constructor(comp: IHeaderGroupCellComp, eResize: HTMLElement, pinned: ColumnPinnedType, columnGroup: AgColumnGroup);
    postConstruct(): void;
    private onResizeStart;
    onResizing(finished: boolean, resizeAmount: any, source?: ColumnEventType): void;
    getInitialValues(shiftKey?: boolean): ColumnSizeAndRatios;
    private storeLocalValues;
    private clearLocalValues;
    resizeLeafColumnsToFit(source: ColumnEventType): void;
    private resizeColumnsFromLocalValues;
    resizeColumns(initialValues: ColumnSizeAndRatios, totalWidth: number, source: ColumnEventType, finished?: boolean): void;
    toggleColumnResizing(resizing: boolean): void;
    private getColumnsToResize;
    private getInitialSizeOfColumns;
    private getSizeRatiosOfColumns;
    private normaliseDragChange;
    destroy(): void;
}
export {};
