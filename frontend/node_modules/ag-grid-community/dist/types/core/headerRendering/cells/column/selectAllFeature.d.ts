import { BeanStub } from '../../../context/beanStub';
import type { BeanCollection } from '../../../context/context';
import type { AgColumn } from '../../../entities/agColumn';
import type { HeaderCellCtrl } from './headerCellCtrl';
export declare class SelectAllFeature extends BeanStub {
    private rowModel;
    private selectionService;
    wireBeans(beans: BeanCollection): void;
    private cbSelectAllVisible;
    private processingEventFromCheckbox;
    private column;
    private headerCellCtrl;
    private cbSelectAll;
    constructor(column: AgColumn);
    onSpaceKeyDown(e: KeyboardEvent): void;
    getCheckboxGui(): HTMLElement;
    setComp(ctrl: HeaderCellCtrl): void;
    private onNewColumnsLoaded;
    private onDisplayedColumnsChanged;
    private showOrHideSelectAll;
    private onModelChanged;
    private onSelectionChanged;
    private updateStateOfCheckbox;
    private refreshSelectAllLabel;
    private checkSelectionType;
    private checkRightRowModelType;
    private onCbSelectAll;
    /**
     * Checkbox is enabled when either the `headerCheckbox` option is enabled in the new selection API
     * or `headerCheckboxSelection` is enabled in the legacy API.
     */
    private isCheckboxSelection;
    private isFilteredOnly;
    private isCurrentPageOnly;
}
