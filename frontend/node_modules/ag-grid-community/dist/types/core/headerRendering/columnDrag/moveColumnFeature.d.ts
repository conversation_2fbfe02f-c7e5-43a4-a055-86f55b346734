import { BeanStub } from '../../context/beanStub';
import type { BeanCollection } from '../../context/context';
import type { DragAndDropIcon, DraggingEvent } from '../../dragAndDrop/dragAndDropService';
import type { AgColumn } from '../../entities/agColumn';
import type { ColumnEventType } from '../../events';
import type { ColumnPinnedType } from '../../interfaces/iColumn';
import type { DropListener } from './bodyDropTarget';
export declare class MoveColumnFeature extends BeanStub implements DropListener {
    private columnModel;
    private visibleColsService;
    private columnMoveService;
    private dragAndDropService;
    private ctrlsService;
    wireBeans(beans: BeanCollection): void;
    private gridBodyCon;
    private needToMoveLeft;
    private needToMoveRight;
    private movingIntervalId;
    private intervalCount;
    private pinned;
    private isCenterContainer;
    private lastDraggingEvent;
    private lastHighlightedColumn;
    private lastMovedInfo;
    private failedMoveAttempts;
    constructor(pinned: ColumnPinnedType);
    postConstruct(): void;
    getIconName(): DragAndDropIcon;
    onDragEnter(draggingEvent: DraggingEvent): void;
    onDragging(draggingEvent?: DraggingEvent | null, fromEnter?: boolean, fakeEvent?: boolean, finished?: boolean): void;
    onDragLeave(): void;
    onDragStop(): void;
    onDragCancel(): void;
    setColumnsVisible(columns: AgColumn[] | null | undefined, visible: boolean, source: ColumnEventType): void;
    private finishColumnMoving;
    private handleColumnDragWhileSuppressingMovement;
    private handleColumnDragWhileAllowingMovement;
    private getAllMovingColumns;
    private getMoveColumnParams;
    private findFirstAndLastMovingColumns;
    private highlightHoveredColumn;
    private getNormalisedXPositionInfo;
    private getColumnMoveAndTargetInfo;
    private normaliseDirection;
    private getNormalisedColumnLeft;
    private isAttemptingToPin;
    private moveColumnsAfterHighlight;
    private clearHighlighted;
    private checkCenterForScrolling;
    private ensureIntervalStarted;
    private ensureIntervalCleared;
    private moveInterval;
    private getPinDirection;
    private attemptToPinColumns;
    destroy(): void;
}
