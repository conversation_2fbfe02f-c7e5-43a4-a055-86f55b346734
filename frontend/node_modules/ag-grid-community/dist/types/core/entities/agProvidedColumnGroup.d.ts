import { BeanStub } from '../context/beanStub';
import type { Column, ColumnGroupShowType, ColumnInstanceId, ProvidedColumnGroup } from '../interfaces/iColumn';
import type { AgColumn } from './agColumn';
import type { ColGroupDef } from './colDef';
export declare function isProvidedColumnGroup(col: Column | ProvidedColumnGroup | string | null): col is AgProvidedColumnGroup;
export type AgProvidedColumnGroupEvent = 'expandedChanged' | 'expandableChanged';
export declare class AgProvidedColumnGroup extends BeanStub<AgProvidedColumnGroupEvent> implements ProvidedColumnGroup {
    readonly isColumn: false;
    private colGroupDef;
    private originalParent;
    private children;
    private groupId;
    private expandable;
    private expanded;
    private padding;
    private level;
    private instanceId;
    private expandableListenerRemoveCallback;
    constructor(colGroupDef: ColGroupDef | null, groupId: string, padding: boolean, level: number);
    destroy(): void;
    private reset;
    getInstanceId(): ColumnInstanceId;
    setOriginalParent(originalParent: AgProvidedColumnGroup | null): void;
    getOriginalParent(): AgProvidedColumnGroup | null;
    getLevel(): number;
    isVisible(): boolean;
    isPadding(): boolean;
    setExpanded(expanded: boolean | undefined): void;
    isExpandable(): boolean;
    isExpanded(): boolean;
    getGroupId(): string;
    getId(): string;
    setChildren(children: (AgColumn | AgProvidedColumnGroup)[]): void;
    getChildren(): (AgColumn | AgProvidedColumnGroup)[];
    getColGroupDef(): ColGroupDef | null;
    getLeafColumns(): AgColumn[];
    private addLeafColumns;
    getColumnGroupShow(): ColumnGroupShowType | undefined;
    setupExpandable(): void;
    setExpandable(): void;
    private findChildrenRemovingPadding;
    private onColumnVisibilityChanged;
}
