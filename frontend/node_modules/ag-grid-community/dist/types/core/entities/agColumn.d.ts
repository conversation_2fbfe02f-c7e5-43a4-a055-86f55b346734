import { BeanStub } from '../context/beanStub';
import type { BeanCollection } from '../context/context';
import type { ColumnEvent, ColumnEventType } from '../events';
import type { Column, ColumnEventName, ColumnGroup, ColumnGroupShowType, ColumnHighlightPosition, ColumnInstanceId, ColumnPinnedType, HeaderColumnId, ProvidedColumnGroup } from '../interfaces/iColumn';
import type { IRowNode } from '../interfaces/iRowNode';
import type { AgColumnGroup } from './agColumnGroup';
import type { AgProvidedColumnGroup } from './agProvidedColumnGroup';
import type { AbstractColDef, ColDef, ColumnFunctionCallbackParams, IAggFunc, SortDirection } from './colDef';
export declare function getNextColInstanceId(): ColumnInstanceId;
export declare function isColumn(col: Column | ColumnGroup | ProvidedColumnGroup): col is AgColumn;
export declare class AgColumn<TValue = any> extends BeanStub<ColumnEventName> implements Column {
    readonly isColumn: true;
    private columnHoverService;
    wireBeans(beans: BeanCollection): void;
    private frameworkEventListenerService;
    private readonly colId;
    private colDef;
    private instanceId;
    private userProvidedColDef;
    private actualWidth;
    private autoHeaderHeight;
    private visible;
    private pinned;
    private left;
    private oldLeft;
    private aggFunc;
    private sort;
    private sortIndex;
    private moving;
    private menuVisible;
    private highlighted;
    private lastLeftPinned;
    private firstRightPinned;
    private minWidth;
    private maxWidth;
    private filterActive;
    private columnEventService;
    private fieldContainsDots;
    private tooltipFieldContainsDots;
    private tooltipEnabled;
    private rowGroupActive;
    private pivotActive;
    private aggregationActive;
    private flex;
    private readonly primary;
    private parent;
    private originalParent;
    constructor(colDef: ColDef<any, TValue>, userProvidedColDef: ColDef<any, TValue> | null, colId: string, primary: boolean);
    getInstanceId(): ColumnInstanceId;
    private setState;
    setColDef(colDef: ColDef<any, TValue>, userProvidedColDef: ColDef<any, TValue> | null, source: ColumnEventType): void;
    getUserProvidedColDef(): ColDef<any, TValue> | null;
    setParent(parent: AgColumnGroup | null): void;
    getParent(): AgColumnGroup | null;
    setOriginalParent(originalParent: AgProvidedColumnGroup | null): void;
    getOriginalParent(): AgProvidedColumnGroup | null;
    postConstruct(): void;
    private initDotNotation;
    private initMinAndMaxWidths;
    private initTooltip;
    resetActualWidth(source: ColumnEventType): void;
    private calculateColInitialWidth;
    isEmptyGroup(): boolean;
    isRowGroupDisplayed(colId: string): boolean;
    isPrimary(): boolean;
    isFilterAllowed(): boolean;
    isFieldContainsDots(): boolean;
    isTooltipEnabled(): boolean;
    isTooltipFieldContainsDots(): boolean;
    getHighlighted(): ColumnHighlightPosition | null;
    addEventListener<T extends ColumnEventName>(eventType: T, userListener: (params: ColumnEvent<T>) => void): void;
    removeEventListener<T extends ColumnEventName>(eventType: T, userListener: (params: ColumnEvent<T>) => void): void;
    createColumnFunctionCallbackParams(rowNode: IRowNode): ColumnFunctionCallbackParams;
    isSuppressNavigable(rowNode: IRowNode): boolean;
    isCellEditable(rowNode: IRowNode): boolean;
    isSuppressFillHandle(): boolean;
    isAutoHeight(): boolean;
    isAutoHeaderHeight(): boolean;
    isRowDrag(rowNode: IRowNode): boolean;
    isDndSource(rowNode: IRowNode): boolean;
    isCellCheckboxSelection(rowNode: IRowNode): boolean;
    isSuppressPaste(rowNode: IRowNode): boolean;
    isResizable(): boolean;
    /** Get value from ColDef or default if it exists. */
    private getColDefValue;
    private isColumnFunc;
    setHighlighted(highlighted: ColumnHighlightPosition | null): void;
    setMoving(moving: boolean, source: ColumnEventType): void;
    private createColumnEvent;
    isMoving(): boolean;
    getSort(): SortDirection | undefined;
    setSort(sort: SortDirection | undefined, source: ColumnEventType): void;
    isSortable(): boolean;
    /** @deprecated v32 use col.getSort() === 'asc */
    isSortAscending(): boolean;
    /** @deprecated v32 use col.getSort() === 'desc */
    isSortDescending(): boolean;
    /** @deprecated v32 use col.getSort() === undefined */
    isSortNone(): boolean;
    /** @deprecated v32 use col.getSort() !== undefined */
    isSorting(): boolean;
    getSortIndex(): number | null | undefined;
    setSortIndex(sortOrder?: number | null): void;
    setMenuVisible(visible: boolean, source: ColumnEventType): void;
    isMenuVisible(): boolean;
    setAggFunc(aggFunc: string | IAggFunc | null | undefined): void;
    getAggFunc(): string | IAggFunc | null | undefined;
    getLeft(): number | null;
    getOldLeft(): number | null;
    getRight(): number;
    setLeft(left: number | null, source: ColumnEventType): void;
    isFilterActive(): boolean;
    setFilterActive(active: boolean, source: ColumnEventType, additionalEventAttributes?: any): void;
    isHovered(): boolean;
    setPinned(pinned: ColumnPinnedType): void;
    setFirstRightPinned(firstRightPinned: boolean, source: ColumnEventType): void;
    setLastLeftPinned(lastLeftPinned: boolean, source: ColumnEventType): void;
    isFirstRightPinned(): boolean;
    isLastLeftPinned(): boolean;
    isPinned(): boolean;
    isPinnedLeft(): boolean;
    isPinnedRight(): boolean;
    getPinned(): ColumnPinnedType;
    setVisible(visible: boolean, source: ColumnEventType): void;
    isVisible(): boolean;
    isSpanHeaderHeight(): boolean;
    getColumnGroupPaddingInfo(): {
        numberOfParents: number;
        isSpanningTotal: boolean;
    };
    getColDef(): ColDef<any, TValue>;
    getDefinition(): AbstractColDef<any, TValue>;
    getColumnGroupShow(): ColumnGroupShowType | undefined;
    getColId(): string;
    getId(): string;
    getUniqueId(): HeaderColumnId;
    getActualWidth(): number;
    getAutoHeaderHeight(): number | null;
    /** Returns true if the header height has changed */
    setAutoHeaderHeight(height: number): boolean;
    private createBaseColDefParams;
    getColSpan(rowNode: IRowNode): number;
    getRowSpan(rowNode: IRowNode): number;
    setActualWidth(actualWidth: number, source: ColumnEventType, silent?: boolean): void;
    fireColumnWidthChangedEvent(source: ColumnEventType): void;
    isGreaterThanMax(width: number): boolean;
    getMinWidth(): number;
    getMaxWidth(): number;
    getFlex(): number;
    setFlex(flex: number | null): void;
    setMinimum(source: ColumnEventType): void;
    setRowGroupActive(rowGroup: boolean, source: ColumnEventType): void;
    isRowGroupActive(): boolean;
    setPivotActive(pivot: boolean, source: ColumnEventType): void;
    isPivotActive(): boolean;
    isAnyFunctionActive(): boolean;
    isAnyFunctionAllowed(): boolean;
    setValueActive(value: boolean, source: ColumnEventType): void;
    isValueActive(): boolean;
    isAllowPivot(): boolean;
    isAllowValue(): boolean;
    isAllowRowGroup(): boolean;
    private dispatchStateUpdatedEvent;
}
