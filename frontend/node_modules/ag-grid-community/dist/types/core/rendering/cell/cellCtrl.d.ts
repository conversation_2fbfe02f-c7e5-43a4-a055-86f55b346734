import type { UserCompDetails } from '../../components/framework/userComponentFactory';
import { BeanStub } from '../../context/beanStub';
import type { BeanCollection } from '../../context/context';
import type { AgColumn } from '../../entities/agColumn';
import type { CellPosition } from '../../entities/cellPositionUtils';
import type { CellStyle } from '../../entities/colDef';
import type { RowNode } from '../../entities/rowNode';
import type { RowPosition } from '../../entities/rowPositionUtils';
import type { AgEventType } from '../../eventTypes';
import type { CellEvent, CellFocusedEvent, FlashCellsEvent } from '../../events';
import type { BrandedType } from '../../interfaces/brandedType';
import type { ICellEditor } from '../../interfaces/iCellEditor';
import type { CellChangedEvent } from '../../interfaces/iRowNode';
import type { <PERSON><PERSON><PERSON><PERSON>enderer } from '../cellRenderers/iCellRenderer';
import { CheckboxSelectionComponent } from '../checkboxSelectionComponent';
import { DndSourceComp } from '../dndSourceComp';
import type { RowCtrl } from '../row/rowCtrl';
import { RowDragComp } from '../row/rowDragComp';
import type { FlashCellsParams } from '../rowRenderer';
export interface ICellComp {
    addOrRemoveCssClass(cssClassName: string, on: boolean): void;
    setUserStyles(styles: CellStyle): void;
    getFocusableElement(): HTMLElement;
    setIncludeSelection(include: boolean): void;
    setIncludeRowDrag(include: boolean): void;
    setIncludeDndSource(include: boolean): void;
    getCellEditor(): ICellEditor | null;
    getCellRenderer(): ICellRenderer | null;
    getParentOfValue(): HTMLElement | null;
    setRenderDetails(compDetails: UserCompDetails | undefined, valueToDisplay: any, forceNewCellRendererInstance: boolean): void;
    setEditDetails(compDetails?: UserCompDetails, popup?: boolean, position?: 'over' | 'under', reactiveCustomComponents?: boolean): void;
}
export type CellCtrlInstanceId = BrandedType<string, 'CellCtrlInstanceId'>;
export declare class CellCtrl extends BeanStub {
    private readonly column;
    private readonly rowNode;
    private readonly beans;
    private readonly rowCtrl;
    static DOM_DATA_KEY_CELL_CTRL: string;
    readonly instanceId: CellCtrlInstanceId;
    readonly colIdSanitised: string;
    private eGui;
    private cellComp;
    private editCompDetails?;
    private focusEventToRestore;
    private printLayout;
    private value;
    private valueFormatted;
    private cellRangeFeature;
    private cellPositionFeature;
    private cellCustomStyleFeature;
    private tooltipFeature;
    private cellMouseListenerFeature;
    private cellKeyboardListenerFeature;
    private cellPosition;
    private editing;
    private includeSelection;
    private includeDndSource;
    private includeRowDrag;
    private isAutoHeight;
    private suppressRefreshCell;
    private customRowDragComp;
    private onCellCompAttachedFuncs;
    private onCellEditorAttachedFuncs;
    constructor(column: AgColumn, rowNode: RowNode, beans: BeanCollection, rowCtrl: RowCtrl);
    shouldRestoreFocus(): boolean;
    onFocusOut(): void;
    private addFeatures;
    private removeFeatures;
    private enableTooltipFeature;
    private disableTooltipFeature;
    setComp(comp: ICellComp, eGui: HTMLElement, eCellWrapper: HTMLElement | undefined, printLayout: boolean, startEditing: boolean, compBean: BeanStub | undefined): void;
    private setupAutoHeight;
    getCellAriaRole(): string;
    isCellRenderer(): boolean;
    getValueToDisplay(): any;
    private showValue;
    private setupControlComps;
    isForceWrapper(): boolean;
    private isIncludeControl;
    private isCheckboxSelection;
    private refreshShouldDestroy;
    startEditing(key?: string | null, cellStartedEdit?: boolean, event?: KeyboardEvent | MouseEvent | null): boolean;
    setEditing(editing: boolean, compDetails: UserCompDetails | undefined): void;
    stopRowOrCellEdit(cancel?: boolean): void;
    onPopupEditorClosed(): void;
    /**
     * Ends the Cell Editing
     * @param cancel `True` if the edit process is being canceled.
     * @returns `True` if the value of the `GridCell` has been updated, otherwise `False`.
     */
    stopEditing(cancel?: boolean): boolean;
    private createCellRendererParams;
    setFocusOutOnEditor(): void;
    setFocusInOnEditor(): void;
    onCellChanged(event: CellChangedEvent): void;
    refreshOrDestroyCell(params?: {
        suppressFlash?: boolean;
        newData?: boolean;
        forceRefresh?: boolean;
    }): void;
    refreshCell(params?: {
        suppressFlash?: boolean;
        newData?: boolean;
        forceRefresh?: boolean;
    }): void;
    stopEditingAndFocus(suppressNavigateAfterEdit?: boolean, shiftKey?: boolean): void;
    flashCell(delays?: Pick<FlashCellsParams, 'fadeDelay' | 'flashDelay' | 'fadeDuration' | 'flashDuration'>): void;
    private animateCell;
    onFlashCells(event: FlashCellsEvent): void;
    isCellEditable(): boolean;
    isSuppressFillHandle(): boolean;
    formatValue(value: any): any;
    private callValueFormatter;
    updateAndFormatValue(compareValues: boolean): boolean;
    private valuesAreEqual;
    getComp(): ICellComp;
    getValue(): any;
    private addDomData;
    createEvent<T extends AgEventType>(domEvent: Event | null, eventType: T): CellEvent<T>;
    processCharacter(event: KeyboardEvent): void;
    onKeyDown(event: KeyboardEvent): void;
    onMouseEvent(eventName: string, mouseEvent: MouseEvent): void;
    getGui(): HTMLElement;
    getColSpanningList(): AgColumn[];
    onLeftChanged(): void;
    onDisplayedColumnsChanged(): void;
    private refreshFirstAndLastStyles;
    private refreshAriaColIndex;
    isSuppressNavigable(): boolean;
    onWidthChanged(): void;
    getColumn(): AgColumn;
    getRowNode(): RowNode;
    isPrintLayout(): boolean;
    getCellPosition(): CellPosition;
    isEditing(): boolean;
    startRowOrCellEdit(key?: string | null, event?: KeyboardEvent | MouseEvent | null): boolean;
    getRowCtrl(): RowCtrl;
    getRowPosition(): RowPosition;
    updateRangeBordersIfRangeCount(): void;
    onCellSelectionChanged(): void;
    isRangeSelectionEnabled(): boolean;
    focusCell(forceBrowserFocus?: boolean): void;
    onRowIndexChanged(): void;
    onSuppressCellFocusChanged(suppressCellFocus: boolean): void;
    onFirstRightPinnedChanged(): void;
    onLastLeftPinnedChanged(): void;
    onCellFocused(event?: CellFocusedEvent): void;
    private createCellPosition;
    private applyStaticCssClasses;
    onColumnHover(): void;
    onColDefChanged(): void;
    private setWrapText;
    dispatchCellContextMenuEvent(event: Event | null): void;
    getCellRenderer(): ICellRenderer | null;
    getCellEditor(): ICellEditor | null;
    destroy(): void;
    createSelectionCheckbox(): CheckboxSelectionComponent;
    createDndSource(): DndSourceComp;
    registerRowDragger(customElement: HTMLElement, dragStartPixels?: number, suppressVisibilityChange?: boolean): void;
    createRowDragComp(customElement?: HTMLElement, dragStartPixels?: number, suppressVisibilityChange?: boolean): RowDragComp | undefined;
    setSuppressRefreshCell(suppressRefreshCell: boolean): void;
    getEditCompDetails(): UserCompDetails | undefined;
    onCellEditorAttached(callback: () => void): void;
    cellEditorAttached(): void;
}
