import type { BeanCollection } from '../../context/context';
import type { ICellEditorComp } from '../../interfaces/iCellEditor';
import { Component } from '../../widgets/component';
import type { TooltipParentComp } from '../../widgets/tooltipStateManager';
import type { ICellRendererComp } from './../cellRenderers/iCellRenderer';
import type { RowCtrl } from './../row/rowCtrl';
import type { CellCtrl } from './cellCtrl';
export declare class CellComp extends Component implements TooltipParentComp {
    private eCellWrapper;
    private eCellValue;
    private beans;
    private column;
    private rowNode;
    private eRow;
    private includeSelection;
    private includeRowDrag;
    private includeDndSource;
    private forceWrapper;
    private checkboxSelectionComp;
    private dndSourceComp;
    private rowDraggingComp;
    private hideEditorPopup;
    private cellEditorPopupWrapper;
    private cellEditor;
    private cellEditorGui;
    private cellRenderer;
    private cellRendererGui;
    private cellRendererClass;
    private rowCtrl;
    private cellCtrl;
    private firstRender;
    private rendererVersion;
    private editorVersion;
    constructor(beans: BeanCollection, cellCtrl: CellCtrl, printLayout: boolean, eRow: HTMLElement, editingRow: boolean);
    private getParentOfValue;
    private setRenderDetails;
    private setEditDetails;
    private removeControls;
    private refreshWrapper;
    private addControls;
    private createCellEditorInstance;
    private insertValueWithoutCellRenderer;
    private destroyEditorAndRenderer;
    private destroyRenderer;
    private destroyEditor;
    private refreshCellRenderer;
    private createCellRendererInstance;
    getCtrl(): CellCtrl;
    getRowCtrl(): RowCtrl | null;
    getCellRenderer(): ICellRendererComp | null | undefined;
    getCellEditor(): ICellEditorComp | null | undefined;
    private afterCellRendererCreated;
    private afterCellEditorCreated;
    private refreshEditStyles;
    private addInCellEditor;
    private addPopupCellEditor;
    detach(): void;
    destroy(): void;
    private clearParentOfValue;
}
