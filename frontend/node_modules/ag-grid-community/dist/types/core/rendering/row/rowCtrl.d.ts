import type { UserCompDetails } from '../../components/framework/userComponentFactory';
import { BeanStub } from '../../context/beanStub';
import type { BeanCollection } from '../../context/context';
import type { AgColumn } from '../../entities/agColumn';
import type { RowStyle } from '../../entities/gridOptions';
import type { RowNode } from '../../entities/rowNode';
import type { RowPosition } from '../../entities/rowPositionUtils';
import type { AgEventType } from '../../eventTypes';
import type { CellFocusedEvent, RowEvent } from '../../events';
import type { RowContainerType } from '../../gridBodyComp/rowContainer/rowContainerCtrl';
import type { BrandedType } from '../../interfaces/brandedType';
import type { RenderedRowEvent } from '../../interfaces/iCallbackParams';
import type { IEventListener } from '../../interfaces/iEventEmitter';
import type { IFrameworkOverrides } from '../../interfaces/iFrameworkOverrides';
import { CellCtrl } from '../cell/cellCtrl';
import type { ICellRenderer, ICellRendererParams } from '../cellRenderers/iCellRenderer';
export type RowCtrlInstanceId = BrandedType<string, 'RowCtrlInstanceId'>;
export interface IRowComp {
    setDomOrder(domOrder: boolean): void;
    addOrRemoveCssClass(cssClassName: string, on: boolean): void;
    setCellCtrls(cellCtrls: CellCtrl[], useFlushSync: boolean): void;
    showFullWidth(compDetails: UserCompDetails): void;
    getFullWidthCellRenderer(): ICellRenderer | null | undefined;
    setTop(top: string): void;
    setTransform(transform: string): void;
    setRowIndex(rowIndex: string): void;
    setRowId(rowId: string): void;
    setRowBusinessKey(businessKey: string): void;
    setUserStyles(styles: RowStyle | undefined): void;
    refreshFullWidth(getUpdatedParams: () => ICellRendererParams): boolean;
}
interface RowGui {
    rowComp: IRowComp;
    element: HTMLElement;
    containerType: RowContainerType;
    compBean: BeanStub;
}
export type RowCtrlEvent = RenderedRowEvent;
export declare class RowCtrl extends BeanStub<RowCtrlEvent> {
    static DOM_DATA_KEY_ROW_CTRL: string;
    readonly instanceId: RowCtrlInstanceId;
    private readonly rowNode;
    private readonly beans;
    private tooltipFeature;
    private rowType;
    private leftGui;
    private centerGui;
    private rightGui;
    private fullWidthGui;
    private allRowGuis;
    private firstRowOnPage;
    private lastRowOnPage;
    private active;
    private stoppingRowEdit;
    private editingRow;
    private rowFocused;
    private centerCellCtrls;
    private leftCellCtrls;
    private rightCellCtrls;
    private slideInAnimation;
    private fadeInAnimation;
    private rowDragComps;
    private readonly useAnimationFrameForCreate;
    private paginationPage;
    private lastMouseDownOnDragger;
    private rowLevel;
    private rowStyles;
    private readonly emptyStyle;
    private readonly printLayout;
    private readonly suppressRowTransform;
    private updateColumnListsPending;
    private rowId;
    private businessKeySanitised;
    private businessKeyForNodeFunc;
    constructor(rowNode: RowNode, beans: BeanCollection, animateIn: boolean, useAnimationFrameForCreate: boolean, printLayout: boolean);
    private initRowBusinessKey;
    private updateRowBusinessKey;
    getRowId(): string | null;
    getRowStyles(): RowStyle | undefined;
    private isSticky;
    private updateGui;
    setComp(rowComp: IRowComp, element: HTMLElement, containerType: RowContainerType, compBean: BeanStub<any> | undefined): void;
    unsetComp(containerType: RowContainerType): void;
    isCacheable(): boolean;
    setCached(cached: boolean): void;
    private initialiseRowComp;
    private setRowCompRowBusinessKey;
    getBusinessKey(): string | null;
    private setRowCompRowId;
    private executeSlideAndFadeAnimations;
    private addRowDraggerToRow;
    private setupFullWidth;
    isPrintLayout(): boolean;
    getFullWidthCellRenderers(): (ICellRenderer<any> | null | undefined)[];
    getCellElement(column: AgColumn): HTMLElement | null;
    private executeProcessRowPostCreateFunc;
    private areAllContainersReady;
    private isNodeFullWidthCell;
    private setRowType;
    private updateColumnLists;
    private createCellCtrls;
    private updateColumnListsImpl;
    private setCellCtrls;
    private getCellCtrlsForContainer;
    private createAllCellCtrls;
    private isCellEligibleToBeRemoved;
    getDomOrder(): boolean;
    private listenOnDomOrder;
    private setAnimateFlags;
    isEditing(): boolean;
    isFullWidth(): boolean;
    refreshFullWidth(): boolean;
    private addListeners;
    private addListenersForCellComps;
    private onRowNodeDataChanged;
    private postProcessCss;
    private onRowNodeHighlightChanged;
    private postProcessRowDragging;
    private updateExpandedCss;
    private onDisplayedColumnsChanged;
    private onVirtualColumnsChanged;
    getRowPosition(): RowPosition;
    private findFullWidthRowGui;
    onKeyboardNavigate(keyboardEvent: KeyboardEvent): void;
    onTabKeyDown(keyboardEvent: KeyboardEvent): void;
    getFullWidthElement(): HTMLElement | null;
    getRowYPosition(): number;
    onSuppressCellFocusChanged(suppressCellFocus: boolean): void;
    onFullWidthRowFocused(event?: CellFocusedEvent): void;
    recreateCell(cellCtrl: CellCtrl): void;
    private removeCellCtrl;
    onMouseEvent(eventName: string, mouseEvent: MouseEvent): void;
    createRowEvent<T extends AgEventType>(type: T, domEvent?: Event): RowEvent<T>;
    private createRowEventWithSource;
    private onRowDblClick;
    private getColumnForFullWidth;
    private onRowMouseDown;
    onRowClick(mouseEvent: MouseEvent): void;
    isRowSelectionBlocked(): boolean;
    setupDetailRowAutoHeight(eDetailGui: HTMLElement): void;
    private createFullWidthCompDetails;
    private refreshRowTooltip;
    private addFullWidthRowDragging;
    private onUiLevelChanged;
    private isFirstRowOnPage;
    private isLastRowOnPage;
    private refreshFirstAndLastRowStyles;
    stopEditing(cancel?: boolean): void;
    setInlineEditingCss(): void;
    setEditingRow(value: boolean): void;
    startRowEditing(key?: string | null, sourceRenderedCell?: CellCtrl | null, event?: KeyboardEvent | null): boolean;
    getAllCellCtrls(): CellCtrl[];
    private postProcessClassesFromGridOptions;
    private postProcessRowClassRules;
    private setStylesFromGridOptions;
    private getPinnedForContainer;
    private getInitialRowClasses;
    processStylesFromGridOptions(): RowStyle | undefined;
    private onRowSelected;
    announceDescription(): void;
    addHoverFunctionality(eGui: RowGui): void;
    private roundRowTopToBounds;
    protected getFrameworkOverrides(): IFrameworkOverrides;
    forEachGui(gui: RowGui | undefined, callback: (gui: RowGui) => void): void;
    private onRowHeightChanged;
    addEventListener<T extends RowCtrlEvent>(eventType: T, listener: IEventListener<T>): void;
    removeEventListener<T extends RowCtrlEvent>(eventType: T, listener: IEventListener<T>): void;
    destroyFirstPass(suppressAnimation?: boolean): void;
    destroySecondPass(): void;
    private setFocusedClasses;
    private onCellFocusChanged;
    private onPaginationChanged;
    private onTopChanged;
    private onPaginationPixelOffsetChanged;
    private applyPaginationOffset;
    setRowTop(pixels: number): void;
    getInitialRowTop(rowContainerType: RowContainerType): string | undefined;
    getInitialTransform(rowContainerType: RowContainerType): string | undefined;
    private getInitialRowTopShared;
    private setRowTopStyle;
    getRowNode(): RowNode;
    getCellCtrl(column: AgColumn): CellCtrl | null;
    private onRowIndexChanged;
    getRowIndex(): string | null;
    private updateRowIndexes;
    setStoppingRowEdit(stoppingRowEdit: boolean): void;
}
export {};
