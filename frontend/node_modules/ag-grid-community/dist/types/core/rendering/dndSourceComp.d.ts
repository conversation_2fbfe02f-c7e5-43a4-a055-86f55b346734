import type { AgColumn } from '../entities/agColumn';
import type { RowNode } from '../entities/rowNode';
import { Component } from '../widgets/component';
export declare class DndSourceComp extends Component {
    private readonly rowNode;
    private readonly column;
    private readonly eCell;
    constructor(rowNode: RowNode, column: AgColumn, eCell: HTMLElement);
    postConstruct(): void;
    private addDragSource;
    private onDragStart;
    private checkVisibility;
}
