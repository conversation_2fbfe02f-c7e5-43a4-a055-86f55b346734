import type { IOverlay, IOverlayComp, IOverlayParams } from './overlayComponent';
import { OverlayComponent } from './overlayComponent';
export interface INoRowsOverlayParams<TData = any, TContext = any> extends IOverlayParams<TData, TContext> {
}
export interface INoRowsOverlay<TData = any, TContext = any> extends IOverlay<TData, TContext, INoRowsOverlayParams> {
}
export interface INoRowsOverlayComp<TData = any, TContext = any> extends IOverlayComp<TData, TContext, INoRowsOverlayParams<TData, TContext>> {
}
export declare class NoRowsOverlayComponent extends OverlayComponent<any, any, INoRowsOverlayParams> implements INoRowsOverlayComp<any, any> {
    init(): void;
}
