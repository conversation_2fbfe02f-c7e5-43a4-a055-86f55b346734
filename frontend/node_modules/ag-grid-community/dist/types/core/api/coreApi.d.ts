import type { BeanCollection } from '../context/context';
import type { GridOptions } from '../entities/gridOptions';
import type { ManagedGridOptionKey, ManagedGridOptions } from '../propertyKeys';
export declare function getGridId(beans: BeanCollection): string;
export declare function destroy(beans: BeanCollection): void;
export declare function isDestroyed(beans: BeanCollection): boolean;
export declare function getGridOption<Key extends keyof GridOptions<TData>, TData = any>(beans: BeanCollection, key: Key): GridOptions<TData>[Key];
export declare function setGridOption<Key extends ManagedGridOptionKey, TData = any>(beans: BeanCollection, key: Key, value: GridOptions<TData>[Key]): void;
export declare function updateGridOptions<TDataUpdate = any>(beans: BeanCollection, options: ManagedGridOptions<TDataUpdate>): void;
