import { ModuleNames } from '../modules/moduleNames';
export declare const gridApiFunctionsMap: {
    getStatusPanel: ModuleNames;
    isSideBarVisible: ModuleNames;
    setSideBarVisible: ModuleNames;
    setSideBarPosition: ModuleNames;
    openToolPanel: ModuleNames;
    closeToolPanel: ModuleNames;
    getOpenedToolPanel: ModuleNames;
    refreshToolPanel: ModuleNames;
    isToolPanelShowing: ModuleNames;
    getToolPanelInstance: ModuleNames;
    getSideBar: ModuleNames;
    getServerSideSelectionState: ModuleNames;
    setServerSideSelectionState: ModuleNames;
    applyServerSideTransaction: ModuleNames;
    applyServerSideTransactionAsync: ModuleNames;
    applyServerSideRowData: ModuleNames;
    retryServerSideLoads: ModuleNames;
    flushServerSideAsyncTransactions: ModuleNames;
    refreshServerSide: ModuleNames;
    getServerSideGroupLevelState: ModuleNames;
    addAggFunc: ModuleNames;
    addAggFuncs: ModuleNames;
    clearAggFuncs: ModuleNames;
    setColumnAggFunc: ModuleNames;
    isPivotMode: ModuleNames;
    getPivotResultColumn: ModuleNames;
    setValueColumns: ModuleNames;
    getValueColumns: ModuleNames;
    removeValueColumn: ModuleNames;
    removeValueColumns: ModuleNames;
    addValueColumns: ModuleNames;
    setRowGroupColumns: ModuleNames;
    moveRowGroupColumn: ModuleNames;
    addValueColumn: ModuleNames;
    removeRowGroupColumn: ModuleNames;
    removeRowGroupColumns: ModuleNames;
    addRowGroupColumn: ModuleNames;
    addRowGroupColumns: ModuleNames;
    getRowGroupColumns: ModuleNames;
    setPivotColumns: ModuleNames;
    removePivotColumn: ModuleNames;
    removePivotColumns: ModuleNames;
    addPivotColumn: ModuleNames;
    addPivotColumns: ModuleNames;
    getPivotColumns: ModuleNames;
    setPivotResultColumns: ModuleNames;
    getPivotResultColumns: ModuleNames;
    getCellRanges: ModuleNames;
    addCellRange: ModuleNames;
    clearRangeSelection: ModuleNames;
    clearCellSelection: ModuleNames;
    showContextMenu: ModuleNames;
    showColumnChooser: ModuleNames;
    hideColumnChooser: ModuleNames;
    addDetailGridInfo: ModuleNames;
    removeDetailGridInfo: ModuleNames;
    getDetailGridInfo: ModuleNames;
    forEachDetailGridInfo: ModuleNames;
    getDataAsExcel: ModuleNames;
    exportDataAsExcel: ModuleNames;
    getSheetDataForExcel: ModuleNames;
    getMultipleSheetsAsExcel: ModuleNames;
    exportMultipleSheetsAsExcel: ModuleNames;
    copyToClipboard: ModuleNames;
    cutToClipboard: ModuleNames;
    copySelectedRowsToClipboard: ModuleNames;
    copySelectedRangeToClipboard: ModuleNames;
    copySelectedRangeDown: ModuleNames;
    pasteFromClipboard: ModuleNames;
    getChartModels: ModuleNames;
    getChartRef: ModuleNames;
    getChartImageDataURL: ModuleNames;
    downloadChart: ModuleNames;
    openChartToolPanel: ModuleNames;
    closeChartToolPanel: ModuleNames;
    createRangeChart: ModuleNames;
    createPivotChart: ModuleNames;
    createCrossFilterChart: ModuleNames;
    updateChart: ModuleNames;
    restoreChart: ModuleNames;
    getAdvancedFilterModel: ModuleNames;
    setAdvancedFilterModel: ModuleNames;
    showAdvancedFilterBuilder: ModuleNames;
    hideAdvancedFilterBuilder: ModuleNames;
    refreshInfiniteCache: ModuleNames;
    purgeInfiniteCache: ModuleNames;
    getInfiniteRowCount: ModuleNames;
    getDataAsCsv: ModuleNames;
    exportDataAsCsv: ModuleNames;
    onGroupExpandedOrCollapsed: ModuleNames;
    refreshClientSideRowModel: ModuleNames;
    forEachLeafNode: ModuleNames;
    forEachNodeAfterFilter: ModuleNames;
    forEachNodeAfterFilterAndSort: ModuleNames;
    resetRowHeights: ModuleNames;
    applyTransaction: ModuleNames;
    applyTransactionAsync: ModuleNames;
    flushAsyncTransactions: ModuleNames;
    getBestCostNodeSelection: ModuleNames;
    isRowDataEmpty: ModuleNames;
    flashCells: ModuleNames;
    tabToNextCell: ModuleNames;
    onFilterChanged: ModuleNames;
    onSortChanged: ModuleNames;
    addEventListener: ModuleNames;
    removeEventListener: ModuleNames;
    dispatchEvent: ModuleNames;
    getGridId: ModuleNames;
    destroy: ModuleNames;
    isDestroyed: ModuleNames;
    getGridOption: ModuleNames;
    setGridOption: ModuleNames;
    updateGridOptions: ModuleNames;
    getState: ModuleNames;
    setNodesSelected: ModuleNames;
    selectAll: ModuleNames;
    deselectAll: ModuleNames;
    selectAllFiltered: ModuleNames;
    deselectAllFiltered: ModuleNames;
    selectAllOnCurrentPage: ModuleNames;
    deselectAllOnCurrentPage: ModuleNames;
    getSelectedNodes: ModuleNames;
    getSelectedRows: ModuleNames;
    redrawRows: ModuleNames;
    setRowNodeExpanded: ModuleNames;
    getRowNode: ModuleNames;
    addRenderedRowListener: ModuleNames;
    getRenderedNodes: ModuleNames;
    forEachNode: ModuleNames;
    getFirstDisplayedRow: ModuleNames;
    getFirstDisplayedRowIndex: ModuleNames;
    getLastDisplayedRow: ModuleNames;
    getLastDisplayedRowIndex: ModuleNames;
    getDisplayedRowAtIndex: ModuleNames;
    getDisplayedRowCount: ModuleNames;
    getModel: ModuleNames;
    getVerticalPixelRange: ModuleNames;
    getHorizontalPixelRange: ModuleNames;
    ensureColumnVisible: ModuleNames;
    ensureIndexVisible: ModuleNames;
    ensureNodeVisible: ModuleNames;
    getFocusedCell: ModuleNames;
    clearFocusedCell: ModuleNames;
    setFocusedCell: ModuleNames;
    setFocusedHeader: ModuleNames;
    tabToPreviousCell: ModuleNames;
    addGlobalListener: ModuleNames;
    removeGlobalListener: ModuleNames;
    expireValueCache: ModuleNames;
    getValue: ModuleNames;
    getCellValue: ModuleNames;
    showColumnMenuAfterButtonClick: ModuleNames;
    showColumnMenuAfterMouseClick: ModuleNames;
    showColumnMenu: ModuleNames;
    hidePopupMenu: ModuleNames;
    showLoadingOverlay: ModuleNames;
    showNoRowsOverlay: ModuleNames;
    hideOverlay: ModuleNames;
    getPinnedTopRowCount: ModuleNames;
    getPinnedBottomRowCount: ModuleNames;
    getPinnedTopRow: ModuleNames;
    getPinnedBottomRow: ModuleNames;
    setGridAriaProperty: ModuleNames;
    refreshCells: ModuleNames;
    refreshHeader: ModuleNames;
    isAnimationFrameQueueEmpty: ModuleNames;
    flushAllAnimationFrames: ModuleNames;
    getSizesForCurrentTheme: ModuleNames;
    getCellRendererInstances: ModuleNames;
    addRowDropZone: ModuleNames;
    removeRowDropZone: ModuleNames;
    getRowDropZoneParams: ModuleNames;
    getColumnDef: ModuleNames;
    getColumnDefs: ModuleNames;
    sizeColumnsToFit: ModuleNames;
    setColumnGroupOpened: ModuleNames;
    getColumnGroup: ModuleNames;
    getProvidedColumnGroup: ModuleNames;
    getDisplayNameForColumn: ModuleNames;
    getDisplayNameForColumnGroup: ModuleNames;
    getColumn: ModuleNames;
    getColumns: ModuleNames;
    applyColumnState: ModuleNames;
    getColumnState: ModuleNames;
    resetColumnState: ModuleNames;
    getColumnGroupState: ModuleNames;
    setColumnGroupState: ModuleNames;
    resetColumnGroupState: ModuleNames;
    isPinning: ModuleNames;
    isPinningLeft: ModuleNames;
    isPinningRight: ModuleNames;
    getDisplayedColAfter: ModuleNames;
    getDisplayedColBefore: ModuleNames;
    setColumnVisible: ModuleNames;
    setColumnsVisible: ModuleNames;
    setColumnPinned: ModuleNames;
    setColumnsPinned: ModuleNames;
    getAllGridColumns: ModuleNames;
    getDisplayedLeftColumns: ModuleNames;
    getDisplayedCenterColumns: ModuleNames;
    getDisplayedRightColumns: ModuleNames;
    getAllDisplayedColumns: ModuleNames;
    getAllDisplayedVirtualColumns: ModuleNames;
    moveColumn: ModuleNames;
    moveColumnByIndex: ModuleNames;
    moveColumns: ModuleNames;
    setColumnWidth: ModuleNames;
    setColumnWidths: ModuleNames;
    getLeftDisplayedColumnGroups: ModuleNames;
    getCenterDisplayedColumnGroups: ModuleNames;
    getRightDisplayedColumnGroups: ModuleNames;
    getAllDisplayedColumnGroups: ModuleNames;
    autoSizeColumn: ModuleNames;
    autoSizeColumns: ModuleNames;
    autoSizeAllColumns: ModuleNames;
    undoCellEditing: ModuleNames;
    redoCellEditing: ModuleNames;
    getCellEditorInstances: ModuleNames;
    getEditingCells: ModuleNames;
    stopEditing: ModuleNames;
    startEditingCell: ModuleNames;
    getCurrentUndoSize: ModuleNames;
    getCurrentRedoSize: ModuleNames;
    isAnyFilterPresent: ModuleNames;
    isColumnFilterPresent: ModuleNames;
    getFilterInstance: ModuleNames;
    getColumnFilterInstance: ModuleNames;
    destroyFilter: ModuleNames;
    setFilterModel: ModuleNames;
    getFilterModel: ModuleNames;
    getColumnFilterModel: ModuleNames;
    setColumnFilterModel: ModuleNames;
    showColumnFilter: ModuleNames;
    isQuickFilterPresent: ModuleNames;
    getQuickFilter: ModuleNames;
    resetQuickFilter: ModuleNames;
    paginationIsLastPageFound: ModuleNames;
    paginationGetPageSize: ModuleNames;
    paginationGetCurrentPage: ModuleNames;
    paginationGetTotalPages: ModuleNames;
    paginationGetRowCount: ModuleNames;
    paginationGoToNextPage: ModuleNames;
    paginationGoToPreviousPage: ModuleNames;
    paginationGoToFirstPage: ModuleNames;
    paginationGoToLastPage: ModuleNames;
    paginationGoToPage: ModuleNames;
    expandAll: ModuleNames;
    collapseAll: ModuleNames;
    onRowHeightChanged: ModuleNames;
    setRowCount: ModuleNames;
    getCacheBlockState: ModuleNames;
    isLastRowIndexKnown: ModuleNames;
};
