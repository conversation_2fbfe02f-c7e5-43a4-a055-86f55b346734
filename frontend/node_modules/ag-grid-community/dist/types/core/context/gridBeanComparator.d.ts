import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, BeanN<PERSON> } from './context';
import type { GenericBean } from './genericBean';
export declare function gridBeanInitComparator(bean1: GenericBean<BeanName, BeanCollection>, bean2: GenericBean<BeanName, BeanCollection>): number;
export declare function gridBeanDestroyComparator(bean1: GenericBean<BeanName, BeanCollection>, bean2: GenericBean<BeanName, BeanCollection>): number;
