import type { AgInputFieldParams } from '../interfaces/agFieldParams';
import { AgAbstractInputField } from './agAbstractInputField';
import type { ComponentSelector } from './component';
export declare class AgInputTextArea extends AgAbstractInputField<HTMLTextAreaElement, string> {
    constructor(config?: AgInputFieldParams);
    setValue(value: string, silent?: boolean): this;
    setCols(cols: number): this;
    setRows(rows: number): this;
}
export declare const AgInputTextAreaSelector: ComponentSelector;
