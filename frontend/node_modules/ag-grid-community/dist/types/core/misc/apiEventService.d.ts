import type { NamedBean } from '../context/bean';
import { BeanStub } from '../context/beanStub';
import type { AgEventType } from '../eventTypes';
import type { AgEventListener, AgGlobalEventListener } from '../events';
export declare class ApiEventService extends BeanStub<AgEventType> implements NamedBean {
    beanName: "apiEventService";
    private syncEventListeners;
    private asyncEventListeners;
    private syncGlobalEventListeners;
    private globalEventListenerPairs;
    private frameworkEventWrappingService;
    postConstruct(): void;
    addEventListener<T extends AgEventType>(eventType: T, userListener: AgEventListener): void;
    removeEventListener<T extends AgEventType>(eventType: T, userListener: AgEventListener): void;
    addGlobalListener(userListener: AgGlobalEventListener): void;
    removeGlobalListener(userListener: AgGlobalEventListener): void;
    private destroyEventListeners;
    private destroyGlobalListeners;
    destroy(): void;
}
