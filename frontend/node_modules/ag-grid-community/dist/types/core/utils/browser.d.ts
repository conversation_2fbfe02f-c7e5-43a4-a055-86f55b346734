export declare function _isBrowserSafari(): boolean;
export declare function _getSafariVersion(): number;
/**
 * Returns true for Chrome and also for Edge (Chromium)
 */
export declare function _isBrowserChrome(): boolean;
export declare function _isBrowserFirefox(): boolean;
export declare function _isMacOsUserAgent(): boolean;
export declare function _isIOSUserAgent(): boolean;
export declare function _browserSupportsPreventScroll(): boolean;
export declare function _getTabIndex(el: HTMLElement | null): string | null;
export declare function _getMaxDivHeight(): number;
export declare function _getBodyWidth(): number;
export declare function _getBodyHeight(): number;
export declare function _getScrollbarWidth(): number | null;
export declare function _isInvisibleScrollbar(): boolean;
