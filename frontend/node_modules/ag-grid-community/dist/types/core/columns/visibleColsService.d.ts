import type { NamedBean } from '../context/bean';
import { BeanStub } from '../context/beanStub';
import type { BeanCollection } from '../context/context';
import type { AgColumn } from '../entities/agColumn';
import { AgColumnGroup } from '../entities/agColumnGroup';
import type { RowNode } from '../entities/rowNode';
import type { ColumnEventType } from '../events';
import type { ColumnPinnedType } from '../interfaces/iColumn';
import { GroupInstanceIdCreator } from './groupInstanceIdCreator';
export declare class VisibleColsService extends BeanStub implements NamedBean {
    beanName: "visibleColsService";
    private columnModel;
    private columnSizeService;
    private columnViewportService;
    private eventDispatcher;
    wireBeans(beans: BeanCollection): void;
    private treeLeft;
    private treeRight;
    private treeCenter;
    private colsAndGroupsMap;
    private columnsLeft;
    private columnsRight;
    private columnsCenter;
    private columns;
    private autoHeightCols;
    private bodyWidth;
    private leftWidth;
    private rightWidth;
    private bodyWidthDirty;
    private ariaOrderColumns;
    refresh(source: ColumnEventType, skipTreeBuild?: boolean): void;
    updateBodyWidths(): void;
    setLeftValues(source: ColumnEventType): void;
    private setFirstRightAndLastLeftPinned;
    private buildTrees;
    clear(): void;
    private joinColsAriaOrder;
    getAriaColIndex(colOrGroup: AgColumn | AgColumnGroup): number;
    getAllAutoHeightCols(): AgColumn[];
    private setLeftValuesOfGroups;
    private setLeftValuesOfCols;
    private joinCols;
    getColsCenter(): AgColumn[];
    getAllTrees(): (AgColumn | AgColumnGroup)[] | null;
    getTreeLeft(): (AgColumn | AgColumnGroup)[];
    getTreeRight(): (AgColumn | AgColumnGroup)[];
    getTreeCenter(): (AgColumn | AgColumnGroup)[];
    getAllCols(): AgColumn[];
    isColDisplayed(column: AgColumn): boolean;
    getLeftColsForRow(rowNode: RowNode): AgColumn[];
    getRightColsForRow(rowNode: RowNode): AgColumn[];
    getColsForRow(rowNode: RowNode, displayedColumns: AgColumn[], filterCallback?: (column: AgColumn) => boolean, emptySpaceBeforeColumn?: (column: AgColumn) => boolean): AgColumn[];
    getBodyContainerWidth(): number;
    getContainerWidth(pinned: ColumnPinnedType): number;
    getCenterCols(): AgColumn[];
    getLeftCols(): AgColumn[];
    getRightCols(): AgColumn[];
    getColBefore(col: AgColumn): AgColumn | null;
    getGroupAtDirection(columnGroup: AgColumnGroup, direction: 'After' | 'Before'): AgColumnGroup | null;
    getColGroupAtLevel(column: AgColumn, level: number): AgColumnGroup | null;
    isPinningLeft(): boolean;
    isPinningRight(): boolean;
    private updateColsAndGroupsMap;
    isVisible(item: AgColumn | AgColumnGroup): boolean;
    private updateOpenClosedVisibilityInColumnGroups;
    getFirstColumn(): AgColumn | null;
    getColumnGroup(colId: string | AgColumnGroup, partId?: number): AgColumnGroup | null;
    getColAfter(col: AgColumn): AgColumn | null;
    isBodyWidthDirty(): boolean;
    setBodyWidthDirty(): void;
    getColsLeftWidth(): number;
    getDisplayedColumnsRightWidth(): number;
    isColAtEdge(col: AgColumn | AgColumnGroup, edge: 'first' | 'last'): boolean;
    createGroups(params: {
        columns: AgColumn[];
        idCreator: GroupInstanceIdCreator;
        pinned: ColumnPinnedType;
        oldDisplayedGroups?: (AgColumn | AgColumnGroup)[];
        isStandaloneStructure?: boolean;
    }): (AgColumn | AgColumnGroup)[];
    private createColGroup;
    private mapOldGroupsById;
    private setupParentsIntoCols;
}
