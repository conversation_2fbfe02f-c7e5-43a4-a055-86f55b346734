export declare const colorSchemeLight: import("../../../Part").Part<import("../../core/core-css").CoreParams>;
export declare const colorSchemeLightWarm: import("../../../Part").Part<import("../../core/core-css").CoreParams>;
export declare const colorSchemeLightCold: import("../../../Part").Part<import("../../core/core-css").CoreParams>;
export declare const colorSchemeDark: import("../../../Part").Part<import("../../core/core-css").CoreParams>;
export declare const colorSchemeDarkWarm: import("../../../Part").Part<import("../../core/core-css").CoreParams>;
export declare const colorSchemeDarkBlue: import("../../../Part").Part<import("../../core/core-css").CoreParams>;
