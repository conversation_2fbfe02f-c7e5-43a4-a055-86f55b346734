export declare const inputStyleBaseCSS = ":where(input[class^=ag-][type=number]:not(.ag-number-field-input-stepper)){-moz-appearance:textfield;&::-webkit-inner-spin-button,&::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}}:where(input[class^=ag-]:not([type]),input[class^=ag-][type=text],input[class^=ag-][type=number],input[class^=ag-][type=tel],input[class^=ag-][type=date],input[class^=ag-][type=datetime-local],textarea[class^=ag-]){background-color:var(--ag-input-background-color);border:var(--ag-input-border);border-radius:var(--ag-input-border-radius);color:var(--ag-input-text-color);cursor:pointer;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;min-height:var(--ag-input-height);padding:0;&:where(:disabled){background-color:var(--ag-input-disabled-background-color);border:var(--ag-input-disabled-border);color:var(--ag-input-disabled-text-color)}&:where(:focus){background-color:var(--ag-input-focus-background-color);border:var(--ag-input-focus-border);box-shadow:var(--ag-input-focus-shadow);color:var(--ag-input-focus-text-color)}&:where(:invalid){background-color:var(--ag-input-invalid-background-color);border:var(--ag-input-invalid-border);color:var(--ag-input-invalid-text-color)}&:where(.invalid){background-color:var(--ag-input-invalid-background-color);border:var(--ag-input-invalid-border);color:var(--ag-input-invalid-text-color)}}&.ag-ltr :where(input[class^=ag-]:not([type]),input[class^=ag-][type=text],input[class^=ag-][type=number],input[class^=ag-][type=tel],input[class^=ag-][type=date],input[class^=ag-][type=datetime-local],textarea[class^=ag-]){padding-left:var(--ag-input-padding-start)}&.ag-rtl :where(input[class^=ag-]:not([type]),input[class^=ag-][type=text],input[class^=ag-][type=number],input[class^=ag-][type=tel],input[class^=ag-][type=date],input[class^=ag-][type=datetime-local],textarea[class^=ag-]){padding-right:var(--ag-input-padding-start)}";
