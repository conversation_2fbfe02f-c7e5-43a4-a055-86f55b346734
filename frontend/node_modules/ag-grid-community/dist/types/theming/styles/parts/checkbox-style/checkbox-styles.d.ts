import type { ColorValue, ImageValue, LengthValue } from '../../../theme-types';
export type CheckboxStyleParams = {
    /**
     * Border radius for checkboxes
     */
    checkboxBorderRadius: LengthValue;
    /**
     * Border width for checkboxes
     */
    checkboxBorderWidth: LengthValue;
    /**
     * Background color of a checked checkbox
     */
    checkboxCheckedBackgroundColor: ColorValue;
    /**
     * Border color of a checked checkbox
     */
    checkboxCheckedBorderColor: ColorValue;
    /**
     * The color of the check mark on checked checkboxes.
     */
    checkboxCheckedShapeColor: ColorValue;
    /**
     * An image defining the shape of the check mark on checked checkboxes.
     */
    checkboxCheckedShapeImage: ImageValue;
    /**
     * Background color of an indeterminate checkbox
     */
    checkboxIndeterminateBackgroundColor: ColorValue;
    /**
     * Border color of an indeterminate checkbox
     */
    checkboxIndeterminateBorderColor: ColorValue;
    /**
     * The color of the dash mark on indeterminate checkboxes
     */
    checkboxIndeterminateShapeColor: ColorValue;
    /**
     * An image defining the shape of the dash mark on indeterminate checkboxes
     */
    checkboxIndeterminateShapeImage: ImageValue;
    /**
     * Background color of an unchecked checkbox
     */
    checkboxUncheckedBackgroundColor: ColorValue;
    /**
     * Border color of an unchecked checkbox
     */
    checkboxUncheckedBorderColor: ColorValue;
    /**
     * An image defining the shape of the mark on checked radio buttons
     */
    radioCheckedShapeImage: ImageValue;
};
export declare const checkboxStyleDefault: import("../../../Part").Part<import("../../core/core-css").CoreParams & CheckboxStyleParams>;
