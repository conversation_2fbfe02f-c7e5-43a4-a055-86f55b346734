export declare const iconSetQuartz: (args?: {
    strokeWidth?: number;
}) => import("../../../../Part").Part<import("../../../core/core-css").CoreParams>;
export declare const iconSetQuartzLight: import("../../../../Part").Part<import("../../../core/core-css").CoreParams>;
export declare const iconSetQuartzRegular: import("../../../../Part").Part<import("../../../core/core-css").CoreParams>;
export declare const iconSetQuartzBold: import("../../../../Part").Part<import("../../../core/core-css").CoreParams>;
