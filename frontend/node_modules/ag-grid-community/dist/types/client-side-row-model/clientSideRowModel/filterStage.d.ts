import type { <PERSON><PERSON>oll<PERSON><PERSON>, IRowNodeStage, NamedBean, StageExecuteParams } from 'ag-grid-community';
import { BeanStub } from 'ag-grid-community';
export declare class FilterStage extends BeanStub implements IRowNodeStage, NamedBean {
    beanName: "filterStage";
    private filterManager?;
    wireBeans(beans: BeanCollection): void;
    execute(params: StageExecuteParams): void;
    private filter;
    private filterNodes;
    private doingTreeDataFiltering;
}
