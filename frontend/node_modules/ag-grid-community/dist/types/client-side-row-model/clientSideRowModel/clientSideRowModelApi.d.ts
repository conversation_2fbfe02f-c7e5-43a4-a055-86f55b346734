import type { BeanCollection, ClientSideRowModelStep, IRowNode, RowDataTransaction, RowNodeTransaction } from 'ag-grid-community';
export declare function onGroupExpandedOrCollapsed(beans: BeanCollection): void;
export declare function refreshClientSideRowModel(beans: BeanCollection, step?: ClientSideRowModelStep): void;
export declare function isRowDataEmpty(beans: BeanCollection): boolean;
export declare function forEachLeafNode<TData = any>(beans: BeanCollection, callback: (rowNode: IRowNode<TData>) => void): void;
export declare function forEachNodeAfterFilter<TData = any>(beans: BeanCollection, callback: (rowNode: IRowNode<TData>, index: number) => void): void;
export declare function forEachNodeAfterFilterAndSort<TData = any>(beans: BeanCollection, callback: (rowNode: IRowNode<TData>, index: number) => void): void;
export declare function resetRowHeights(beans: BeanCollection): void;
export declare function applyTransaction<TData = any>(beans: BeanCollection, rowDataTransaction: RowDataTransaction<TData>): RowNodeTransaction<TData> | null | undefined;
export declare function applyTransactionAsync<TData = any>(beans: BeanCollection, rowDataTransaction: RowDataTransaction<TData>, callback?: (res: RowNodeTransaction<TData>) => void): void;
export declare function flushAsyncTransactions(beans: BeanCollection): void;
export declare function getBestCostNodeSelection<TData = any>(beans: BeanCollection): IRowNode<TData>[] | undefined;
