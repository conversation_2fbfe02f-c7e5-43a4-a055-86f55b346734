import type { <PERSON>Collec<PERSON>, IRowNodeStage, NamedBean, StageExecuteParams } from 'ag-grid-community';
import { BeanStub, RowNode } from 'ag-grid-community';
export declare class FlattenStage extends BeanStub implements IRowNodeStage, NamedBean {
    beanName: "flattenStage";
    private beans;
    private columnModel;
    wireBeans(beans: BeanCollection): void;
    execute(params: StageExecuteParams): RowNode[];
    private getFlattenDetails;
    private recursivelyAddToRowsToDisplay;
    private addRowNodeToRowsToDisplay;
    private createDetailNode;
}
