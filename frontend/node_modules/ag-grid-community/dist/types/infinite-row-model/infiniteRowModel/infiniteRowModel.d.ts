import type { <PERSON><PERSON>oll<PERSON><PERSON>, IDatasource, IInfiniteRowModel, NamedBean, RowBounds, RowModelType, RowNode } from 'ag-grid-community';
import { BeanStub } from 'ag-grid-community';
export declare class InfiniteRowModel extends BeanStub implements NamedBean, IInfiniteRowModel {
    beanName: "rowModel";
    private filterManager?;
    private sortController;
    private selectionService;
    private rowRenderer;
    private rowNodeBlockLoader;
    wireBeans(beans: BeanCollection): void;
    private infiniteCache;
    private datasource;
    private rowHeight;
    private cacheParams;
    getRowBounds(index: number): RowBounds;
    ensureRowHeightsValid(): boolean;
    postConstruct(): void;
    private verifyProps;
    start(): void;
    destroy(): void;
    private destroyDatasource;
    private addEventListeners;
    private onFilterChanged;
    private onSortChanged;
    private onColumnEverything;
    private isSortModelDifferent;
    getType(): RowModelType;
    setDatasource(datasource: IDatasource | undefined): void;
    isEmpty(): boolean;
    isRowsToRender(): boolean;
    getNodesInRangeForSelection(firstInRange: RowNode, lastInRange: RowNode): RowNode[];
    private reset;
    private dispatchModelUpdatedEvent;
    private resetCache;
    private updateRowHeights;
    private destroyCache;
    private onCacheUpdated;
    getRow(rowIndex: number): RowNode | undefined;
    getRowNode(id: string): RowNode | undefined;
    forEachNode(callback: (rowNode: RowNode, index: number) => void): void;
    getTopLevelRowCount(): number;
    getTopLevelRowDisplayedIndex(topLevelIndex: number): number;
    getRowIndexAtPixel(pixel: number): number;
    getRowCount(): number;
    isRowPresent(rowNode: RowNode): boolean;
    refreshCache(): void;
    purgeCache(): void;
    isLastRowIndexKnown(): boolean;
    setRowCount(rowCount: number, lastRowIndexKnown?: boolean): void;
}
