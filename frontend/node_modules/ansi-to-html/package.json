{"name": "ansi-to-html", "version": "0.7.2", "description": "Convert ansi escaped text streams to html.", "main": "lib/ansi_to_html.js", "engines": {"node": ">=8.0.0"}, "scripts": {"build": "babel src --source-maps --copy-files --out-dir lib", "build:watch": "babel src --source-maps --out-dir lib --watch", "lint": "eslint .", "test": "nyc mocha --reporter tap", "test:watch": "mocha --reporter tap --watch ./test ./"}, "nyc": {"reporter": ["text", "html"]}, "repository": {"type": "git", "url": "https://github.com/rburns/ansi-to-html.git"}, "homepage": "https://github.com/rburns/ansi-to-html", "bugs": {"url": "https://github.com/rburns/ansi-to-html/issues"}, "keywords": ["ansi", "html"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://rburns.paiges.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "micha<PERSON>@riesd.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>-<PERSON><PERSON>m", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "NeeEoo", "url": "https://github.com/NeeEoo"}, {"name": "<PERSON>", "url": "https://github.com/brettz9"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hasparus"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lior-cher<PERSON>sky"}, {"name": "Maple Miao", "url": "https://github.com/mapleeit"}, {"name": "remolueoend", "url": "https://github.com/remolueoend"}, {"name": "<PERSON>", "url": "https://github.com/vboulaye"}], "license": "MIT", "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.5", "@babel/preset-env": "^7.15.6", "chai": "^4.3.0", "eslint": "^7.0.0", "mocha": "^9.0.0", "ansi-regex": ">=5.0.1", "nyc": "^15.1.0"}, "dependencies": {"entities": "^2.2.0"}, "bin": {"ansi-to-html": "./bin/ansi-to-html"}, "babel": {"presets": ["@babel/env"]}}