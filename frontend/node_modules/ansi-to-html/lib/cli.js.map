{"version": 3, "sources": ["../src/cli.js"], "names": ["help", "args", "stream", "file", "skip", "argv", "process", "slice", "i", "len", "length", "newline", "escapeXML", "fg", "bg", "console", "log", "require", "__dirname", "version", "exit", "convert", "htmlStream", "on", "chunk", "stdout", "write", "toHtml", "createReadStream", "encoding", "stdin", "setEncoding"], "mappings": "AAAA;AACA;;AACA,IAAMA,IAAI,ibAAV;AAeA,IAAMC,IAAI,GAAG;AACTC,EAAAA,MAAM,EAAE;AADC,CAAb;AAGA,IAAIC,IAAI,GAAG,IAAX;AAAA,IACIC,IAAI,GAAG,KADX;AAGA,IAAMC,IAAI,GAAGC,OAAO,CAACD,IAAR,CAAaE,KAAb,CAAmB,CAAnB,CAAb;;AACA,KACI,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGJ,IAAI,CAACK,MAD1B,EAEIF,CAAC,GAAGC,GAFR,EAGI,EAAED,CAHN,EAIE;AACE,MAAIJ,IAAJ,EAAU;AACNA,IAAAA,IAAI,GAAG,KAAP;AACA;AACH;;AACD,UAAQC,IAAI,CAACG,CAAD,CAAZ;AACI,SAAK,IAAL;AACA,SAAK,WAAL;AACIP,MAAAA,IAAI,CAACU,OAAL,GAAe,IAAf;AACA;;AACJ,SAAK,IAAL;AACA,SAAK,aAAL;AACIV,MAAAA,IAAI,CAACW,SAAL,GAAiB,IAAjB;AACA;;AACJ,SAAK,IAAL;AACA,SAAK,MAAL;AACIX,MAAAA,IAAI,CAACY,EAAL,GAAUR,IAAI,CAACG,CAAC,GAAG,CAAL,CAAd;AACAJ,MAAAA,IAAI,GAAG,IAAP;AACA;;AACJ,SAAK,IAAL;AACA,SAAK,MAAL;AACIH,MAAAA,IAAI,CAACa,EAAL,GAAUT,IAAI,CAACG,CAAC,GAAG,CAAL,CAAd;AACAJ,MAAAA,IAAI,GAAG,IAAP;AACA;;AACJ,SAAK,IAAL;AACA,SAAK,WAAL;AACIW,MAAAA,OAAO,CAACC,GAAR,CAAYC,OAAO,CAACC,SAAS,GAAG,kBAAb,CAAP,CAAwCC,OAApD;AACAb,MAAAA,OAAO,CAACc,IAAR,CAAa,CAAb,EAFJ,CAGI;;AACA;;AACJ,SAAK,IAAL;AACA,SAAK,QAAL;AACIL,MAAAA,OAAO,CAACC,GAAR,CAAYhB,IAAZ;AACAM,MAAAA,OAAO,CAACc,IAAR,CAAa,CAAb,EAFJ,CAGI;;AACA;;AACJ;AACIjB,MAAAA,IAAI,GAAGE,IAAI,CAACG,CAAD,CAAX;AAhCR;AAkCH;;AAED,IAAMa,OAAO,GAAG,KAAKJ,OAAO,CAAC,mBAAD,CAAZ,EAAmChB,IAAnC,CAAhB;;AAEA,IAAMqB,UAAU,GAAG,SAAbA,UAAa,CAAUpB,MAAV,EAAkB;AACjC,SAAOA,MAAM,CAACqB,EAAP,CAAU,MAAV,EAAkB,UAAUC,KAAV,EAAiB;AACtC,WAAOlB,OAAO,CAACmB,MAAR,CAAeC,KAAf,CAAqBL,OAAO,CAACM,MAAR,CAAeH,KAAf,CAArB,CAAP;AACH,GAFM,CAAP;AAGH,CAJD;;AAMA,IAAIrB,IAAJ,EAAU;AACN,MAAMD,MAAM,GAAGe,OAAO,CAAC,IAAD,CAAP,CAAcW,gBAAd,CAA+BzB,IAA/B,EAAqC;AAAC0B,IAAAA,QAAQ,EAAE;AAAX,GAArC,CAAf;;AACAP,EAAAA,UAAU,CAACpB,MAAD,CAAV;AACH,CAHD,MAGO;AACHI,EAAAA,OAAO,CAACwB,KAAR,CAAcC,WAAd,CAA0B,MAA1B;AACAT,EAAAA,UAAU,CAAChB,OAAO,CAACwB,KAAT,CAAV;AACH", "sourcesContent": ["'use strict';\n/* eslint no-console:0 */\nconst help = `\nusage: ansi-to-html [options] [file]\n\nfile:  The file to display or stdin\n\noptions:\n\n    -f, --fg         The background color used for resets (#000)\n    -b, --bg         The foreground color used for resets (#FFF)\n    -n, --newline    Convert newline characters to <br/>  (false)\n    -x, --escapeXML  Generate XML entities                (false)\n    -v, --version    Print version\n    -h, --help       Print help\n`;\n\nconst args = {\n    stream: true\n};\nlet file = null,\n    skip = false;\n\nconst argv = process.argv.slice(2);\nfor (\n    let i = 0, len = argv.length;\n    i < len;\n    ++i\n) {\n    if (skip) {\n        skip = false;\n        continue;\n    }\n    switch (argv[i]) {\n        case '-n':\n        case '--newline':\n            args.newline = true;\n            break;\n        case '-x':\n        case '--escapeXML':\n            args.escapeXML = true;\n            break;\n        case '-f':\n        case '--fg':\n            args.fg = argv[i + 1];\n            skip = true;\n            break;\n        case '-b':\n        case '--bg':\n            args.bg = argv[i + 1];\n            skip = true;\n            break;\n        case '-v':\n        case '--version':\n            console.log(require(__dirname + '/../package.json').version);\n            process.exit(0);\n            // istanbul ignore next\n            break;\n        case '-h':\n        case '--help':\n            console.log(help);\n            process.exit(0);\n            // istanbul ignore next\n            break;\n        default:\n            file = argv[i];\n    }\n}\n\nconst convert = new (require('./ansi_to_html.js'))(args);\n\nconst htmlStream = function (stream) {\n    return stream.on('data', function (chunk) {\n        return process.stdout.write(convert.toHtml(chunk));\n    });\n};\n\nif (file) {\n    const stream = require('fs').createReadStream(file, {encoding: 'utf8'});\n    htmlStream(stream);\n} else {\n    process.stdin.setEncoding('utf8');\n    htmlStream(process.stdin);\n}\n"], "file": "cli.js"}