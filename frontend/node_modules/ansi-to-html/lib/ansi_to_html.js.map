{"version": 3, "sources": ["../src/ansi_to_html.js"], "names": ["entities", "require", "defaults", "fg", "bg", "newline", "escapeXML", "stream", "colors", "getDefaultColors", "range", "for<PERSON>ach", "red", "green", "blue", "setStyleColor", "gray", "c", "l", "toHexString", "r", "g", "b", "toColorHexString", "num", "str", "toString", "length", "ref", "results", "push", "join", "generateOutput", "stack", "token", "data", "options", "result", "pushText", "handleDisplay", "pushForegroundColor", "pushBackgroundColor", "handleRgb", "substring", "slice", "operation", "substr", "color", "split", "rgb", "map", "value", "Number", "pushStyle", "code", "parseInt", "codeMap", "resetStyles", "pushTag", "closeTag", "stackClone", "reverse", "tag", "low", "high", "j", "notCategory", "category", "e", "categoryForCode", "text", "encodeXML", "style", "last", "pop", "tokenize", "callback", "ansiMatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove", "removeXterm256Foreground", "m", "g1", "removeXterm256Background", "an<PERSON><PERSON><PERSON>", "trim", "trimRight", "realText", "tokens", "pattern", "sub", "process", "handler", "i", "replace", "results1", "outer", "o", "len", "updateStickyStack", "stickyStack", "filter", "Filter", "Object", "assign", "input", "buf", "element", "output", "module", "exports"], "mappings": "AAAA;;;;;;;;;;;;;;AACA,IAAMA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAxB;;AACA,IAAMC,QAAQ,GAAG;AACbC,EAAAA,EAAE,EAAE,MADS;AAEbC,EAAAA,EAAE,EAAE,MAFS;AAGbC,EAAAA,OAAO,EAAE,KAHI;AAIbC,EAAAA,SAAS,EAAE,KAJE;AAKbC,EAAAA,MAAM,EAAE,KALK;AAMbC,EAAAA,MAAM,EAAEC,gBAAgB;AANX,CAAjB;;AASA,SAASA,gBAAT,GAA4B;AACxB,MAAMD,MAAM,GAAG;AACX,OAAG,MADQ;AAEX,OAAG,MAFQ;AAGX,OAAG,MAHQ;AAIX,OAAG,MAJQ;AAKX,OAAG,MALQ;AAMX,OAAG,MANQ;AAOX,OAAG,MAPQ;AAQX,OAAG,MARQ;AASX,OAAG,MATQ;AAUX,OAAG,MAVQ;AAWX,QAAI,MAXO;AAYX,QAAI,MAZO;AAaX,QAAI,MAbO;AAcX,QAAI,MAdO;AAeX,QAAI,MAfO;AAgBX,QAAI;AAhBO,GAAf;AAmBAE,EAAAA,KAAK,CAAC,CAAD,EAAI,CAAJ,CAAL,CAAYC,OAAZ,CAAoB,UAAAC,GAAG,EAAI;AACvBF,IAAAA,KAAK,CAAC,CAAD,EAAI,CAAJ,CAAL,CAAYC,OAAZ,CAAoB,UAAAE,KAAK,EAAI;AACzBH,MAAAA,KAAK,CAAC,CAAD,EAAI,CAAJ,CAAL,CAAYC,OAAZ,CAAoB,UAAAG,IAAI;AAAA,eAAIC,aAAa,CAACH,GAAD,EAAMC,KAAN,EAAaC,IAAb,EAAmBN,MAAnB,CAAjB;AAAA,OAAxB;AACH,KAFD;AAGH,GAJD;AAMAE,EAAAA,KAAK,CAAC,CAAD,EAAI,EAAJ,CAAL,CAAaC,OAAb,CAAqB,UAAUK,IAAV,EAAgB;AACjC,QAAMC,CAAC,GAAGD,IAAI,GAAG,GAAjB;AACA,QAAME,CAAC,GAAGC,WAAW,CAACH,IAAI,GAAG,EAAP,GAAY,CAAb,CAArB;AAEAR,IAAAA,MAAM,CAACS,CAAD,CAAN,GAAY,MAAMC,CAAN,GAAUA,CAAV,GAAcA,CAA1B;AACH,GALD;AAOA,SAAOV,MAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,aAAT,CAAuBH,GAAvB,EAA4BC,KAA5B,EAAmCC,IAAnC,EAAyCN,MAAzC,EAAiD;AAC7C,MAAMS,CAAC,GAAG,KAAML,GAAG,GAAG,EAAZ,GAAmBC,KAAK,GAAG,CAA3B,GAAgCC,IAA1C;AACA,MAAMM,CAAC,GAAGR,GAAG,GAAG,CAAN,GAAUA,GAAG,GAAG,EAAN,GAAW,EAArB,GAA0B,CAApC;AACA,MAAMS,CAAC,GAAGR,KAAK,GAAG,CAAR,GAAYA,KAAK,GAAG,EAAR,GAAa,EAAzB,GAA8B,CAAxC;AACA,MAAMS,CAAC,GAAGR,IAAI,GAAG,CAAP,GAAWA,IAAI,GAAG,EAAP,GAAY,EAAvB,GAA4B,CAAtC;AAEAN,EAAAA,MAAM,CAACS,CAAD,CAAN,GAAYM,gBAAgB,CAAC,CAACH,CAAD,EAAIC,CAAJ,EAAOC,CAAP,CAAD,CAA5B;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASH,WAAT,CAAqBK,GAArB,EAA0B;AACtB,MAAIC,GAAG,GAAGD,GAAG,CAACE,QAAJ,CAAa,EAAb,CAAV;;AAEA,SAAOD,GAAG,CAACE,MAAJ,GAAa,CAApB,EAAuB;AACnBF,IAAAA,GAAG,GAAG,MAAMA,GAAZ;AACH;;AAED,SAAOA,GAAP;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASF,gBAAT,CAA0BK,GAA1B,EAA+B;AAC3B,MAAMC,OAAO,GAAG,EAAhB;;AAD2B,6CAGXD,GAHW;AAAA;;AAAA;AAG3B,wDAAqB;AAAA,UAAVR,CAAU;AACjBS,MAAAA,OAAO,CAACC,IAAR,CAAaX,WAAW,CAACC,CAAD,CAAxB;AACH;AAL0B;AAAA;AAAA;AAAA;AAAA;;AAO3B,SAAO,MAAMS,OAAO,CAACE,IAAR,CAAa,EAAb,CAAb;AACH;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,cAAT,CAAwBC,KAAxB,EAA+BC,KAA/B,EAAsCC,IAAtC,EAA4CC,OAA5C,EAAqD;AACjD,MAAIC,MAAJ;;AAEA,MAAIH,KAAK,KAAK,MAAd,EAAsB;AAClBG,IAAAA,MAAM,GAAGC,QAAQ,CAACH,IAAD,EAAOC,OAAP,CAAjB;AACH,GAFD,MAEO,IAAIF,KAAK,KAAK,SAAd,EAAyB;AAC5BG,IAAAA,MAAM,GAAGE,aAAa,CAACN,KAAD,EAAQE,IAAR,EAAcC,OAAd,CAAtB;AACH,GAFM,MAEA,IAAIF,KAAK,KAAK,oBAAd,EAAoC;AACvCG,IAAAA,MAAM,GAAGG,mBAAmB,CAACP,KAAD,EAAQG,OAAO,CAAC5B,MAAR,CAAe2B,IAAf,CAAR,CAA5B;AACH,GAFM,MAEA,IAAID,KAAK,KAAK,oBAAd,EAAoC;AACvCG,IAAAA,MAAM,GAAGI,mBAAmB,CAACR,KAAD,EAAQG,OAAO,CAAC5B,MAAR,CAAe2B,IAAf,CAAR,CAA5B;AACH,GAFM,MAEA,IAAID,KAAK,KAAK,KAAd,EAAqB;AACxBG,IAAAA,MAAM,GAAGK,SAAS,CAACT,KAAD,EAAQE,IAAR,CAAlB;AACH;;AAED,SAAOE,MAAP;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASK,SAAT,CAAmBT,KAAnB,EAA0BE,IAA1B,EAAgC;AAC5BA,EAAAA,IAAI,GAAGA,IAAI,CAACQ,SAAL,CAAe,CAAf,EAAkBC,KAAlB,CAAwB,CAAxB,EAA2B,CAAC,CAA5B,CAAP;AACA,MAAMC,SAAS,GAAG,CAACV,IAAI,CAACW,MAAL,CAAY,CAAZ,EAAe,CAAf,CAAnB;AAEA,MAAMC,KAAK,GAAGZ,IAAI,CAACQ,SAAL,CAAe,CAAf,EAAkBK,KAAlB,CAAwB,GAAxB,CAAd;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,GAAN,CAAU,UAAUC,KAAV,EAAiB;AACnC,WAAO,CAAC,MAAMC,MAAM,CAACD,KAAD,CAAN,CAAczB,QAAd,CAAuB,EAAvB,CAAP,EAAmCoB,MAAnC,CAA0C,CAAC,CAA3C,CAAP;AACH,GAFW,EAETf,IAFS,CAEJ,EAFI,CAAZ;AAIA,SAAOsB,SAAS,CAACpB,KAAD,EAAQ,CAACY,SAAS,KAAK,EAAd,GAAmB,SAAnB,GAA+B,oBAAhC,IAAwDI,GAAhE,CAAhB;AACH;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASV,aAAT,CAAuBN,KAAvB,EAA8BqB,IAA9B,EAAoClB,OAApC,EAA6C;AACzCkB,EAAAA,IAAI,GAAGC,QAAQ,CAACD,IAAD,EAAO,EAAP,CAAf;AAEA,MAAME,OAAO,GAAG;AACZ,UAAM;AAAA,aAAM,OAAN;AAAA,KADM;AAEZ,OAAG;AAAA,aAAMvB,KAAK,CAACN,MAAN,IAAgB8B,WAAW,CAACxB,KAAD,CAAjC;AAAA,KAFS;AAGZ,OAAG;AAAA,aAAMyB,OAAO,CAACzB,KAAD,EAAQ,GAAR,CAAb;AAAA,KAHS;AAIZ,OAAG;AAAA,aAAMyB,OAAO,CAACzB,KAAD,EAAQ,GAAR,CAAb;AAAA,KAJS;AAKZ,OAAG;AAAA,aAAMyB,OAAO,CAACzB,KAAD,EAAQ,GAAR,CAAb;AAAA,KALS;AAMZ,OAAG;AAAA,aAAMoB,SAAS,CAACpB,KAAD,EAAQ,cAAR,CAAf;AAAA,KANS;AAOZ,OAAG;AAAA,aAAMyB,OAAO,CAACzB,KAAD,EAAQ,QAAR,CAAb;AAAA,KAPS;AAQZ,QAAI;AAAA,aAAMoB,SAAS,CAACpB,KAAD,EAAQ,2DAAR,CAAf;AAAA,KARQ;AASZ,QAAI;AAAA,aAAM0B,QAAQ,CAAC1B,KAAD,EAAQ,GAAR,CAAd;AAAA,KATQ;AAUZ,QAAI;AAAA,aAAM0B,QAAQ,CAAC1B,KAAD,EAAQ,GAAR,CAAd;AAAA,KAVQ;AAWZ,QAAI;AAAA,aAAMO,mBAAmB,CAACP,KAAD,EAAQG,OAAO,CAACjC,EAAhB,CAAzB;AAAA,KAXQ;AAYZ,QAAI;AAAA,aAAMsC,mBAAmB,CAACR,KAAD,EAAQG,OAAO,CAAChC,EAAhB,CAAzB;AAAA,KAZQ;AAaZ,QAAI;AAAA,aAAMiD,SAAS,CAACpB,KAAD,EAAQ,0BAAR,CAAf;AAAA;AAbQ,GAAhB;AAgBA,MAAII,MAAJ;;AACA,MAAImB,OAAO,CAACF,IAAD,CAAX,EAAmB;AACfjB,IAAAA,MAAM,GAAGmB,OAAO,CAACF,IAAD,CAAP,EAAT;AACH,GAFD,MAEO,IAAI,IAAIA,IAAJ,IAAYA,IAAI,GAAG,CAAvB,EAA0B;AAC7BjB,IAAAA,MAAM,GAAGqB,OAAO,CAACzB,KAAD,EAAQ,OAAR,CAAhB;AACH,GAFM,MAEA,IAAI,KAAKqB,IAAL,IAAaA,IAAI,GAAG,EAAxB,EAA4B;AAC/BjB,IAAAA,MAAM,GAAGG,mBAAmB,CAACP,KAAD,EAAQG,OAAO,CAAC5B,MAAR,CAAe8C,IAAI,GAAG,EAAtB,CAAR,CAA5B;AACH,GAFM,MAEA,IAAK,KAAKA,IAAL,IAAaA,IAAI,GAAG,EAAzB,EAA8B;AACjCjB,IAAAA,MAAM,GAAGI,mBAAmB,CAACR,KAAD,EAAQG,OAAO,CAAC5B,MAAR,CAAe8C,IAAI,GAAG,EAAtB,CAAR,CAA5B;AACH,GAFM,MAEA,IAAK,KAAKA,IAAL,IAAaA,IAAI,GAAG,EAAzB,EAA8B;AACjCjB,IAAAA,MAAM,GAAGG,mBAAmB,CAACP,KAAD,EAAQG,OAAO,CAAC5B,MAAR,CAAe,KAAK8C,IAAI,GAAG,EAAZ,CAAf,CAAR,CAA5B;AACH,GAFM,MAEA,IAAK,KAAKA,IAAL,IAAaA,IAAI,GAAG,GAAzB,EAA+B;AAClCjB,IAAAA,MAAM,GAAGI,mBAAmB,CAACR,KAAD,EAAQG,OAAO,CAAC5B,MAAR,CAAe,KAAK8C,IAAI,GAAG,GAAZ,CAAf,CAAR,CAA5B;AACH;;AAED,SAAOjB,MAAP;AACH;AAED;AACA;AACA;AACA;;;AACA,SAASoB,WAAT,CAAqBxB,KAArB,EAA4B;AACxB,MAAM2B,UAAU,GAAG3B,KAAK,CAACW,KAAN,CAAY,CAAZ,CAAnB;AAEAX,EAAAA,KAAK,CAACN,MAAN,GAAe,CAAf;AAEA,SAAOiC,UAAU,CAACC,OAAX,GAAqBX,GAArB,CAAyB,UAAUY,GAAV,EAAe;AAC3C,WAAO,OAAOA,GAAP,GAAa,GAApB;AACH,GAFM,EAEJ/B,IAFI,CAEC,EAFD,CAAP;AAGH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASrB,KAAT,CAAeqD,GAAf,EAAoBC,IAApB,EAA0B;AACtB,MAAMnC,OAAO,GAAG,EAAhB;;AAEA,OAAK,IAAIoC,CAAC,GAAGF,GAAb,EAAkBE,CAAC,IAAID,IAAvB,EAA6BC,CAAC,EAA9B,EAAkC;AAC9BpC,IAAAA,OAAO,CAACC,IAAR,CAAamC,CAAb;AACH;;AAED,SAAOpC,OAAP;AACH;AAID;AACA;AACA;AACA;AACA;;;AACA,SAASqC,WAAT,CAAqBC,QAArB,EAA+B;AAC3B,SAAO,UAAUC,CAAV,EAAa;AAChB,WAAO,CAACD,QAAQ,KAAK,IAAb,IAAqBC,CAAC,CAACD,QAAF,KAAeA,QAArC,KAAkDA,QAAQ,KAAK,KAAtE;AACH,GAFD;AAGH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASE,eAAT,CAAyBf,IAAzB,EAA+B;AAC3BA,EAAAA,IAAI,GAAGC,QAAQ,CAACD,IAAD,EAAO,EAAP,CAAf;AACA,MAAIjB,MAAM,GAAG,IAAb;;AAEA,MAAIiB,IAAI,KAAK,CAAb,EAAgB;AACZjB,IAAAA,MAAM,GAAG,KAAT;AACH,GAFD,MAEO,IAAIiB,IAAI,KAAK,CAAb,EAAgB;AACnBjB,IAAAA,MAAM,GAAG,MAAT;AACH,GAFM,MAEA,IAAK,IAAIiB,IAAJ,IAAYA,IAAI,GAAG,CAAxB,EAA4B;AAC/BjB,IAAAA,MAAM,GAAG,WAAT;AACH,GAFM,MAEA,IAAK,IAAIiB,IAAJ,IAAYA,IAAI,GAAG,CAAxB,EAA4B;AAC/BjB,IAAAA,MAAM,GAAG,OAAT;AACH,GAFM,MAEA,IAAIiB,IAAI,KAAK,CAAb,EAAgB;AACnBjB,IAAAA,MAAM,GAAG,MAAT;AACH,GAFM,MAEA,IAAIiB,IAAI,KAAK,CAAb,EAAgB;AACnBjB,IAAAA,MAAM,GAAG,QAAT;AACH,GAFM,MAEA,IAAK,KAAKiB,IAAL,IAAaA,IAAI,GAAG,EAArB,IAA4BA,IAAI,KAAK,EAArC,IAA4C,KAAKA,IAAL,IAAaA,IAAI,GAAG,EAApE,EAAyE;AAC5EjB,IAAAA,MAAM,GAAG,kBAAT;AACH,GAFM,MAEA,IAAK,KAAKiB,IAAL,IAAaA,IAAI,GAAG,EAArB,IAA4BA,IAAI,KAAK,EAArC,IAA4C,KAAKA,IAAL,IAAaA,IAAI,GAAG,GAApE,EAA0E;AAC7EjB,IAAAA,MAAM,GAAG,kBAAT;AACH;;AAED,SAAOA,MAAP;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASC,QAAT,CAAkBgC,IAAlB,EAAwBlC,OAAxB,EAAiC;AAC7B,MAAIA,OAAO,CAAC9B,SAAZ,EAAuB;AACnB,WAAON,QAAQ,CAACuE,SAAT,CAAmBD,IAAnB,CAAP;AACH;;AAED,SAAOA,IAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASZ,OAAT,CAAiBzB,KAAjB,EAAwB6B,GAAxB,EAA6BU,KAA7B,EAAoC;AAChC,MAAI,CAACA,KAAL,EAAY;AACRA,IAAAA,KAAK,GAAG,EAAR;AACH;;AAEDvC,EAAAA,KAAK,CAACH,IAAN,CAAWgC,GAAX;AAEA,oBAAWA,GAAX,SAAiBU,KAAK,sBAAcA,KAAd,UAAyB,EAA/C;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASnB,SAAT,CAAmBpB,KAAnB,EAA0BuC,KAA1B,EAAiC;AAC7B,SAAOd,OAAO,CAACzB,KAAD,EAAQ,MAAR,EAAgBuC,KAAhB,CAAd;AACH;;AAED,SAAShC,mBAAT,CAA6BP,KAA7B,EAAoCc,KAApC,EAA2C;AACvC,SAAOW,OAAO,CAACzB,KAAD,EAAQ,MAAR,EAAgB,WAAWc,KAA3B,CAAd;AACH;;AAED,SAASN,mBAAT,CAA6BR,KAA7B,EAAoCc,KAApC,EAA2C;AACvC,SAAOW,OAAO,CAACzB,KAAD,EAAQ,MAAR,EAAgB,sBAAsBc,KAAtC,CAAd;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASY,QAAT,CAAkB1B,KAAlB,EAAyBuC,KAAzB,EAAgC;AAC5B,MAAIC,IAAJ;;AAEA,MAAIxC,KAAK,CAACW,KAAN,CAAY,CAAC,CAAb,EAAgB,CAAhB,MAAuB4B,KAA3B,EAAkC;AAC9BC,IAAAA,IAAI,GAAGxC,KAAK,CAACyC,GAAN,EAAP;AACH;;AAED,MAAID,IAAJ,EAAU;AACN,WAAO,OAAOD,KAAP,GAAe,GAAtB;AACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,QAAT,CAAkBL,IAAlB,EAAwBlC,OAAxB,EAAiCwC,QAAjC,EAA2C;AACvC,MAAIC,SAAS,GAAG,KAAhB;AACA,MAAMC,WAAW,GAAG,CAApB;;AAEA,WAASC,MAAT,GAAkB;AACd,WAAO,EAAP;AACH;;AAED,WAASC,wBAAT,CAAkCC,CAAlC,EAAqCC,EAArC,EAAyC;AACrCN,IAAAA,QAAQ,CAAC,oBAAD,EAAuBM,EAAvB,CAAR;AACA,WAAO,EAAP;AACH;;AAED,WAASC,wBAAT,CAAkCF,CAAlC,EAAqCC,EAArC,EAAyC;AACrCN,IAAAA,QAAQ,CAAC,oBAAD,EAAuBM,EAAvB,CAAR;AACA,WAAO,EAAP;AACH;;AAED,WAAS7E,OAAT,CAAiB4E,CAAjB,EAAoB;AAChB,QAAI7C,OAAO,CAAC/B,OAAZ,EAAqB;AACjBuE,MAAAA,QAAQ,CAAC,SAAD,EAAY,CAAC,CAAb,CAAR;AACH,KAFD,MAEO;AACHA,MAAAA,QAAQ,CAAC,MAAD,EAASK,CAAT,CAAR;AACH;;AAED,WAAO,EAAP;AACH;;AAED,WAASG,QAAT,CAAkBH,CAAlB,EAAqBC,EAArB,EAAyB;AACrBL,IAAAA,SAAS,GAAG,IAAZ;;AACA,QAAIK,EAAE,CAACG,IAAH,GAAU1D,MAAV,KAAqB,CAAzB,EAA4B;AACxBuD,MAAAA,EAAE,GAAG,GAAL;AACH;;AAEDA,IAAAA,EAAE,GAAGA,EAAE,CAACI,SAAH,CAAa,GAAb,EAAkBtC,KAAlB,CAAwB,GAAxB,CAAL;;AANqB,gDAQLkC,EARK;AAAA;;AAAA;AAQrB,6DAAoB;AAAA,YAAT7D,CAAS;AAChBuD,QAAAA,QAAQ,CAAC,SAAD,EAAYvD,CAAZ,CAAR;AACH;AAVoB;AAAA;AAAA;AAAA;AAAA;;AAYrB,WAAO,EAAP;AACH;;AAED,WAASkE,QAAT,CAAkBN,CAAlB,EAAqB;AACjBL,IAAAA,QAAQ,CAAC,MAAD,EAASK,CAAT,CAAR;AAEA,WAAO,EAAP;AACH;;AAED,WAAShC,GAAT,CAAagC,CAAb,EAAgB;AACZL,IAAAA,QAAQ,CAAC,KAAD,EAAQK,CAAR,CAAR;AAEA,WAAO,EAAP;AACH;AAED;;;AACA,MAAMO,MAAM,GAAG,CAAC;AACZC,IAAAA,OAAO,EAAE,QADG;AAEZC,IAAAA,GAAG,EAAEX;AAFO,GAAD,EAGZ;AACCU,IAAAA,OAAO,EAAE,gBADV;AAECC,IAAAA,GAAG,EAAEX;AAFN,GAHY,EAMZ;AACCU,IAAAA,OAAO,EAAE,YADV;AAECC,IAAAA,GAAG,EAAEX;AAFN,GANY,EASZ;AACCU,IAAAA,OAAO,EAAE,6BADV;AAECC,IAAAA,GAAG,EAAEzC;AAFN,GATY,EAYZ;AACCwC,IAAAA,OAAO,EAAE,oBADV;AAECC,IAAAA,GAAG,EAAEV;AAFN,GAZY,EAeZ;AACCS,IAAAA,OAAO,EAAE,oBADV;AAECC,IAAAA,GAAG,EAAEP;AAFN,GAfY,EAkBZ;AACCM,IAAAA,OAAO,EAAE,KADV;AAECC,IAAAA,GAAG,EAAErF;AAFN,GAlBY,EAqBZ;AACCoF,IAAAA,OAAO,EAAE,QADV;AAECC,IAAAA,GAAG,EAAErF;AAFN,GArBY,EAwBZ;AACCoF,IAAAA,OAAO,EAAE,KADV;AAECC,IAAAA,GAAG,EAAErF;AAFN,GAxBY,EA2BZ;AACCoF,IAAAA,OAAO,EAAE,2BADV;AAECC,IAAAA,GAAG,EAAEN;AAFN,GA3BY,EA8BZ;AACC;AACA;AACA;AACA;AACA;AACA;AACA;AACAK,IAAAA,OAAO,EAAE,aARV;AASCC,IAAAA,GAAG,EAAEX;AATN,GA9BY,EAwCZ;AACC;AACA;AACAU,IAAAA,OAAO,EAAE,yBAHV;AAICC,IAAAA,GAAG,EAAEX;AAJN,GAxCY,EA6CZ;AACC;AACAU,IAAAA,OAAO,EAAE,oBAFV;AAGCC,IAAAA,GAAG,EAAEX;AAHN,GA7CY,EAiDZ;AACC;AACR;AACA;AACA;AACA;AACA;AACA;AACQU,IAAAA,OAAO,EAAE,uBARV;AASCC,IAAAA,GAAG,EAAEH;AATN,GAjDY,CAAf;;AA6DA,WAASI,OAAT,CAAiBC,OAAjB,EAA0BC,CAA1B,EAA6B;AACzB,QAAIA,CAAC,GAAGf,WAAJ,IAAmBD,SAAvB,EAAkC;AAC9B;AACH;;AAEDA,IAAAA,SAAS,GAAG,KAAZ;AAEAP,IAAAA,IAAI,GAAGA,IAAI,CAACwB,OAAL,CAAaF,OAAO,CAACH,OAArB,EAA8BG,OAAO,CAACF,GAAtC,CAAP;AACH;;AAED,MAAMK,QAAQ,GAAG,EAAjB;AACA,cAAezB,IAAf;AAAA,MAAK3C,MAAL,SAAKA,MAAL;;AAEAqE,EAAAA,KAAK,EACL,OAAOrE,MAAM,GAAG,CAAhB,EAAmB;AACf,SAAK,IAAIkE,CAAC,GAAG,CAAR,EAAWI,CAAC,GAAG,CAAf,EAAkBC,GAAG,GAAGV,MAAM,CAAC7D,MAApC,EAA4CsE,CAAC,GAAGC,GAAhD,EAAqDL,CAAC,GAAG,EAAEI,CAA3D,EAA8D;AAC1D,UAAML,OAAO,GAAGJ,MAAM,CAACK,CAAD,CAAtB;AACAF,MAAAA,OAAO,CAACC,OAAD,EAAUC,CAAV,CAAP;;AAEA,UAAIvB,IAAI,CAAC3C,MAAL,KAAgBA,MAApB,EAA4B;AACxB;AACA;AACAA,QAAAA,MAAM,GAAG2C,IAAI,CAAC3C,MAAd;AACA,iBAASqE,KAAT;AACH;AACJ;;AAED,QAAI1B,IAAI,CAAC3C,MAAL,KAAgBA,MAApB,EAA4B;AACxB;AACH;;AACDoE,IAAAA,QAAQ,CAACjE,IAAT,CAAc,CAAd;AAEAH,IAAAA,MAAM,GAAG2C,IAAI,CAAC3C,MAAd;AACH;;AAED,SAAOoE,QAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASI,iBAAT,CAA2BC,WAA3B,EAAwClE,KAAxC,EAA+CC,IAA/C,EAAqD;AACjD,MAAID,KAAK,KAAK,MAAd,EAAsB;AAClBkE,IAAAA,WAAW,GAAGA,WAAW,CAACC,MAAZ,CAAmBnC,WAAW,CAACG,eAAe,CAAClC,IAAD,CAAhB,CAA9B,CAAd;AACAiE,IAAAA,WAAW,CAACtE,IAAZ,CAAiB;AAACI,MAAAA,KAAK,EAALA,KAAD;AAAQC,MAAAA,IAAI,EAAJA,IAAR;AAAcgC,MAAAA,QAAQ,EAAEE,eAAe,CAAClC,IAAD;AAAvC,KAAjB;AACH;;AAED,SAAOiE,WAAP;AACH;;IAEKE,M;AACF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,kBAAYlE,OAAZ,EAAqB;AAAA;;AACjBA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;;AAEA,QAAIA,OAAO,CAAC5B,MAAZ,EAAoB;AAChB4B,MAAAA,OAAO,CAAC5B,MAAR,GAAiB+F,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBtG,QAAQ,CAACM,MAA3B,EAAmC4B,OAAO,CAAC5B,MAA3C,CAAjB;AACH;;AAED,SAAK4B,OAAL,GAAemE,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBtG,QAAlB,EAA4BkC,OAA5B,CAAf;AACA,SAAKH,KAAL,GAAa,EAAb;AACA,SAAKmE,WAAL,GAAmB,EAAnB;AACH;AACD;AACJ;AACA;AACA;;;;;WACI,gBAAOK,KAAP,EAAc;AAAA;;AACVA,MAAAA,KAAK,GAAG,OAAOA,KAAP,KAAiB,QAAjB,GAA4B,CAACA,KAAD,CAA5B,GAAsCA,KAA9C;AACA,UAAOxE,KAAP,GAAyB,IAAzB,CAAOA,KAAP;AAAA,UAAcG,OAAd,GAAyB,IAAzB,CAAcA,OAAd;AACA,UAAMsE,GAAG,GAAG,EAAZ;AAEA,WAAKN,WAAL,CAAiBzF,OAAjB,CAAyB,UAAAgG,OAAO,EAAI;AAChC,YAAMC,MAAM,GAAG5E,cAAc,CAACC,KAAD,EAAQ0E,OAAO,CAACzE,KAAhB,EAAuByE,OAAO,CAACxE,IAA/B,EAAqCC,OAArC,CAA7B;;AAEA,YAAIwE,MAAJ,EAAY;AACRF,UAAAA,GAAG,CAAC5E,IAAJ,CAAS8E,MAAT;AACH;AACJ,OAND;AAQAjC,MAAAA,QAAQ,CAAC8B,KAAK,CAAC1E,IAAN,CAAW,EAAX,CAAD,EAAiBK,OAAjB,EAA0B,UAACF,KAAD,EAAQC,IAAR,EAAiB;AAC/C,YAAMyE,MAAM,GAAG5E,cAAc,CAACC,KAAD,EAAQC,KAAR,EAAeC,IAAf,EAAqBC,OAArB,CAA7B;;AAEA,YAAIwE,MAAJ,EAAY;AACRF,UAAAA,GAAG,CAAC5E,IAAJ,CAAS8E,MAAT;AACH;;AAED,YAAIxE,OAAO,CAAC7B,MAAZ,EAAoB;AAChB,UAAA,KAAI,CAAC6F,WAAL,GAAmBD,iBAAiB,CAAC,KAAI,CAACC,WAAN,EAAmBlE,KAAnB,EAA0BC,IAA1B,CAApC;AACH;AACJ,OAVO,CAAR;;AAYA,UAAIF,KAAK,CAACN,MAAV,EAAkB;AACd+E,QAAAA,GAAG,CAAC5E,IAAJ,CAAS2B,WAAW,CAACxB,KAAD,CAApB;AACH;;AAED,aAAOyE,GAAG,CAAC3E,IAAJ,CAAS,EAAT,CAAP;AACH;;;;;;AAGL8E,MAAM,CAACC,OAAP,GAAiBR,MAAjB", "sourcesContent": ["'use strict';\nconst entities = require('entities');\nconst defaults = {\n    fg: '#FFF',\n    bg: '#000',\n    newline: false,\n    escapeXML: false,\n    stream: false,\n    colors: getDefaultColors()\n};\n\nfunction getDefaultColors() {\n    const colors = {\n        0: '#000',\n        1: '#A00',\n        2: '#0A0',\n        3: '#A50',\n        4: '#00A',\n        5: '#A0A',\n        6: '#0AA',\n        7: '#AAA',\n        8: '#555',\n        9: '#F55',\n        10: '#5F5',\n        11: '#FF5',\n        12: '#55F',\n        13: '#F5F',\n        14: '#5FF',\n        15: '#FFF'\n    };\n\n    range(0, 5).forEach(red => {\n        range(0, 5).forEach(green => {\n            range(0, 5).forEach(blue => setStyleColor(red, green, blue, colors));\n        });\n    });\n\n    range(0, 23).forEach(function (gray) {\n        const c = gray + 232;\n        const l = toHexString(gray * 10 + 8);\n\n        colors[c] = '#' + l + l + l;\n    });\n\n    return colors;\n}\n\n/**\n * @param {number} red\n * @param {number} green\n * @param {number} blue\n * @param {object} colors\n */\nfunction setStyleColor(red, green, blue, colors) {\n    const c = 16 + (red * 36) + (green * 6) + blue;\n    const r = red > 0 ? red * 40 + 55 : 0;\n    const g = green > 0 ? green * 40 + 55 : 0;\n    const b = blue > 0 ? blue * 40 + 55 : 0;\n\n    colors[c] = toColorHexString([r, g, b]);\n}\n\n/**\n * Converts from a number like 15 to a hex string like 'F'\n * @param {number} num\n * @returns {string}\n */\nfunction toHexString(num) {\n    let str = num.toString(16);\n\n    while (str.length < 2) {\n        str = '0' + str;\n    }\n\n    return str;\n}\n\n/**\n * Converts from an array of numbers like [15, 15, 15] to a hex string like 'FFF'\n * @param {[red, green, blue]} ref\n * @returns {string}\n */\nfunction toColorHexString(ref) {\n    const results = [];\n\n    for (const r of ref) {\n        results.push(toHexString(r));\n    }\n\n    return '#' + results.join('');\n}\n\n/**\n * @param {Array} stack\n * @param {string} token\n * @param {*} data\n * @param {object} options\n */\nfunction generateOutput(stack, token, data, options) {\n    let result;\n\n    if (token === 'text') {\n        result = pushText(data, options);\n    } else if (token === 'display') {\n        result = handleDisplay(stack, data, options);\n    } else if (token === 'xterm256Foreground') {\n        result = pushForegroundColor(stack, options.colors[data]);\n    } else if (token === 'xterm256Background') {\n        result = pushBackgroundColor(stack, options.colors[data]);\n    } else if (token === 'rgb') {\n        result = handleRgb(stack, data);\n    }\n\n    return result;\n}\n\n/**\n * @param {Array} stack\n * @param {string} data\n * @returns {*}\n */\nfunction handleRgb(stack, data) {\n    data = data.substring(2).slice(0, -1);\n    const operation = +data.substr(0, 2);\n\n    const color = data.substring(5).split(';');\n    const rgb = color.map(function (value) {\n        return ('0' + Number(value).toString(16)).substr(-2);\n    }).join('');\n\n    return pushStyle(stack, (operation === 38 ? 'color:#' : 'background-color:#') + rgb);\n}\n\n/**\n * @param {Array} stack\n * @param {number} code\n * @param {object} options\n * @returns {*}\n */\nfunction handleDisplay(stack, code, options) {\n    code = parseInt(code, 10);\n\n    const codeMap = {\n        '-1': () => '<br/>',\n        0: () => stack.length && resetStyles(stack),\n        1: () => pushTag(stack, 'b'),\n        3: () => pushTag(stack, 'i'),\n        4: () => pushTag(stack, 'u'),\n        8: () => pushStyle(stack, 'display:none'),\n        9: () => pushTag(stack, 'strike'),\n        22: () => pushStyle(stack, 'font-weight:normal;text-decoration:none;font-style:normal'),\n        23: () => closeTag(stack, 'i'),\n        24: () => closeTag(stack, 'u'),\n        39: () => pushForegroundColor(stack, options.fg),\n        49: () => pushBackgroundColor(stack, options.bg),\n        53: () => pushStyle(stack, 'text-decoration:overline')\n    };\n\n    let result;\n    if (codeMap[code]) {\n        result = codeMap[code]();\n    } else if (4 < code && code < 7) {\n        result = pushTag(stack, 'blink');\n    } else if (29 < code && code < 38) {\n        result = pushForegroundColor(stack, options.colors[code - 30]);\n    } else if ((39 < code && code < 48)) {\n        result = pushBackgroundColor(stack, options.colors[code - 40]);\n    } else if ((89 < code && code < 98)) {\n        result = pushForegroundColor(stack, options.colors[8 + (code - 90)]);\n    } else if ((99 < code && code < 108)) {\n        result = pushBackgroundColor(stack, options.colors[8 + (code - 100)]);\n    }\n\n    return result;\n}\n\n/**\n * Clear all the styles\n * @returns {string}\n */\nfunction resetStyles(stack) {\n    const stackClone = stack.slice(0);\n\n    stack.length = 0;\n\n    return stackClone.reverse().map(function (tag) {\n        return '</' + tag + '>';\n    }).join('');\n}\n\n/**\n * Creates an array of numbers ranging from low to high\n * @param {number} low\n * @param {number} high\n * @returns {Array}\n * @example range(3, 7); // creates [3, 4, 5, 6, 7]\n */\nfunction range(low, high) {\n    const results = [];\n\n    for (let j = low; j <= high; j++) {\n        results.push(j);\n    }\n\n    return results;\n}\n\n\n\n/**\n * Returns a new function that is true if value is NOT the same category\n * @param {string} category\n * @returns {function}\n */\nfunction notCategory(category) {\n    return function (e) {\n        return (category === null || e.category !== category) && category !== 'all';\n    };\n}\n\n/**\n * Converts a code into an ansi token type\n * @param {number} code\n * @returns {string}\n */\nfunction categoryForCode(code) {\n    code = parseInt(code, 10);\n    let result = null;\n\n    if (code === 0) {\n        result = 'all';\n    } else if (code === 1) {\n        result = 'bold';\n    } else if ((2 < code && code < 5)) {\n        result = 'underline';\n    } else if ((4 < code && code < 7)) {\n        result = 'blink';\n    } else if (code === 8) {\n        result = 'hide';\n    } else if (code === 9) {\n        result = 'strike';\n    } else if ((29 < code && code < 38) || code === 39 || (89 < code && code < 98)) {\n        result = 'foreground-color';\n    } else if ((39 < code && code < 48) || code === 49 || (99 < code && code < 108)) {\n        result = 'background-color';\n    }\n\n    return result;\n}\n\n/**\n * @param {string} text\n * @param {object} options\n * @returns {string}\n */\nfunction pushText(text, options) {\n    if (options.escapeXML) {\n        return entities.encodeXML(text);\n    }\n\n    return text;\n}\n\n/**\n * @param {Array} stack\n * @param {string} tag\n * @param {string} [style='']\n * @returns {string}\n */\nfunction pushTag(stack, tag, style) {\n    if (!style) {\n        style = '';\n    }\n\n    stack.push(tag);\n\n    return `<${tag}${style ? ` style=\"${style}\"` : ''}>`;\n}\n\n/**\n * @param {Array} stack\n * @param {string} style\n * @returns {string}\n */\nfunction pushStyle(stack, style) {\n    return pushTag(stack, 'span', style);\n}\n\nfunction pushForegroundColor(stack, color) {\n    return pushTag(stack, 'span', 'color:' + color);\n}\n\nfunction pushBackgroundColor(stack, color) {\n    return pushTag(stack, 'span', 'background-color:' + color);\n}\n\n/**\n * @param {Array} stack\n * @param {string} style\n * @returns {string}\n */\nfunction closeTag(stack, style) {\n    let last;\n\n    if (stack.slice(-1)[0] === style) {\n        last = stack.pop();\n    }\n\n    if (last) {\n        return '</' + style + '>';\n    }\n}\n\n/**\n * @param {string} text\n * @param {object} options\n * @param {function} callback\n * @returns {Array}\n */\nfunction tokenize(text, options, callback) {\n    let ansiMatch = false;\n    const ansiHandler = 3;\n\n    function remove() {\n        return '';\n    }\n\n    function removeXterm256Foreground(m, g1) {\n        callback('xterm256Foreground', g1);\n        return '';\n    }\n\n    function removeXterm256Background(m, g1) {\n        callback('xterm256Background', g1);\n        return '';\n    }\n\n    function newline(m) {\n        if (options.newline) {\n            callback('display', -1);\n        } else {\n            callback('text', m);\n        }\n\n        return '';\n    }\n\n    function ansiMess(m, g1) {\n        ansiMatch = true;\n        if (g1.trim().length === 0) {\n            g1 = '0';\n        }\n\n        g1 = g1.trimRight(';').split(';');\n\n        for (const g of g1) {\n            callback('display', g);\n        }\n\n        return '';\n    }\n\n    function realText(m) {\n        callback('text', m);\n\n        return '';\n    }\n\n    function rgb(m) {\n        callback('rgb', m);\n\n        return '';\n    }\n\n    /* eslint no-control-regex:0 */\n    const tokens = [{\n        pattern: /^\\x08+/,\n        sub: remove\n    }, {\n        pattern: /^\\x1b\\[[012]?K/,\n        sub: remove\n    }, {\n        pattern: /^\\x1b\\[\\(B/,\n        sub: remove\n    }, {\n        pattern: /^\\x1b\\[[34]8;2;\\d+;\\d+;\\d+m/,\n        sub: rgb\n    }, {\n        pattern: /^\\x1b\\[38;5;(\\d+)m/,\n        sub: removeXterm256Foreground\n    }, {\n        pattern: /^\\x1b\\[48;5;(\\d+)m/,\n        sub: removeXterm256Background\n    }, {\n        pattern: /^\\n/,\n        sub: newline\n    }, {\n        pattern: /^\\r+\\n/,\n        sub: newline\n    }, {\n        pattern: /^\\r/,\n        sub: newline\n    }, {\n        pattern: /^\\x1b\\[((?:\\d{1,3};?)+|)m/,\n        sub: ansiMess\n    }, {\n        // CSI n J\n        // ED - Erase in Display Clears part of the screen.\n        // If n is 0 (or missing), clear from cursor to end of screen.\n        // If n is 1, clear from cursor to beginning of the screen.\n        // If n is 2, clear entire screen (and moves cursor to upper left on DOS ANSI.SYS).\n        // If n is 3, clear entire screen and delete all lines saved in the scrollback buffer\n        //   (this feature was added for xterm and is supported by other terminal applications).\n        pattern: /^\\x1b\\[\\d?J/,\n        sub: remove\n    }, {\n        // CSI n ; m f\n        // HVP - Horizontal Vertical Position Same as CUP\n        pattern: /^\\x1b\\[\\d{0,3};\\d{0,3}f/,\n        sub: remove\n    }, {\n        // catch-all for CSI sequences?\n        pattern: /^\\x1b\\[?[\\d;]{0,3}/,\n        sub: remove\n    }, {\n        /**\n         * extracts real text - not containing:\n         * - `\\x1b' - ESC - escape (Ascii 27)\n         * - '\\x08' - BS - backspace (Ascii 8)\n         * - `\\n` - Newline - linefeed (LF) (ascii 10)\n         * - `\\r` - Windows Carriage Return (CR)\n         */\n        pattern: /^(([^\\x1b\\x08\\r\\n])+)/,\n        sub: realText\n    }];\n\n    function process(handler, i) {\n        if (i > ansiHandler && ansiMatch) {\n            return;\n        }\n\n        ansiMatch = false;\n\n        text = text.replace(handler.pattern, handler.sub);\n    }\n\n    const results1 = [];\n    let {length} = text;\n\n    outer:\n    while (length > 0) {\n        for (let i = 0, o = 0, len = tokens.length; o < len; i = ++o) {\n            const handler = tokens[i];\n            process(handler, i);\n\n            if (text.length !== length) {\n                // We matched a token and removed it from the text. We need to\n                // start matching *all* tokens against the new text.\n                length = text.length;\n                continue outer;\n            }\n        }\n\n        if (text.length === length) {\n            break;\n        }\n        results1.push(0);\n\n        length = text.length;\n    }\n\n    return results1;\n}\n\n/**\n * If streaming, then the stack is \"sticky\"\n *\n * @param {Array} stickyStack\n * @param {string} token\n * @param {*} data\n * @returns {Array}\n */\nfunction updateStickyStack(stickyStack, token, data) {\n    if (token !== 'text') {\n        stickyStack = stickyStack.filter(notCategory(categoryForCode(data)));\n        stickyStack.push({token, data, category: categoryForCode(data)});\n    }\n\n    return stickyStack;\n}\n\nclass Filter {\n    /**\n     * @param {object} options\n     * @param {string=} options.fg The default foreground color used when reset color codes are encountered.\n     * @param {string=} options.bg The default background color used when reset color codes are encountered.\n     * @param {boolean=} options.newline Convert newline characters to `<br/>`.\n     * @param {boolean=} options.escapeXML Generate HTML/XML entities.\n     * @param {boolean=} options.stream Save style state across invocations of `toHtml()`.\n     * @param {(string[] | {[code: number]: string})=} options.colors Can override specific colors or the entire ANSI palette.\n     */\n    constructor(options) {\n        options = options || {};\n\n        if (options.colors) {\n            options.colors = Object.assign({}, defaults.colors, options.colors);\n        }\n\n        this.options = Object.assign({}, defaults, options);\n        this.stack = [];\n        this.stickyStack = [];\n    }\n    /**\n     * @param {string | string[]} input\n     * @returns {string}\n     */\n    toHtml(input) {\n        input = typeof input === 'string' ? [input] : input;\n        const {stack, options} = this;\n        const buf = [];\n\n        this.stickyStack.forEach(element => {\n            const output = generateOutput(stack, element.token, element.data, options);\n\n            if (output) {\n                buf.push(output);\n            }\n        });\n\n        tokenize(input.join(''), options, (token, data) => {\n            const output = generateOutput(stack, token, data, options);\n\n            if (output) {\n                buf.push(output);\n            }\n\n            if (options.stream) {\n                this.stickyStack = updateStickyStack(this.stickyStack, token, data);\n            }\n        });\n\n        if (stack.length) {\n            buf.push(resetStyles(stack));\n        }\n\n        return buf.join('');\n    }\n}\n\nmodule.exports = Filter;\n"], "file": "ansi_to_html.js"}