import type { AgAreaSeriesOptions } from './areaOptions';
import type { AgBarSeriesOptions } from './barOptions';
import type { AgBoxPlotSeriesOptions } from './boxPlotOptions';
import type { AgBubbleSeriesOptions } from './bubbleOptions';
import type { AgBulletSeriesOptions } from './bulletOptions';
import type { AgCandlestickSeriesOptions } from './candlestickOptions';
import type { AgConeFunnelSeriesOptions } from './coneFunnelOptions';
import type { AgFunnelSeriesOptions } from './funnelOptions';
import type { AgHeatmapSeriesOptions } from './heatmapOptions';
import type { AgHistogramSeriesOptions } from './histogramOptions';
import type { AgLineSeriesOptions } from './lineOptions';
import type { AgOhlcSeriesOptions } from './ohlcOptions';
import type { AgRangeAreaSeriesOptions } from './rangeAreaOptions';
import type { AgRangeBarSeriesOptions } from './rangeBarOptions';
import type { AgScatterSeriesOptions } from './scatterOptions';
import type { AgWaterfallSeriesOptions } from './waterfallOptions';
export type AgCartesianSeriesOptions = AgAreaSeriesOptions | AgBarSeriesOptions | AgBoxPlotSeriesOptions | AgBubbleSeriesOptions | AgBulletSeriesOptions | AgCandlestickSeriesOptions | AgConeFunnelSeriesOptions | AgFunnelSeriesOptions | AgHeatmapSeriesOptions | AgHistogramSeriesOptions | AgLineSeriesOptions | AgOhlcSeriesOptions | AgRangeAreaSeriesOptions | AgRangeBarSeriesOptions | AgScatterSeriesOptions | AgWaterfallSeriesOptions;
