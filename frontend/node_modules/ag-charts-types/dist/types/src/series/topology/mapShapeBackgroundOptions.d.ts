import type { <PERSON><PERSON>J<PERSON><PERSON> } from '../../chart/types';
import type { FillOptions, LineDashOptions, StrokeOptions } from '../cartesian/commonOptions';
export interface AgMapShapeBackgroundThemeableOptions extends FillOptions, StrokeOptions, LineDashOptions {
}
export interface AgMapShapeBackgroundOptions extends AgMapShapeBackgroundThemeableOptions {
    /** Configuration for the Map Shape Background. */
    type: 'map-shape-background';
    /** GeoJSON data. */
    topology?: GeoJSON;
}
