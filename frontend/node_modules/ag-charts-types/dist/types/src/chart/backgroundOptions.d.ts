import type { Opacity, PixelSize } from './types';
export interface AgChartBackgroundImage {
    /** URL of the image. */
    url: string;
    /** Distance from the left border of the chart to the left border of the image. If neither left nor right specified, the image is centred horizontally. */
    left?: PixelSize;
    /** Distance from the top border of the chart to the top border of the image. If neither top nor bottom specified, the image is centred vertically. */
    top?: PixelSize;
    /** Distance from the right border of the chart to the right border of the image. If neither left nor right specified, the image is centred horizontally. */
    right?: PixelSize;
    /** Distance from the bottom border of the chart to the bottom border of the image. If neither top nor bottom specified, the image is centred vertically. */
    bottom?: PixelSize;
    /** Width of the image. If both left and width are specified, right is ignored. If only height is provided, width will be computed to preserve the original width/height ratio. If neither is provided, the original width is used. */
    width?: PixelSize;
    /** Height of the image. If both top and height are specified, bottom is ignored. If only width is provided, height will be computed to preserve the original width/height ratio.  If neither is provided, the original height is used. */
    height?: PixelSize;
    /** Opacity of the image. */
    opacity?: Opacity;
}
