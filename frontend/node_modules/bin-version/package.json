{"name": "bin-version", "version": "6.0.0", "description": "Get the version of a binary in semver format", "license": "MIT", "repository": "sindresorhus/bin-version", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["binary", "executable", "version", "semver", "semantic", "cli"], "dependencies": {"execa": "^5.0.0", "find-versions": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}}