Ace (Ajax.org Cloud9 Editor)
============================
[![CDNJS](https://img.shields.io/cdnjs/v/ace.svg)](https://cdnjs.com/libraries/ace)
[![npm](https://img.shields.io/npm/v/ace-builds.svg)](https://www.npmjs.com/package/ace-builds)

Ace is a code editor written in JavaScript.

---

### ❗<span style="color:red">ISSUES ARE DISABLED IN THIS REPOSITORY</span>❗

This repository has only generated files. To report your issues, suggest features, ask questions, or work on Ace editor itself please go to Ace repository https://github.com/ajaxorg/ace.

---


Here you can find pre-built files for convenience of embedding. It contains 4 versions:
 * [src](https://github.com/ajaxorg/ace-builds/tree/master/src)              concatenated but not minified
 * [src-min](https://github.com/ajaxorg/ace-builds/tree/master/src-min)      concatenated and minified with uglify.js
 * [src-noconflict](https://github.com/ajaxorg/ace-builds/tree/master/src-noconflict)      uses ace.require instead of require
 * [src-min-noconflict](https://github.com/ajaxorg/ace-builds/tree/master/src-min-noconflict)      concatenated, minified with uglify.js, and uses ace.require instead of require


For a simple way of embedding ace into webpage see [editor.html](https://github.com/ajaxorg/ace-builds/blob/master/editor.html) or list of other [simple examples](https://github.com/ajaxorg/ace-builds/tree/master/demo).
To see ace in action go to [kitchen-sink-demo](http://ajaxorg.github.io/ace-builds/kitchen-sink.html), [scrollable-page-demo](http://ajaxorg.github.io/ace-builds/demo/scrollable-page.html) or [minimal demo](http://ajaxorg.github.io/ace-builds/editor.html).


