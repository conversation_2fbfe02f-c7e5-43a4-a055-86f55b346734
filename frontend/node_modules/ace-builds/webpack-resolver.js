
ace.config.setModuleUrl('ace/ext/beautify', require('file-loader?esModule=false!./src-noconflict/ext-beautify.js'));
ace.config.setModuleUrl('ace/ext/code_lens', require('file-loader?esModule=false!./src-noconflict/ext-code_lens.js'));
ace.config.setModuleUrl('ace/ext/command_bar', require('file-loader?esModule=false!./src-noconflict/ext-command_bar.js'));
ace.config.setModuleUrl('ace/ext/elastic_tabstops_lite', require('file-loader?esModule=false!./src-noconflict/ext-elastic_tabstops_lite.js'));
ace.config.setModuleUrl('ace/ext/emmet', require('file-loader?esModule=false!./src-noconflict/ext-emmet.js'));
ace.config.setModuleUrl('ace/ext/error_marker', require('file-loader?esModule=false!./src-noconflict/ext-error_marker.js'));
ace.config.setModuleUrl('ace/ext/hardwrap', require('file-loader?esModule=false!./src-noconflict/ext-hardwrap.js'));
ace.config.setModuleUrl('ace/ext/inline_autocomplete', require('file-loader?esModule=false!./src-noconflict/ext-inline_autocomplete.js'));
ace.config.setModuleUrl('ace/ext/keyboard_menu', require('file-loader?esModule=false!./src-noconflict/ext-keybinding_menu.js'));
ace.config.setModuleUrl('ace/ext/language_tools', require('file-loader?esModule=false!./src-noconflict/ext-language_tools.js'));
ace.config.setModuleUrl('ace/ext/linking', require('file-loader?esModule=false!./src-noconflict/ext-linking.js'));
ace.config.setModuleUrl('ace/ext/modelist', require('file-loader?esModule=false!./src-noconflict/ext-modelist.js'));
ace.config.setModuleUrl('ace/ext/options', require('file-loader?esModule=false!./src-noconflict/ext-options.js'));
ace.config.setModuleUrl('ace/ext/prompt', require('file-loader?esModule=false!./src-noconflict/ext-prompt.js'));
ace.config.setModuleUrl('ace/ext/rtl', require('file-loader?esModule=false!./src-noconflict/ext-rtl.js'));
ace.config.setModuleUrl('ace/ext/searchbox', require('file-loader?esModule=false!./src-noconflict/ext-searchbox.js'));
ace.config.setModuleUrl('ace/ext/settings_menu', require('file-loader?esModule=false!./src-noconflict/ext-settings_menu.js'));
ace.config.setModuleUrl('ace/ext/simple_tokenizer', require('file-loader?esModule=false!./src-noconflict/ext-simple_tokenizer.js'));
ace.config.setModuleUrl('ace/ext/spellcheck', require('file-loader?esModule=false!./src-noconflict/ext-spellcheck.js'));
ace.config.setModuleUrl('ace/ext/split', require('file-loader?esModule=false!./src-noconflict/ext-split.js'));
ace.config.setModuleUrl('ace/ext/static_highlight', require('file-loader?esModule=false!./src-noconflict/ext-static_highlight.js'));
ace.config.setModuleUrl('ace/ext/statusbar', require('file-loader?esModule=false!./src-noconflict/ext-statusbar.js'));
ace.config.setModuleUrl('ace/ext/textarea', require('file-loader?esModule=false!./src-noconflict/ext-textarea.js'));
ace.config.setModuleUrl('ace/ext/themelist', require('file-loader?esModule=false!./src-noconflict/ext-themelist.js'));
ace.config.setModuleUrl('ace/ext/whitespace', require('file-loader?esModule=false!./src-noconflict/ext-whitespace.js'));
ace.config.setModuleUrl('ace/keyboard/emacs', require('file-loader?esModule=false!./src-noconflict/keybinding-emacs.js'));
ace.config.setModuleUrl('ace/keyboard/sublime', require('file-loader?esModule=false!./src-noconflict/keybinding-sublime.js'));
ace.config.setModuleUrl('ace/keyboard/vim', require('file-loader?esModule=false!./src-noconflict/keybinding-vim.js'));
ace.config.setModuleUrl('ace/keyboard/vscode', require('file-loader?esModule=false!./src-noconflict/keybinding-vscode.js'));
ace.config.setModuleUrl('ace/mode/abap', require('file-loader?esModule=false!./src-noconflict/mode-abap.js'));
ace.config.setModuleUrl('ace/mode/abc', require('file-loader?esModule=false!./src-noconflict/mode-abc.js'));
ace.config.setModuleUrl('ace/mode/actionscript', require('file-loader?esModule=false!./src-noconflict/mode-actionscript.js'));
ace.config.setModuleUrl('ace/mode/ada', require('file-loader?esModule=false!./src-noconflict/mode-ada.js'));
ace.config.setModuleUrl('ace/mode/alda', require('file-loader?esModule=false!./src-noconflict/mode-alda.js'));
ace.config.setModuleUrl('ace/mode/apache_conf', require('file-loader?esModule=false!./src-noconflict/mode-apache_conf.js'));
ace.config.setModuleUrl('ace/mode/apex', require('file-loader?esModule=false!./src-noconflict/mode-apex.js'));
ace.config.setModuleUrl('ace/mode/applescript', require('file-loader?esModule=false!./src-noconflict/mode-applescript.js'));
ace.config.setModuleUrl('ace/mode/aql', require('file-loader?esModule=false!./src-noconflict/mode-aql.js'));
ace.config.setModuleUrl('ace/mode/asciidoc', require('file-loader?esModule=false!./src-noconflict/mode-asciidoc.js'));
ace.config.setModuleUrl('ace/mode/asl', require('file-loader?esModule=false!./src-noconflict/mode-asl.js'));
ace.config.setModuleUrl('ace/mode/assembly_arm32', require('file-loader?esModule=false!./src-noconflict/mode-assembly_arm32.js'));
ace.config.setModuleUrl('ace/mode/assembly_x86', require('file-loader?esModule=false!./src-noconflict/mode-assembly_x86.js'));
ace.config.setModuleUrl('ace/mode/astro', require('file-loader?esModule=false!./src-noconflict/mode-astro.js'));
ace.config.setModuleUrl('ace/mode/autohotkey', require('file-loader?esModule=false!./src-noconflict/mode-autohotkey.js'));
ace.config.setModuleUrl('ace/mode/basic', require('file-loader?esModule=false!./src-noconflict/mode-basic.js'));
ace.config.setModuleUrl('ace/mode/batchfile', require('file-loader?esModule=false!./src-noconflict/mode-batchfile.js'));
ace.config.setModuleUrl('ace/mode/bibtex', require('file-loader?esModule=false!./src-noconflict/mode-bibtex.js'));
ace.config.setModuleUrl('ace/mode/c9search', require('file-loader?esModule=false!./src-noconflict/mode-c9search.js'));
ace.config.setModuleUrl('ace/mode/c_cpp', require('file-loader?esModule=false!./src-noconflict/mode-c_cpp.js'));
ace.config.setModuleUrl('ace/mode/cirru', require('file-loader?esModule=false!./src-noconflict/mode-cirru.js'));
ace.config.setModuleUrl('ace/mode/clojure', require('file-loader?esModule=false!./src-noconflict/mode-clojure.js'));
ace.config.setModuleUrl('ace/mode/cobol', require('file-loader?esModule=false!./src-noconflict/mode-cobol.js'));
ace.config.setModuleUrl('ace/mode/coffee', require('file-loader?esModule=false!./src-noconflict/mode-coffee.js'));
ace.config.setModuleUrl('ace/mode/coldfusion', require('file-loader?esModule=false!./src-noconflict/mode-coldfusion.js'));
ace.config.setModuleUrl('ace/mode/crystal', require('file-loader?esModule=false!./src-noconflict/mode-crystal.js'));
ace.config.setModuleUrl('ace/mode/csharp', require('file-loader?esModule=false!./src-noconflict/mode-csharp.js'));
ace.config.setModuleUrl('ace/mode/csound_document', require('file-loader?esModule=false!./src-noconflict/mode-csound_document.js'));
ace.config.setModuleUrl('ace/mode/csound_orchestra', require('file-loader?esModule=false!./src-noconflict/mode-csound_orchestra.js'));
ace.config.setModuleUrl('ace/mode/csound_score', require('file-loader?esModule=false!./src-noconflict/mode-csound_score.js'));
ace.config.setModuleUrl('ace/mode/csp', require('file-loader?esModule=false!./src-noconflict/mode-csp.js'));
ace.config.setModuleUrl('ace/mode/css', require('file-loader?esModule=false!./src-noconflict/mode-css.js'));
ace.config.setModuleUrl('ace/mode/csv', require('file-loader?esModule=false!./src-noconflict/mode-csv.js'));
ace.config.setModuleUrl('ace/mode/curly', require('file-loader?esModule=false!./src-noconflict/mode-curly.js'));
ace.config.setModuleUrl('ace/mode/cuttlefish', require('file-loader?esModule=false!./src-noconflict/mode-cuttlefish.js'));
ace.config.setModuleUrl('ace/mode/d', require('file-loader?esModule=false!./src-noconflict/mode-d.js'));
ace.config.setModuleUrl('ace/mode/dart', require('file-loader?esModule=false!./src-noconflict/mode-dart.js'));
ace.config.setModuleUrl('ace/mode/diff', require('file-loader?esModule=false!./src-noconflict/mode-diff.js'));
ace.config.setModuleUrl('ace/mode/django', require('file-loader?esModule=false!./src-noconflict/mode-django.js'));
ace.config.setModuleUrl('ace/mode/dockerfile', require('file-loader?esModule=false!./src-noconflict/mode-dockerfile.js'));
ace.config.setModuleUrl('ace/mode/dot', require('file-loader?esModule=false!./src-noconflict/mode-dot.js'));
ace.config.setModuleUrl('ace/mode/drools', require('file-loader?esModule=false!./src-noconflict/mode-drools.js'));
ace.config.setModuleUrl('ace/mode/edifact', require('file-loader?esModule=false!./src-noconflict/mode-edifact.js'));
ace.config.setModuleUrl('ace/mode/eiffel', require('file-loader?esModule=false!./src-noconflict/mode-eiffel.js'));
ace.config.setModuleUrl('ace/mode/ejs', require('file-loader?esModule=false!./src-noconflict/mode-ejs.js'));
ace.config.setModuleUrl('ace/mode/elixir', require('file-loader?esModule=false!./src-noconflict/mode-elixir.js'));
ace.config.setModuleUrl('ace/mode/elm', require('file-loader?esModule=false!./src-noconflict/mode-elm.js'));
ace.config.setModuleUrl('ace/mode/erlang', require('file-loader?esModule=false!./src-noconflict/mode-erlang.js'));
ace.config.setModuleUrl('ace/mode/flix', require('file-loader?esModule=false!./src-noconflict/mode-flix.js'));
ace.config.setModuleUrl('ace/mode/forth', require('file-loader?esModule=false!./src-noconflict/mode-forth.js'));
ace.config.setModuleUrl('ace/mode/fortran', require('file-loader?esModule=false!./src-noconflict/mode-fortran.js'));
ace.config.setModuleUrl('ace/mode/fsharp', require('file-loader?esModule=false!./src-noconflict/mode-fsharp.js'));
ace.config.setModuleUrl('ace/mode/fsl', require('file-loader?esModule=false!./src-noconflict/mode-fsl.js'));
ace.config.setModuleUrl('ace/mode/ftl', require('file-loader?esModule=false!./src-noconflict/mode-ftl.js'));
ace.config.setModuleUrl('ace/mode/gcode', require('file-loader?esModule=false!./src-noconflict/mode-gcode.js'));
ace.config.setModuleUrl('ace/mode/gherkin', require('file-loader?esModule=false!./src-noconflict/mode-gherkin.js'));
ace.config.setModuleUrl('ace/mode/gitignore', require('file-loader?esModule=false!./src-noconflict/mode-gitignore.js'));
ace.config.setModuleUrl('ace/mode/glsl', require('file-loader?esModule=false!./src-noconflict/mode-glsl.js'));
ace.config.setModuleUrl('ace/mode/gobstones', require('file-loader?esModule=false!./src-noconflict/mode-gobstones.js'));
ace.config.setModuleUrl('ace/mode/golang', require('file-loader?esModule=false!./src-noconflict/mode-golang.js'));
ace.config.setModuleUrl('ace/mode/graphqlschema', require('file-loader?esModule=false!./src-noconflict/mode-graphqlschema.js'));
ace.config.setModuleUrl('ace/mode/groovy', require('file-loader?esModule=false!./src-noconflict/mode-groovy.js'));
ace.config.setModuleUrl('ace/mode/haml', require('file-loader?esModule=false!./src-noconflict/mode-haml.js'));
ace.config.setModuleUrl('ace/mode/handlebars', require('file-loader?esModule=false!./src-noconflict/mode-handlebars.js'));
ace.config.setModuleUrl('ace/mode/haskell', require('file-loader?esModule=false!./src-noconflict/mode-haskell.js'));
ace.config.setModuleUrl('ace/mode/haskell_cabal', require('file-loader?esModule=false!./src-noconflict/mode-haskell_cabal.js'));
ace.config.setModuleUrl('ace/mode/haxe', require('file-loader?esModule=false!./src-noconflict/mode-haxe.js'));
ace.config.setModuleUrl('ace/mode/hjson', require('file-loader?esModule=false!./src-noconflict/mode-hjson.js'));
ace.config.setModuleUrl('ace/mode/html', require('file-loader?esModule=false!./src-noconflict/mode-html.js'));
ace.config.setModuleUrl('ace/mode/html_elixir', require('file-loader?esModule=false!./src-noconflict/mode-html_elixir.js'));
ace.config.setModuleUrl('ace/mode/html_ruby', require('file-loader?esModule=false!./src-noconflict/mode-html_ruby.js'));
ace.config.setModuleUrl('ace/mode/ini', require('file-loader?esModule=false!./src-noconflict/mode-ini.js'));
ace.config.setModuleUrl('ace/mode/io', require('file-loader?esModule=false!./src-noconflict/mode-io.js'));
ace.config.setModuleUrl('ace/mode/ion', require('file-loader?esModule=false!./src-noconflict/mode-ion.js'));
ace.config.setModuleUrl('ace/mode/jack', require('file-loader?esModule=false!./src-noconflict/mode-jack.js'));
ace.config.setModuleUrl('ace/mode/jade', require('file-loader?esModule=false!./src-noconflict/mode-jade.js'));
ace.config.setModuleUrl('ace/mode/java', require('file-loader?esModule=false!./src-noconflict/mode-java.js'));
ace.config.setModuleUrl('ace/mode/javascript', require('file-loader?esModule=false!./src-noconflict/mode-javascript.js'));
ace.config.setModuleUrl('ace/mode/jexl', require('file-loader?esModule=false!./src-noconflict/mode-jexl.js'));
ace.config.setModuleUrl('ace/mode/json', require('file-loader?esModule=false!./src-noconflict/mode-json.js'));
ace.config.setModuleUrl('ace/mode/json5', require('file-loader?esModule=false!./src-noconflict/mode-json5.js'));
ace.config.setModuleUrl('ace/mode/jsoniq', require('file-loader?esModule=false!./src-noconflict/mode-jsoniq.js'));
ace.config.setModuleUrl('ace/mode/jsp', require('file-loader?esModule=false!./src-noconflict/mode-jsp.js'));
ace.config.setModuleUrl('ace/mode/jssm', require('file-loader?esModule=false!./src-noconflict/mode-jssm.js'));
ace.config.setModuleUrl('ace/mode/jsx', require('file-loader?esModule=false!./src-noconflict/mode-jsx.js'));
ace.config.setModuleUrl('ace/mode/julia', require('file-loader?esModule=false!./src-noconflict/mode-julia.js'));
ace.config.setModuleUrl('ace/mode/kotlin', require('file-loader?esModule=false!./src-noconflict/mode-kotlin.js'));
ace.config.setModuleUrl('ace/mode/latex', require('file-loader?esModule=false!./src-noconflict/mode-latex.js'));
ace.config.setModuleUrl('ace/mode/latte', require('file-loader?esModule=false!./src-noconflict/mode-latte.js'));
ace.config.setModuleUrl('ace/mode/less', require('file-loader?esModule=false!./src-noconflict/mode-less.js'));
ace.config.setModuleUrl('ace/mode/liquid', require('file-loader?esModule=false!./src-noconflict/mode-liquid.js'));
ace.config.setModuleUrl('ace/mode/lisp', require('file-loader?esModule=false!./src-noconflict/mode-lisp.js'));
ace.config.setModuleUrl('ace/mode/livescript', require('file-loader?esModule=false!./src-noconflict/mode-livescript.js'));
ace.config.setModuleUrl('ace/mode/logiql', require('file-loader?esModule=false!./src-noconflict/mode-logiql.js'));
ace.config.setModuleUrl('ace/mode/logtalk', require('file-loader?esModule=false!./src-noconflict/mode-logtalk.js'));
ace.config.setModuleUrl('ace/mode/lsl', require('file-loader?esModule=false!./src-noconflict/mode-lsl.js'));
ace.config.setModuleUrl('ace/mode/lua', require('file-loader?esModule=false!./src-noconflict/mode-lua.js'));
ace.config.setModuleUrl('ace/mode/luapage', require('file-loader?esModule=false!./src-noconflict/mode-luapage.js'));
ace.config.setModuleUrl('ace/mode/lucene', require('file-loader?esModule=false!./src-noconflict/mode-lucene.js'));
ace.config.setModuleUrl('ace/mode/makefile', require('file-loader?esModule=false!./src-noconflict/mode-makefile.js'));
ace.config.setModuleUrl('ace/mode/markdown', require('file-loader?esModule=false!./src-noconflict/mode-markdown.js'));
ace.config.setModuleUrl('ace/mode/mask', require('file-loader?esModule=false!./src-noconflict/mode-mask.js'));
ace.config.setModuleUrl('ace/mode/matlab', require('file-loader?esModule=false!./src-noconflict/mode-matlab.js'));
ace.config.setModuleUrl('ace/mode/maze', require('file-loader?esModule=false!./src-noconflict/mode-maze.js'));
ace.config.setModuleUrl('ace/mode/mediawiki', require('file-loader?esModule=false!./src-noconflict/mode-mediawiki.js'));
ace.config.setModuleUrl('ace/mode/mel', require('file-loader?esModule=false!./src-noconflict/mode-mel.js'));
ace.config.setModuleUrl('ace/mode/mips', require('file-loader?esModule=false!./src-noconflict/mode-mips.js'));
ace.config.setModuleUrl('ace/mode/mixal', require('file-loader?esModule=false!./src-noconflict/mode-mixal.js'));
ace.config.setModuleUrl('ace/mode/mushcode', require('file-loader?esModule=false!./src-noconflict/mode-mushcode.js'));
ace.config.setModuleUrl('ace/mode/mysql', require('file-loader?esModule=false!./src-noconflict/mode-mysql.js'));
ace.config.setModuleUrl('ace/mode/nasal', require('file-loader?esModule=false!./src-noconflict/mode-nasal.js'));
ace.config.setModuleUrl('ace/mode/nginx', require('file-loader?esModule=false!./src-noconflict/mode-nginx.js'));
ace.config.setModuleUrl('ace/mode/nim', require('file-loader?esModule=false!./src-noconflict/mode-nim.js'));
ace.config.setModuleUrl('ace/mode/nix', require('file-loader?esModule=false!./src-noconflict/mode-nix.js'));
ace.config.setModuleUrl('ace/mode/nsis', require('file-loader?esModule=false!./src-noconflict/mode-nsis.js'));
ace.config.setModuleUrl('ace/mode/nunjucks', require('file-loader?esModule=false!./src-noconflict/mode-nunjucks.js'));
ace.config.setModuleUrl('ace/mode/objectivec', require('file-loader?esModule=false!./src-noconflict/mode-objectivec.js'));
ace.config.setModuleUrl('ace/mode/ocaml', require('file-loader?esModule=false!./src-noconflict/mode-ocaml.js'));
ace.config.setModuleUrl('ace/mode/odin', require('file-loader?esModule=false!./src-noconflict/mode-odin.js'));
ace.config.setModuleUrl('ace/mode/partiql', require('file-loader?esModule=false!./src-noconflict/mode-partiql.js'));
ace.config.setModuleUrl('ace/mode/pascal', require('file-loader?esModule=false!./src-noconflict/mode-pascal.js'));
ace.config.setModuleUrl('ace/mode/perl', require('file-loader?esModule=false!./src-noconflict/mode-perl.js'));
ace.config.setModuleUrl('ace/mode/pgsql', require('file-loader?esModule=false!./src-noconflict/mode-pgsql.js'));
ace.config.setModuleUrl('ace/mode/php', require('file-loader?esModule=false!./src-noconflict/mode-php.js'));
ace.config.setModuleUrl('ace/mode/php_laravel_blade', require('file-loader?esModule=false!./src-noconflict/mode-php_laravel_blade.js'));
ace.config.setModuleUrl('ace/mode/pig', require('file-loader?esModule=false!./src-noconflict/mode-pig.js'));
ace.config.setModuleUrl('ace/mode/plain_text', require('file-loader?esModule=false!./src-noconflict/mode-plain_text.js'));
ace.config.setModuleUrl('ace/mode/plsql', require('file-loader?esModule=false!./src-noconflict/mode-plsql.js'));
ace.config.setModuleUrl('ace/mode/powershell', require('file-loader?esModule=false!./src-noconflict/mode-powershell.js'));
ace.config.setModuleUrl('ace/mode/praat', require('file-loader?esModule=false!./src-noconflict/mode-praat.js'));
ace.config.setModuleUrl('ace/mode/prisma', require('file-loader?esModule=false!./src-noconflict/mode-prisma.js'));
ace.config.setModuleUrl('ace/mode/prolog', require('file-loader?esModule=false!./src-noconflict/mode-prolog.js'));
ace.config.setModuleUrl('ace/mode/properties', require('file-loader?esModule=false!./src-noconflict/mode-properties.js'));
ace.config.setModuleUrl('ace/mode/protobuf', require('file-loader?esModule=false!./src-noconflict/mode-protobuf.js'));
ace.config.setModuleUrl('ace/mode/prql', require('file-loader?esModule=false!./src-noconflict/mode-prql.js'));
ace.config.setModuleUrl('ace/mode/puppet', require('file-loader?esModule=false!./src-noconflict/mode-puppet.js'));
ace.config.setModuleUrl('ace/mode/python', require('file-loader?esModule=false!./src-noconflict/mode-python.js'));
ace.config.setModuleUrl('ace/mode/qml', require('file-loader?esModule=false!./src-noconflict/mode-qml.js'));
ace.config.setModuleUrl('ace/mode/r', require('file-loader?esModule=false!./src-noconflict/mode-r.js'));
ace.config.setModuleUrl('ace/mode/raku', require('file-loader?esModule=false!./src-noconflict/mode-raku.js'));
ace.config.setModuleUrl('ace/mode/razor', require('file-loader?esModule=false!./src-noconflict/mode-razor.js'));
ace.config.setModuleUrl('ace/mode/rdoc', require('file-loader?esModule=false!./src-noconflict/mode-rdoc.js'));
ace.config.setModuleUrl('ace/mode/red', require('file-loader?esModule=false!./src-noconflict/mode-red.js'));
ace.config.setModuleUrl('ace/mode/redshift', require('file-loader?esModule=false!./src-noconflict/mode-redshift.js'));
ace.config.setModuleUrl('ace/mode/rhtml', require('file-loader?esModule=false!./src-noconflict/mode-rhtml.js'));
ace.config.setModuleUrl('ace/mode/robot', require('file-loader?esModule=false!./src-noconflict/mode-robot.js'));
ace.config.setModuleUrl('ace/mode/rst', require('file-loader?esModule=false!./src-noconflict/mode-rst.js'));
ace.config.setModuleUrl('ace/mode/ruby', require('file-loader?esModule=false!./src-noconflict/mode-ruby.js'));
ace.config.setModuleUrl('ace/mode/rust', require('file-loader?esModule=false!./src-noconflict/mode-rust.js'));
ace.config.setModuleUrl('ace/mode/sac', require('file-loader?esModule=false!./src-noconflict/mode-sac.js'));
ace.config.setModuleUrl('ace/mode/sass', require('file-loader?esModule=false!./src-noconflict/mode-sass.js'));
ace.config.setModuleUrl('ace/mode/scad', require('file-loader?esModule=false!./src-noconflict/mode-scad.js'));
ace.config.setModuleUrl('ace/mode/scala', require('file-loader?esModule=false!./src-noconflict/mode-scala.js'));
ace.config.setModuleUrl('ace/mode/scheme', require('file-loader?esModule=false!./src-noconflict/mode-scheme.js'));
ace.config.setModuleUrl('ace/mode/scrypt', require('file-loader?esModule=false!./src-noconflict/mode-scrypt.js'));
ace.config.setModuleUrl('ace/mode/scss', require('file-loader?esModule=false!./src-noconflict/mode-scss.js'));
ace.config.setModuleUrl('ace/mode/sh', require('file-loader?esModule=false!./src-noconflict/mode-sh.js'));
ace.config.setModuleUrl('ace/mode/sjs', require('file-loader?esModule=false!./src-noconflict/mode-sjs.js'));
ace.config.setModuleUrl('ace/mode/slim', require('file-loader?esModule=false!./src-noconflict/mode-slim.js'));
ace.config.setModuleUrl('ace/mode/smarty', require('file-loader?esModule=false!./src-noconflict/mode-smarty.js'));
ace.config.setModuleUrl('ace/mode/smithy', require('file-loader?esModule=false!./src-noconflict/mode-smithy.js'));
ace.config.setModuleUrl('ace/mode/snippets', require('file-loader?esModule=false!./src-noconflict/mode-snippets.js'));
ace.config.setModuleUrl('ace/mode/soy_template', require('file-loader?esModule=false!./src-noconflict/mode-soy_template.js'));
ace.config.setModuleUrl('ace/mode/space', require('file-loader?esModule=false!./src-noconflict/mode-space.js'));
ace.config.setModuleUrl('ace/mode/sparql', require('file-loader?esModule=false!./src-noconflict/mode-sparql.js'));
ace.config.setModuleUrl('ace/mode/sql', require('file-loader?esModule=false!./src-noconflict/mode-sql.js'));
ace.config.setModuleUrl('ace/mode/sqlserver', require('file-loader?esModule=false!./src-noconflict/mode-sqlserver.js'));
ace.config.setModuleUrl('ace/mode/stylus', require('file-loader?esModule=false!./src-noconflict/mode-stylus.js'));
ace.config.setModuleUrl('ace/mode/svg', require('file-loader?esModule=false!./src-noconflict/mode-svg.js'));
ace.config.setModuleUrl('ace/mode/swift', require('file-loader?esModule=false!./src-noconflict/mode-swift.js'));
ace.config.setModuleUrl('ace/mode/tcl', require('file-loader?esModule=false!./src-noconflict/mode-tcl.js'));
ace.config.setModuleUrl('ace/mode/terraform', require('file-loader?esModule=false!./src-noconflict/mode-terraform.js'));
ace.config.setModuleUrl('ace/mode/tex', require('file-loader?esModule=false!./src-noconflict/mode-tex.js'));
ace.config.setModuleUrl('ace/mode/text', require('file-loader?esModule=false!./src-noconflict/mode-text.js'));
ace.config.setModuleUrl('ace/mode/textile', require('file-loader?esModule=false!./src-noconflict/mode-textile.js'));
ace.config.setModuleUrl('ace/mode/toml', require('file-loader?esModule=false!./src-noconflict/mode-toml.js'));
ace.config.setModuleUrl('ace/mode/tsv', require('file-loader?esModule=false!./src-noconflict/mode-tsv.js'));
ace.config.setModuleUrl('ace/mode/tsx', require('file-loader?esModule=false!./src-noconflict/mode-tsx.js'));
ace.config.setModuleUrl('ace/mode/turtle', require('file-loader?esModule=false!./src-noconflict/mode-turtle.js'));
ace.config.setModuleUrl('ace/mode/twig', require('file-loader?esModule=false!./src-noconflict/mode-twig.js'));
ace.config.setModuleUrl('ace/mode/typescript', require('file-loader?esModule=false!./src-noconflict/mode-typescript.js'));
ace.config.setModuleUrl('ace/mode/vala', require('file-loader?esModule=false!./src-noconflict/mode-vala.js'));
ace.config.setModuleUrl('ace/mode/vbscript', require('file-loader?esModule=false!./src-noconflict/mode-vbscript.js'));
ace.config.setModuleUrl('ace/mode/velocity', require('file-loader?esModule=false!./src-noconflict/mode-velocity.js'));
ace.config.setModuleUrl('ace/mode/verilog', require('file-loader?esModule=false!./src-noconflict/mode-verilog.js'));
ace.config.setModuleUrl('ace/mode/vhdl', require('file-loader?esModule=false!./src-noconflict/mode-vhdl.js'));
ace.config.setModuleUrl('ace/mode/visualforce', require('file-loader?esModule=false!./src-noconflict/mode-visualforce.js'));
ace.config.setModuleUrl('ace/mode/vue', require('file-loader?esModule=false!./src-noconflict/mode-vue.js'));
ace.config.setModuleUrl('ace/mode/wollok', require('file-loader?esModule=false!./src-noconflict/mode-wollok.js'));
ace.config.setModuleUrl('ace/mode/xml', require('file-loader?esModule=false!./src-noconflict/mode-xml.js'));
ace.config.setModuleUrl('ace/mode/xquery', require('file-loader?esModule=false!./src-noconflict/mode-xquery.js'));
ace.config.setModuleUrl('ace/mode/yaml', require('file-loader?esModule=false!./src-noconflict/mode-yaml.js'));
ace.config.setModuleUrl('ace/mode/zeek', require('file-loader?esModule=false!./src-noconflict/mode-zeek.js'));
ace.config.setModuleUrl('ace/mode/zig', require('file-loader?esModule=false!./src-noconflict/mode-zig.js'));

ace.config.setModuleUrl('ace/theme/ambiance', require('file-loader?esModule=false!./src-noconflict/theme-ambiance.js'));
ace.config.setModuleUrl('ace/theme/chaos', require('file-loader?esModule=false!./src-noconflict/theme-chaos.js'));
ace.config.setModuleUrl('ace/theme/chrome', require('file-loader?esModule=false!./src-noconflict/theme-chrome.js'));
ace.config.setModuleUrl('ace/theme/cloud9_day', require('file-loader?esModule=false!./src-noconflict/theme-cloud9_day.js'));
ace.config.setModuleUrl('ace/theme/cloud9_night', require('file-loader?esModule=false!./src-noconflict/theme-cloud9_night.js'));
ace.config.setModuleUrl('ace/theme/cloud9_night_low_color', require('file-loader?esModule=false!./src-noconflict/theme-cloud9_night_low_color.js'));
ace.config.setModuleUrl('ace/theme/cloud_editor', require('file-loader?esModule=false!./src-noconflict/theme-cloud_editor.js'));
ace.config.setModuleUrl('ace/theme/cloud_editor_dark', require('file-loader?esModule=false!./src-noconflict/theme-cloud_editor_dark.js'));
ace.config.setModuleUrl('ace/theme/clouds', require('file-loader?esModule=false!./src-noconflict/theme-clouds.js'));
ace.config.setModuleUrl('ace/theme/clouds_midnight', require('file-loader?esModule=false!./src-noconflict/theme-clouds_midnight.js'));
ace.config.setModuleUrl('ace/theme/cobalt', require('file-loader?esModule=false!./src-noconflict/theme-cobalt.js'));
ace.config.setModuleUrl('ace/theme/crimson_editor', require('file-loader?esModule=false!./src-noconflict/theme-crimson_editor.js'));
ace.config.setModuleUrl('ace/theme/dawn', require('file-loader?esModule=false!./src-noconflict/theme-dawn.js'));
ace.config.setModuleUrl('ace/theme/dracula', require('file-loader?esModule=false!./src-noconflict/theme-dracula.js'));
ace.config.setModuleUrl('ace/theme/dreamweaver', require('file-loader?esModule=false!./src-noconflict/theme-dreamweaver.js'));
ace.config.setModuleUrl('ace/theme/eclipse', require('file-loader?esModule=false!./src-noconflict/theme-eclipse.js'));
ace.config.setModuleUrl('ace/theme/github', require('file-loader?esModule=false!./src-noconflict/theme-github.js'));
ace.config.setModuleUrl('ace/theme/github_dark', require('file-loader?esModule=false!./src-noconflict/theme-github_dark.js'));
ace.config.setModuleUrl('ace/theme/github_light_default', require('file-loader?esModule=false!./src-noconflict/theme-github_light_default.js'));
ace.config.setModuleUrl('ace/theme/gob', require('file-loader?esModule=false!./src-noconflict/theme-gob.js'));
ace.config.setModuleUrl('ace/theme/gruvbox', require('file-loader?esModule=false!./src-noconflict/theme-gruvbox.js'));
ace.config.setModuleUrl('ace/theme/gruvbox_dark_hard', require('file-loader?esModule=false!./src-noconflict/theme-gruvbox_dark_hard.js'));
ace.config.setModuleUrl('ace/theme/gruvbox_light_hard', require('file-loader?esModule=false!./src-noconflict/theme-gruvbox_light_hard.js'));
ace.config.setModuleUrl('ace/theme/idle_fingers', require('file-loader?esModule=false!./src-noconflict/theme-idle_fingers.js'));
ace.config.setModuleUrl('ace/theme/iplastic', require('file-loader?esModule=false!./src-noconflict/theme-iplastic.js'));
ace.config.setModuleUrl('ace/theme/katzenmilch', require('file-loader?esModule=false!./src-noconflict/theme-katzenmilch.js'));
ace.config.setModuleUrl('ace/theme/kr_theme', require('file-loader?esModule=false!./src-noconflict/theme-kr_theme.js'));
ace.config.setModuleUrl('ace/theme/kuroir', require('file-loader?esModule=false!./src-noconflict/theme-kuroir.js'));
ace.config.setModuleUrl('ace/theme/merbivore', require('file-loader?esModule=false!./src-noconflict/theme-merbivore.js'));
ace.config.setModuleUrl('ace/theme/merbivore_soft', require('file-loader?esModule=false!./src-noconflict/theme-merbivore_soft.js'));
ace.config.setModuleUrl('ace/theme/mono_industrial', require('file-loader?esModule=false!./src-noconflict/theme-mono_industrial.js'));
ace.config.setModuleUrl('ace/theme/monokai', require('file-loader?esModule=false!./src-noconflict/theme-monokai.js'));
ace.config.setModuleUrl('ace/theme/nord_dark', require('file-loader?esModule=false!./src-noconflict/theme-nord_dark.js'));
ace.config.setModuleUrl('ace/theme/one_dark', require('file-loader?esModule=false!./src-noconflict/theme-one_dark.js'));
ace.config.setModuleUrl('ace/theme/pastel_on_dark', require('file-loader?esModule=false!./src-noconflict/theme-pastel_on_dark.js'));
ace.config.setModuleUrl('ace/theme/solarized_dark', require('file-loader?esModule=false!./src-noconflict/theme-solarized_dark.js'));
ace.config.setModuleUrl('ace/theme/solarized_light', require('file-loader?esModule=false!./src-noconflict/theme-solarized_light.js'));
ace.config.setModuleUrl('ace/theme/sqlserver', require('file-loader?esModule=false!./src-noconflict/theme-sqlserver.js'));
ace.config.setModuleUrl('ace/theme/terminal', require('file-loader?esModule=false!./src-noconflict/theme-terminal.js'));
ace.config.setModuleUrl('ace/theme/textmate', require('file-loader?esModule=false!./src-noconflict/theme-textmate.js'));
ace.config.setModuleUrl('ace/theme/tomorrow', require('file-loader?esModule=false!./src-noconflict/theme-tomorrow.js'));
ace.config.setModuleUrl('ace/theme/tomorrow_night', require('file-loader?esModule=false!./src-noconflict/theme-tomorrow_night.js'));
ace.config.setModuleUrl('ace/theme/tomorrow_night_blue', require('file-loader?esModule=false!./src-noconflict/theme-tomorrow_night_blue.js'));
ace.config.setModuleUrl('ace/theme/tomorrow_night_bright', require('file-loader?esModule=false!./src-noconflict/theme-tomorrow_night_bright.js'));
ace.config.setModuleUrl('ace/theme/tomorrow_night_eighties', require('file-loader?esModule=false!./src-noconflict/theme-tomorrow_night_eighties.js'));
ace.config.setModuleUrl('ace/theme/twilight', require('file-loader?esModule=false!./src-noconflict/theme-twilight.js'));
ace.config.setModuleUrl('ace/theme/vibrant_ink', require('file-loader?esModule=false!./src-noconflict/theme-vibrant_ink.js'));
ace.config.setModuleUrl('ace/theme/xcode', require('file-loader?esModule=false!./src-noconflict/theme-xcode.js'));
ace.config.setModuleUrl('ace/mode/base_worker', require('file-loader?esModule=false!./src-noconflict/worker-base.js'));
ace.config.setModuleUrl('ace/mode/coffee_worker', require('file-loader?esModule=false!./src-noconflict/worker-coffee.js'));
ace.config.setModuleUrl('ace/mode/css_worker', require('file-loader?esModule=false!./src-noconflict/worker-css.js'));
ace.config.setModuleUrl('ace/mode/html_worker', require('file-loader?esModule=false!./src-noconflict/worker-html.js'));
ace.config.setModuleUrl('ace/mode/javascript_worker', require('file-loader?esModule=false!./src-noconflict/worker-javascript.js'));
ace.config.setModuleUrl('ace/mode/json_worker', require('file-loader?esModule=false!./src-noconflict/worker-json.js'));
ace.config.setModuleUrl('ace/mode/lua_worker', require('file-loader?esModule=false!./src-noconflict/worker-lua.js'));
ace.config.setModuleUrl('ace/mode/php_worker', require('file-loader?esModule=false!./src-noconflict/worker-php.js'));
ace.config.setModuleUrl('ace/mode/xml_worker', require('file-loader?esModule=false!./src-noconflict/worker-xml.js'));
ace.config.setModuleUrl('ace/mode/xquery_worker', require('file-loader?esModule=false!./src-noconflict/worker-xquery.js'));
ace.config.setModuleUrl('ace/mode/yaml_worker', require('file-loader?esModule=false!./src-noconflict/worker-yaml.js'));
ace.config.setModuleUrl('ace/snippets/abap', require('file-loader?esModule=false!./src-noconflict/snippets/abap.js'));
ace.config.setModuleUrl('ace/snippets/abc', require('file-loader?esModule=false!./src-noconflict/snippets/abc.js'));
ace.config.setModuleUrl('ace/snippets/actionscript', require('file-loader?esModule=false!./src-noconflict/snippets/actionscript.js'));
ace.config.setModuleUrl('ace/snippets/ada', require('file-loader?esModule=false!./src-noconflict/snippets/ada.js'));
ace.config.setModuleUrl('ace/snippets/alda', require('file-loader?esModule=false!./src-noconflict/snippets/alda.js'));
ace.config.setModuleUrl('ace/snippets/apache_conf', require('file-loader?esModule=false!./src-noconflict/snippets/apache_conf.js'));
ace.config.setModuleUrl('ace/snippets/apex', require('file-loader?esModule=false!./src-noconflict/snippets/apex.js'));
ace.config.setModuleUrl('ace/snippets/applescript', require('file-loader?esModule=false!./src-noconflict/snippets/applescript.js'));
ace.config.setModuleUrl('ace/snippets/aql', require('file-loader?esModule=false!./src-noconflict/snippets/aql.js'));
ace.config.setModuleUrl('ace/snippets/asciidoc', require('file-loader?esModule=false!./src-noconflict/snippets/asciidoc.js'));
ace.config.setModuleUrl('ace/snippets/asl', require('file-loader?esModule=false!./src-noconflict/snippets/asl.js'));
ace.config.setModuleUrl('ace/snippets/assembly_arm32', require('file-loader?esModule=false!./src-noconflict/snippets/assembly_arm32.js'));
ace.config.setModuleUrl('ace/snippets/assembly_x86', require('file-loader?esModule=false!./src-noconflict/snippets/assembly_x86.js'));
ace.config.setModuleUrl('ace/snippets/astro', require('file-loader?esModule=false!./src-noconflict/snippets/astro.js'));
ace.config.setModuleUrl('ace/snippets/autohotkey', require('file-loader?esModule=false!./src-noconflict/snippets/autohotkey.js'));
ace.config.setModuleUrl('ace/snippets/basic', require('file-loader?esModule=false!./src-noconflict/snippets/basic.js'));
ace.config.setModuleUrl('ace/snippets/batchfile', require('file-loader?esModule=false!./src-noconflict/snippets/batchfile.js'));
ace.config.setModuleUrl('ace/snippets/bibtex', require('file-loader?esModule=false!./src-noconflict/snippets/bibtex.js'));
ace.config.setModuleUrl('ace/snippets/c9search', require('file-loader?esModule=false!./src-noconflict/snippets/c9search.js'));
ace.config.setModuleUrl('ace/snippets/c_cpp', require('file-loader?esModule=false!./src-noconflict/snippets/c_cpp.js'));
ace.config.setModuleUrl('ace/snippets/cirru', require('file-loader?esModule=false!./src-noconflict/snippets/cirru.js'));
ace.config.setModuleUrl('ace/snippets/clojure', require('file-loader?esModule=false!./src-noconflict/snippets/clojure.js'));
ace.config.setModuleUrl('ace/snippets/cobol', require('file-loader?esModule=false!./src-noconflict/snippets/cobol.js'));
ace.config.setModuleUrl('ace/snippets/coffee', require('file-loader?esModule=false!./src-noconflict/snippets/coffee.js'));
ace.config.setModuleUrl('ace/snippets/coldfusion', require('file-loader?esModule=false!./src-noconflict/snippets/coldfusion.js'));
ace.config.setModuleUrl('ace/snippets/crystal', require('file-loader?esModule=false!./src-noconflict/snippets/crystal.js'));
ace.config.setModuleUrl('ace/snippets/csharp', require('file-loader?esModule=false!./src-noconflict/snippets/csharp.js'));
ace.config.setModuleUrl('ace/snippets/csound_document', require('file-loader?esModule=false!./src-noconflict/snippets/csound_document.js'));
ace.config.setModuleUrl('ace/snippets/csound_orchestra', require('file-loader?esModule=false!./src-noconflict/snippets/csound_orchestra.js'));
ace.config.setModuleUrl('ace/snippets/csound_score', require('file-loader?esModule=false!./src-noconflict/snippets/csound_score.js'));
ace.config.setModuleUrl('ace/snippets/csp', require('file-loader?esModule=false!./src-noconflict/snippets/csp.js'));
ace.config.setModuleUrl('ace/snippets/css', require('file-loader?esModule=false!./src-noconflict/snippets/css.js'));
ace.config.setModuleUrl('ace/snippets/csv', require('file-loader?esModule=false!./src-noconflict/snippets/csv.js'));
ace.config.setModuleUrl('ace/snippets/curly', require('file-loader?esModule=false!./src-noconflict/snippets/curly.js'));
ace.config.setModuleUrl('ace/snippets/cuttlefish', require('file-loader?esModule=false!./src-noconflict/snippets/cuttlefish.js'));
ace.config.setModuleUrl('ace/snippets/d', require('file-loader?esModule=false!./src-noconflict/snippets/d.js'));
ace.config.setModuleUrl('ace/snippets/dart', require('file-loader?esModule=false!./src-noconflict/snippets/dart.js'));
ace.config.setModuleUrl('ace/snippets/diff', require('file-loader?esModule=false!./src-noconflict/snippets/diff.js'));
ace.config.setModuleUrl('ace/snippets/django', require('file-loader?esModule=false!./src-noconflict/snippets/django.js'));
ace.config.setModuleUrl('ace/snippets/dockerfile', require('file-loader?esModule=false!./src-noconflict/snippets/dockerfile.js'));
ace.config.setModuleUrl('ace/snippets/dot', require('file-loader?esModule=false!./src-noconflict/snippets/dot.js'));
ace.config.setModuleUrl('ace/snippets/drools', require('file-loader?esModule=false!./src-noconflict/snippets/drools.js'));
ace.config.setModuleUrl('ace/snippets/edifact', require('file-loader?esModule=false!./src-noconflict/snippets/edifact.js'));
ace.config.setModuleUrl('ace/snippets/eiffel', require('file-loader?esModule=false!./src-noconflict/snippets/eiffel.js'));
ace.config.setModuleUrl('ace/snippets/ejs', require('file-loader?esModule=false!./src-noconflict/snippets/ejs.js'));
ace.config.setModuleUrl('ace/snippets/elixir', require('file-loader?esModule=false!./src-noconflict/snippets/elixir.js'));
ace.config.setModuleUrl('ace/snippets/elm', require('file-loader?esModule=false!./src-noconflict/snippets/elm.js'));
ace.config.setModuleUrl('ace/snippets/erlang', require('file-loader?esModule=false!./src-noconflict/snippets/erlang.js'));
ace.config.setModuleUrl('ace/snippets/flix', require('file-loader?esModule=false!./src-noconflict/snippets/flix.js'));
ace.config.setModuleUrl('ace/snippets/forth', require('file-loader?esModule=false!./src-noconflict/snippets/forth.js'));
ace.config.setModuleUrl('ace/snippets/fortran', require('file-loader?esModule=false!./src-noconflict/snippets/fortran.js'));
ace.config.setModuleUrl('ace/snippets/fsharp', require('file-loader?esModule=false!./src-noconflict/snippets/fsharp.js'));
ace.config.setModuleUrl('ace/snippets/fsl', require('file-loader?esModule=false!./src-noconflict/snippets/fsl.js'));
ace.config.setModuleUrl('ace/snippets/ftl', require('file-loader?esModule=false!./src-noconflict/snippets/ftl.js'));
ace.config.setModuleUrl('ace/snippets/gcode', require('file-loader?esModule=false!./src-noconflict/snippets/gcode.js'));
ace.config.setModuleUrl('ace/snippets/gherkin', require('file-loader?esModule=false!./src-noconflict/snippets/gherkin.js'));
ace.config.setModuleUrl('ace/snippets/gitignore', require('file-loader?esModule=false!./src-noconflict/snippets/gitignore.js'));
ace.config.setModuleUrl('ace/snippets/glsl', require('file-loader?esModule=false!./src-noconflict/snippets/glsl.js'));
ace.config.setModuleUrl('ace/snippets/gobstones', require('file-loader?esModule=false!./src-noconflict/snippets/gobstones.js'));
ace.config.setModuleUrl('ace/snippets/golang', require('file-loader?esModule=false!./src-noconflict/snippets/golang.js'));
ace.config.setModuleUrl('ace/snippets/graphqlschema', require('file-loader?esModule=false!./src-noconflict/snippets/graphqlschema.js'));
ace.config.setModuleUrl('ace/snippets/groovy', require('file-loader?esModule=false!./src-noconflict/snippets/groovy.js'));
ace.config.setModuleUrl('ace/snippets/haml', require('file-loader?esModule=false!./src-noconflict/snippets/haml.js'));
ace.config.setModuleUrl('ace/snippets/handlebars', require('file-loader?esModule=false!./src-noconflict/snippets/handlebars.js'));
ace.config.setModuleUrl('ace/snippets/haskell', require('file-loader?esModule=false!./src-noconflict/snippets/haskell.js'));
ace.config.setModuleUrl('ace/snippets/haskell_cabal', require('file-loader?esModule=false!./src-noconflict/snippets/haskell_cabal.js'));
ace.config.setModuleUrl('ace/snippets/haxe', require('file-loader?esModule=false!./src-noconflict/snippets/haxe.js'));
ace.config.setModuleUrl('ace/snippets/hjson', require('file-loader?esModule=false!./src-noconflict/snippets/hjson.js'));
ace.config.setModuleUrl('ace/snippets/html', require('file-loader?esModule=false!./src-noconflict/snippets/html.js'));
ace.config.setModuleUrl('ace/snippets/html_elixir', require('file-loader?esModule=false!./src-noconflict/snippets/html_elixir.js'));
ace.config.setModuleUrl('ace/snippets/html_ruby', require('file-loader?esModule=false!./src-noconflict/snippets/html_ruby.js'));
ace.config.setModuleUrl('ace/snippets/ini', require('file-loader?esModule=false!./src-noconflict/snippets/ini.js'));
ace.config.setModuleUrl('ace/snippets/io', require('file-loader?esModule=false!./src-noconflict/snippets/io.js'));
ace.config.setModuleUrl('ace/snippets/ion', require('file-loader?esModule=false!./src-noconflict/snippets/ion.js'));
ace.config.setModuleUrl('ace/snippets/jack', require('file-loader?esModule=false!./src-noconflict/snippets/jack.js'));
ace.config.setModuleUrl('ace/snippets/jade', require('file-loader?esModule=false!./src-noconflict/snippets/jade.js'));
ace.config.setModuleUrl('ace/snippets/java', require('file-loader?esModule=false!./src-noconflict/snippets/java.js'));
ace.config.setModuleUrl('ace/snippets/javascript', require('file-loader?esModule=false!./src-noconflict/snippets/javascript.js'));
ace.config.setModuleUrl('ace/snippets/jexl', require('file-loader?esModule=false!./src-noconflict/snippets/jexl.js'));
ace.config.setModuleUrl('ace/snippets/json', require('file-loader?esModule=false!./src-noconflict/snippets/json.js'));
ace.config.setModuleUrl('ace/snippets/json5', require('file-loader?esModule=false!./src-noconflict/snippets/json5.js'));
ace.config.setModuleUrl('ace/snippets/jsoniq', require('file-loader?esModule=false!./src-noconflict/snippets/jsoniq.js'));
ace.config.setModuleUrl('ace/snippets/jsp', require('file-loader?esModule=false!./src-noconflict/snippets/jsp.js'));
ace.config.setModuleUrl('ace/snippets/jssm', require('file-loader?esModule=false!./src-noconflict/snippets/jssm.js'));
ace.config.setModuleUrl('ace/snippets/jsx', require('file-loader?esModule=false!./src-noconflict/snippets/jsx.js'));
ace.config.setModuleUrl('ace/snippets/julia', require('file-loader?esModule=false!./src-noconflict/snippets/julia.js'));
ace.config.setModuleUrl('ace/snippets/kotlin', require('file-loader?esModule=false!./src-noconflict/snippets/kotlin.js'));
ace.config.setModuleUrl('ace/snippets/latex', require('file-loader?esModule=false!./src-noconflict/snippets/latex.js'));
ace.config.setModuleUrl('ace/snippets/latte', require('file-loader?esModule=false!./src-noconflict/snippets/latte.js'));
ace.config.setModuleUrl('ace/snippets/less', require('file-loader?esModule=false!./src-noconflict/snippets/less.js'));
ace.config.setModuleUrl('ace/snippets/liquid', require('file-loader?esModule=false!./src-noconflict/snippets/liquid.js'));
ace.config.setModuleUrl('ace/snippets/lisp', require('file-loader?esModule=false!./src-noconflict/snippets/lisp.js'));
ace.config.setModuleUrl('ace/snippets/livescript', require('file-loader?esModule=false!./src-noconflict/snippets/livescript.js'));
ace.config.setModuleUrl('ace/snippets/logiql', require('file-loader?esModule=false!./src-noconflict/snippets/logiql.js'));
ace.config.setModuleUrl('ace/snippets/logtalk', require('file-loader?esModule=false!./src-noconflict/snippets/logtalk.js'));
ace.config.setModuleUrl('ace/snippets/lsl', require('file-loader?esModule=false!./src-noconflict/snippets/lsl.js'));
ace.config.setModuleUrl('ace/snippets/lua', require('file-loader?esModule=false!./src-noconflict/snippets/lua.js'));
ace.config.setModuleUrl('ace/snippets/luapage', require('file-loader?esModule=false!./src-noconflict/snippets/luapage.js'));
ace.config.setModuleUrl('ace/snippets/lucene', require('file-loader?esModule=false!./src-noconflict/snippets/lucene.js'));
ace.config.setModuleUrl('ace/snippets/makefile', require('file-loader?esModule=false!./src-noconflict/snippets/makefile.js'));
ace.config.setModuleUrl('ace/snippets/markdown', require('file-loader?esModule=false!./src-noconflict/snippets/markdown.js'));
ace.config.setModuleUrl('ace/snippets/mask', require('file-loader?esModule=false!./src-noconflict/snippets/mask.js'));
ace.config.setModuleUrl('ace/snippets/matlab', require('file-loader?esModule=false!./src-noconflict/snippets/matlab.js'));
ace.config.setModuleUrl('ace/snippets/maze', require('file-loader?esModule=false!./src-noconflict/snippets/maze.js'));
ace.config.setModuleUrl('ace/snippets/mediawiki', require('file-loader?esModule=false!./src-noconflict/snippets/mediawiki.js'));
ace.config.setModuleUrl('ace/snippets/mel', require('file-loader?esModule=false!./src-noconflict/snippets/mel.js'));
ace.config.setModuleUrl('ace/snippets/mips', require('file-loader?esModule=false!./src-noconflict/snippets/mips.js'));
ace.config.setModuleUrl('ace/snippets/mixal', require('file-loader?esModule=false!./src-noconflict/snippets/mixal.js'));
ace.config.setModuleUrl('ace/snippets/mushcode', require('file-loader?esModule=false!./src-noconflict/snippets/mushcode.js'));
ace.config.setModuleUrl('ace/snippets/mysql', require('file-loader?esModule=false!./src-noconflict/snippets/mysql.js'));
ace.config.setModuleUrl('ace/snippets/nasal', require('file-loader?esModule=false!./src-noconflict/snippets/nasal.js'));
ace.config.setModuleUrl('ace/snippets/nginx', require('file-loader?esModule=false!./src-noconflict/snippets/nginx.js'));
ace.config.setModuleUrl('ace/snippets/nim', require('file-loader?esModule=false!./src-noconflict/snippets/nim.js'));
ace.config.setModuleUrl('ace/snippets/nix', require('file-loader?esModule=false!./src-noconflict/snippets/nix.js'));
ace.config.setModuleUrl('ace/snippets/nsis', require('file-loader?esModule=false!./src-noconflict/snippets/nsis.js'));
ace.config.setModuleUrl('ace/snippets/nunjucks', require('file-loader?esModule=false!./src-noconflict/snippets/nunjucks.js'));
ace.config.setModuleUrl('ace/snippets/objectivec', require('file-loader?esModule=false!./src-noconflict/snippets/objectivec.js'));
ace.config.setModuleUrl('ace/snippets/ocaml', require('file-loader?esModule=false!./src-noconflict/snippets/ocaml.js'));
ace.config.setModuleUrl('ace/snippets/odin', require('file-loader?esModule=false!./src-noconflict/snippets/odin.js'));
ace.config.setModuleUrl('ace/snippets/partiql', require('file-loader?esModule=false!./src-noconflict/snippets/partiql.js'));
ace.config.setModuleUrl('ace/snippets/pascal', require('file-loader?esModule=false!./src-noconflict/snippets/pascal.js'));
ace.config.setModuleUrl('ace/snippets/perl', require('file-loader?esModule=false!./src-noconflict/snippets/perl.js'));
ace.config.setModuleUrl('ace/snippets/pgsql', require('file-loader?esModule=false!./src-noconflict/snippets/pgsql.js'));
ace.config.setModuleUrl('ace/snippets/php', require('file-loader?esModule=false!./src-noconflict/snippets/php.js'));
ace.config.setModuleUrl('ace/snippets/php_laravel_blade', require('file-loader?esModule=false!./src-noconflict/snippets/php_laravel_blade.js'));
ace.config.setModuleUrl('ace/snippets/pig', require('file-loader?esModule=false!./src-noconflict/snippets/pig.js'));
ace.config.setModuleUrl('ace/snippets/plain_text', require('file-loader?esModule=false!./src-noconflict/snippets/plain_text.js'));
ace.config.setModuleUrl('ace/snippets/plsql', require('file-loader?esModule=false!./src-noconflict/snippets/plsql.js'));
ace.config.setModuleUrl('ace/snippets/powershell', require('file-loader?esModule=false!./src-noconflict/snippets/powershell.js'));
ace.config.setModuleUrl('ace/snippets/praat', require('file-loader?esModule=false!./src-noconflict/snippets/praat.js'));
ace.config.setModuleUrl('ace/snippets/prisma', require('file-loader?esModule=false!./src-noconflict/snippets/prisma.js'));
ace.config.setModuleUrl('ace/snippets/prolog', require('file-loader?esModule=false!./src-noconflict/snippets/prolog.js'));
ace.config.setModuleUrl('ace/snippets/properties', require('file-loader?esModule=false!./src-noconflict/snippets/properties.js'));
ace.config.setModuleUrl('ace/snippets/protobuf', require('file-loader?esModule=false!./src-noconflict/snippets/protobuf.js'));
ace.config.setModuleUrl('ace/snippets/prql', require('file-loader?esModule=false!./src-noconflict/snippets/prql.js'));
ace.config.setModuleUrl('ace/snippets/puppet', require('file-loader?esModule=false!./src-noconflict/snippets/puppet.js'));
ace.config.setModuleUrl('ace/snippets/python', require('file-loader?esModule=false!./src-noconflict/snippets/python.js'));
ace.config.setModuleUrl('ace/snippets/qml', require('file-loader?esModule=false!./src-noconflict/snippets/qml.js'));
ace.config.setModuleUrl('ace/snippets/r', require('file-loader?esModule=false!./src-noconflict/snippets/r.js'));
ace.config.setModuleUrl('ace/snippets/raku', require('file-loader?esModule=false!./src-noconflict/snippets/raku.js'));
ace.config.setModuleUrl('ace/snippets/razor', require('file-loader?esModule=false!./src-noconflict/snippets/razor.js'));
ace.config.setModuleUrl('ace/snippets/rdoc', require('file-loader?esModule=false!./src-noconflict/snippets/rdoc.js'));
ace.config.setModuleUrl('ace/snippets/red', require('file-loader?esModule=false!./src-noconflict/snippets/red.js'));
ace.config.setModuleUrl('ace/snippets/redshift', require('file-loader?esModule=false!./src-noconflict/snippets/redshift.js'));
ace.config.setModuleUrl('ace/snippets/rhtml', require('file-loader?esModule=false!./src-noconflict/snippets/rhtml.js'));
ace.config.setModuleUrl('ace/snippets/robot', require('file-loader?esModule=false!./src-noconflict/snippets/robot.js'));
ace.config.setModuleUrl('ace/snippets/rst', require('file-loader?esModule=false!./src-noconflict/snippets/rst.js'));
ace.config.setModuleUrl('ace/snippets/ruby', require('file-loader?esModule=false!./src-noconflict/snippets/ruby.js'));
ace.config.setModuleUrl('ace/snippets/rust', require('file-loader?esModule=false!./src-noconflict/snippets/rust.js'));
ace.config.setModuleUrl('ace/snippets/sac', require('file-loader?esModule=false!./src-noconflict/snippets/sac.js'));
ace.config.setModuleUrl('ace/snippets/sass', require('file-loader?esModule=false!./src-noconflict/snippets/sass.js'));
ace.config.setModuleUrl('ace/snippets/scad', require('file-loader?esModule=false!./src-noconflict/snippets/scad.js'));
ace.config.setModuleUrl('ace/snippets/scala', require('file-loader?esModule=false!./src-noconflict/snippets/scala.js'));
ace.config.setModuleUrl('ace/snippets/scheme', require('file-loader?esModule=false!./src-noconflict/snippets/scheme.js'));
ace.config.setModuleUrl('ace/snippets/scrypt', require('file-loader?esModule=false!./src-noconflict/snippets/scrypt.js'));
ace.config.setModuleUrl('ace/snippets/scss', require('file-loader?esModule=false!./src-noconflict/snippets/scss.js'));
ace.config.setModuleUrl('ace/snippets/sh', require('file-loader?esModule=false!./src-noconflict/snippets/sh.js'));
ace.config.setModuleUrl('ace/snippets/sjs', require('file-loader?esModule=false!./src-noconflict/snippets/sjs.js'));
ace.config.setModuleUrl('ace/snippets/slim', require('file-loader?esModule=false!./src-noconflict/snippets/slim.js'));
ace.config.setModuleUrl('ace/snippets/smarty', require('file-loader?esModule=false!./src-noconflict/snippets/smarty.js'));
ace.config.setModuleUrl('ace/snippets/smithy', require('file-loader?esModule=false!./src-noconflict/snippets/smithy.js'));
ace.config.setModuleUrl('ace/snippets/snippets', require('file-loader?esModule=false!./src-noconflict/snippets/snippets.js'));
ace.config.setModuleUrl('ace/snippets/soy_template', require('file-loader?esModule=false!./src-noconflict/snippets/soy_template.js'));
ace.config.setModuleUrl('ace/snippets/space', require('file-loader?esModule=false!./src-noconflict/snippets/space.js'));
ace.config.setModuleUrl('ace/snippets/sparql', require('file-loader?esModule=false!./src-noconflict/snippets/sparql.js'));
ace.config.setModuleUrl('ace/snippets/sql', require('file-loader?esModule=false!./src-noconflict/snippets/sql.js'));
ace.config.setModuleUrl('ace/snippets/sqlserver', require('file-loader?esModule=false!./src-noconflict/snippets/sqlserver.js'));
ace.config.setModuleUrl('ace/snippets/stylus', require('file-loader?esModule=false!./src-noconflict/snippets/stylus.js'));
ace.config.setModuleUrl('ace/snippets/svg', require('file-loader?esModule=false!./src-noconflict/snippets/svg.js'));
ace.config.setModuleUrl('ace/snippets/swift', require('file-loader?esModule=false!./src-noconflict/snippets/swift.js'));
ace.config.setModuleUrl('ace/snippets/tcl', require('file-loader?esModule=false!./src-noconflict/snippets/tcl.js'));
ace.config.setModuleUrl('ace/snippets/terraform', require('file-loader?esModule=false!./src-noconflict/snippets/terraform.js'));
ace.config.setModuleUrl('ace/snippets/tex', require('file-loader?esModule=false!./src-noconflict/snippets/tex.js'));
ace.config.setModuleUrl('ace/snippets/text', require('file-loader?esModule=false!./src-noconflict/snippets/text.js'));
ace.config.setModuleUrl('ace/snippets/textile', require('file-loader?esModule=false!./src-noconflict/snippets/textile.js'));
ace.config.setModuleUrl('ace/snippets/toml', require('file-loader?esModule=false!./src-noconflict/snippets/toml.js'));
ace.config.setModuleUrl('ace/snippets/tsv', require('file-loader?esModule=false!./src-noconflict/snippets/tsv.js'));
ace.config.setModuleUrl('ace/snippets/tsx', require('file-loader?esModule=false!./src-noconflict/snippets/tsx.js'));
ace.config.setModuleUrl('ace/snippets/turtle', require('file-loader?esModule=false!./src-noconflict/snippets/turtle.js'));
ace.config.setModuleUrl('ace/snippets/twig', require('file-loader?esModule=false!./src-noconflict/snippets/twig.js'));
ace.config.setModuleUrl('ace/snippets/typescript', require('file-loader?esModule=false!./src-noconflict/snippets/typescript.js'));
ace.config.setModuleUrl('ace/snippets/vala', require('file-loader?esModule=false!./src-noconflict/snippets/vala.js'));
ace.config.setModuleUrl('ace/snippets/vbscript', require('file-loader?esModule=false!./src-noconflict/snippets/vbscript.js'));
ace.config.setModuleUrl('ace/snippets/velocity', require('file-loader?esModule=false!./src-noconflict/snippets/velocity.js'));
ace.config.setModuleUrl('ace/snippets/verilog', require('file-loader?esModule=false!./src-noconflict/snippets/verilog.js'));
ace.config.setModuleUrl('ace/snippets/vhdl', require('file-loader?esModule=false!./src-noconflict/snippets/vhdl.js'));
ace.config.setModuleUrl('ace/snippets/visualforce', require('file-loader?esModule=false!./src-noconflict/snippets/visualforce.js'));
ace.config.setModuleUrl('ace/snippets/vue', require('file-loader?esModule=false!./src-noconflict/snippets/vue.js'));
ace.config.setModuleUrl('ace/snippets/wollok', require('file-loader?esModule=false!./src-noconflict/snippets/wollok.js'));
ace.config.setModuleUrl('ace/snippets/xml', require('file-loader?esModule=false!./src-noconflict/snippets/xml.js'));
ace.config.setModuleUrl('ace/snippets/xquery', require('file-loader?esModule=false!./src-noconflict/snippets/xquery.js'));
ace.config.setModuleUrl('ace/snippets/yaml', require('file-loader?esModule=false!./src-noconflict/snippets/yaml.js'));
ace.config.setModuleUrl('ace/snippets/zeek', require('file-loader?esModule=false!./src-noconflict/snippets/zeek.js'));
ace.config.setModuleUrl('ace/snippets/zig', require('file-loader?esModule=false!./src-noconflict/snippets/zig.js'));