declare module "ace-builds/src-noconflict/mode-abap_highlight_rules" {
    export const AbapHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-abap" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-abc_highlight_rules" {
    export const ABCHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-abc" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-actionscript_highlight_rules" {
    export const ActionScriptHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-actionscript" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-ada_highlight_rules" {
    export const AdaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-ada" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-alda_highlight_rules" {
    export const AldaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-alda" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-apache_conf_highlight_rules" {
    export const ApacheConfHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-apache_conf" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-apex_highlight_rules" {
    export const ApexHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-apex" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-applescript_highlight_rules" {
    export const AppleScriptHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-applescript" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-aql_highlight_rules" {
    export const AqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-aql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-asciidoc_highlight_rules" {
    export const AsciidocHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-asciidoc" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-asl_highlight_rules" {
    export const ASLHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-asl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-assembly_arm32_highlight_rules" {
    export const AssemblyARM32HighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-assembly_arm32" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-assembly_x86_highlight_rules" {
    export const AssemblyX86HighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-assembly_x86" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-astro_highlight_rules" {
    export const AstroHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-astro" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-autohotkey_highlight_rules" {
    export const AutoHotKeyHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-autohotkey" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-basic_highlight_rules" {
    export const BasicHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-basic" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-batchfile_highlight_rules" {
    export const BatchFileHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-batchfile" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds-internal/mode/behaviour" {
    export const Behaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds-internal/mode/behaviour/css" {
    export const CssBehaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds-internal/mode/behaviour/cstyle" {
    export const CstyleBehaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds-internal/mode/behaviour/html" {
    export const HtmlBehaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds-internal/mode/behaviour/javascript" {
    export const JavaScriptBehaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds-internal/mode/behaviour/liquid" {
    export const LiquidBehaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds-internal/mode/behaviour/xml" {
    export const XmlBehaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds-internal/mode/behaviour/xquery" {
    export const XQueryBehaviour: new () => import("ace-builds").Ace.Behaviour;
}

declare module "ace-builds/src-noconflict/mode-bibtex_highlight_rules" {
    export const BibTeXHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-bibtex" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-c_cpp_highlight_rules" {
    export const cFunctions: string;
    export const c_cppHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-c_cpp" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-c9search_highlight_rules" {
    export const C9SearchHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-c9search" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-cirru_highlight_rules" {
    export const CirruHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-cirru" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-clojure_highlight_rules" {
    export const ClojureHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-clojure" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-cobol_highlight_rules" {
    export const CobolHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-cobol" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-coffee_highlight_rules" {
    export const CoffeeHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-coffee" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-coldfusion_highlight_rules" {
    export const ColdfusionHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-coldfusion" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-crystal_highlight_rules" {
    export const CrystalHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-crystal" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-csharp_highlight_rules" {
    export const CSharpHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-csharp" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-csound_document_highlight_rules" {
    export const CsoundDocumentHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-csound_document" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-csound_orchestra_highlight_rules" {
    export const CsoundOrchestraHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-csound_orchestra" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-csound_preprocessor_highlight_rules" {
    export const CsoundPreprocessorHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-csound_score_highlight_rules" {
    export const CsoundScoreHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-csound_score" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-csp_highlight_rules" {
    export const CspHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-csp" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-css_completions" {
    export const CssCompletions: new () => import("ace-builds").Ace.Completion;
}

declare module "ace-builds/src-noconflict/mode-css_highlight_rules" {
    export const supportType: string;
    export const supportFunction: string;
    export const supportConstant: string;
    export const supportConstantColor: string;
    export const supportConstantFonts: string;
    export const numRe: string;
    export const pseudoElements: string;
    export const pseudoClasses: string;
    export const CssHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-css" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-csv_highlight_rules" {
    export const CsvHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-csv" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-curly_highlight_rules" {
    export const CurlyHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-curly" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-cuttlefish_highlight_rules" {
    export const CuttlefishHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-cuttlefish" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-d_highlight_rules" {
    export const DHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-d" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-dart_highlight_rules" {
    export const DartHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-dart" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-diff_highlight_rules" {
    export const DiffHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-diff" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-django" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-doc_comment_highlight_rules" {
    export const DocCommentHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-dockerfile_highlight_rules" {
    export const DockerfileHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-dockerfile" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-dot_highlight_rules" {
    export const DotHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-dot" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-drools_highlight_rules" {
    export const DroolsHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-drools" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-edifact_highlight_rules" {
    export const EdifactHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-edifact" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-eiffel_highlight_rules" {
    export const EiffelHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-eiffel" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-ejs" {
    export const EjsHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-elixir_highlight_rules" {
    export const ElixirHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-elixir" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-elm_highlight_rules" {
    export const ElmHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-elm" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-erlang_highlight_rules" {
    export const ErlangHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-erlang" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-flix_highlight_rules" {
    export const FlixHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-flix" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds-internal/mode/folding/asciidoc" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/basic" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/c9search" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/coffee" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/csharp" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/cstyle" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/diff" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/drools" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/fold_mode" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/haskell_cabal" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/html" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/ini" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/java" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/javascript" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/latex" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/lua" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/markdown" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/mixed" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/php" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/pythonic" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/ruby" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/sql" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/sqlserver" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/vbscript" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/velocity" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/xml" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds-internal/mode/folding/yaml" {
    export const FoldMode: new () => import("ace-builds").Ace.Folding;
}

declare module "ace-builds/src-noconflict/mode-forth_highlight_rules" {
    export const ForthHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-forth" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-fortran_highlight_rules" {
    export const FortranHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-fortran" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-fsharp_highlight_rules" {
    export const FSharpHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-fsharp" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-fsl_highlight_rules" {
    export const FSLHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-fsl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-ftl_highlight_rules" {
    export const FtlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-ftl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-gcode_highlight_rules" {
    export const GcodeHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-gcode" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-gherkin_highlight_rules" {
    export const GherkinHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-gherkin" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-gitignore_highlight_rules" {
    export const GitignoreHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-gitignore" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-glsl_highlight_rules" {
    export const glslHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-glsl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-gobstones_highlight_rules" {
    export const GobstonesHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-gobstones" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-golang_highlight_rules" {
    export const GolangHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-golang" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-graphqlschema_highlight_rules" {
    export const GraphQLSchemaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-graphqlschema" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-groovy_highlight_rules" {
    export const GroovyHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-groovy" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-haml_highlight_rules" {
    export const HamlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-haml" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-handlebars_highlight_rules" {
    export const HandlebarsHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-handlebars" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-haskell_cabal_highlight_rules" {
    export const CabalHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-haskell_cabal" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-haskell_highlight_rules" {
    export const HaskellHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-haskell" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-haxe_highlight_rules" {
    export const HaxeHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-haxe" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-hjson_highlight_rules" {
    export const HjsonHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-hjson" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-html_completions" {
    export const HtmlCompletions: new () => import("ace-builds").Ace.Completion;
}

declare module "ace-builds/src-noconflict/mode-html_elixir_highlight_rules" {
    export const HtmlElixirHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-html_elixir" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-html_highlight_rules" {
    export const HtmlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-html_ruby_highlight_rules" {
    export const HtmlRubyHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-html_ruby" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-html" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-ini_highlight_rules" {
    export const IniHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-ini" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-io_highlight_rules" {
    export const IoHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-io" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-ion_highlight_rules" {
    export const IonHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-ion" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-jack_highlight_rules" {
    export const JackHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-jack" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-jade_highlight_rules" {
    export const JadeHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-jade" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-java_highlight_rules" {
    export const JavaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-java" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-javascript_highlight_rules" {
    export const JavaScriptHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-javascript" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-jexl_highlight_rules" {
    export const JexlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-jexl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-js_regex_highlight_rules" {
    export const JsRegexHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-jsdoc_comment_highlight_rules" {
    export const JsDocCommentHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-json_highlight_rules" {
    export const JsonHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-json" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-json5_highlight_rules" {
    export const Json5HighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-json5" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-jsp_highlight_rules" {
    export const JspHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-jsp" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-jssm_highlight_rules" {
    export const JSSMHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-jssm" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-jsx_highlight_rules" {
    export const JsxHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-jsx" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-julia_highlight_rules" {
    export const JuliaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-julia" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-kotlin_highlight_rules" {
    export const KotlinHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-kotlin" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-latex_highlight_rules" {
    export const LatexHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-latex" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-latte_highlight_rules" {
    export const LatteHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-latte" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-less_highlight_rules" {
    export const LessHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-less" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-liquid_highlight_rules" {
    export const LiquidHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-liquid" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-lisp_highlight_rules" {
    export const LispHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-lisp" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-livescript" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-logiql_highlight_rules" {
    export const LogiQLHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-logiql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-logtalk_highlight_rules" {
    export const LogtalkHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-logtalk" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-lsl_highlight_rules" {
    export const LSLHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-lsl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-lua_highlight_rules" {
    export const LuaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-lua" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-luapage_highlight_rules" {
    export const LuaPageHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-luapage" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-lucene_highlight_rules" {
    export const LuceneHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-lucene" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-makefile_highlight_rules" {
    export const MakefileHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-makefile" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-markdown_highlight_rules" {
    export const MarkdownHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-markdown" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-mask_highlight_rules" {
    export const MaskHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-mask" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds-internal/mode/matching_brace_outdent" {
    export const MatchingBraceOutdent: new () => import("ace-builds").Ace.Outdent;
}

declare module "ace-builds-internal/mode/matching_parens_outdent" {
    export const MatchingParensOutdent: new () => import("ace-builds").Ace.Outdent;
}

declare module "ace-builds/src-noconflict/mode-matlab_highlight_rules" {
    export const MatlabHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-matlab" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-maze_highlight_rules" {
    export const MazeHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-maze" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-mediawiki_highlight_rules" {
    export const MediaWikiHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-mediawiki" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-mel_highlight_rules" {
    export const MELHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-mel" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-mips_highlight_rules" {
    export const MIPSHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-mips" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-mixal_highlight_rules" {
    export const MixalHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-mixal" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-mushcode_highlight_rules" {
    export const MushCodeRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-mushcode" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-mysql_highlight_rules" {
    export const MysqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-mysql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-nasal_highlight_rules" {
    export const NasalHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-nasal" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-nginx_highlight_rules" {
    export const NginxHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-nginx" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-nim_highlight_rules" {
    export const NimHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-nim" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-nix_highlight_rules" {
    export const NixHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-nix" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-nsis_highlight_rules" {
    export const NSISHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-nsis" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-nunjucks_highlight_rules" {
    export const NunjucksHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-nunjucks" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-objectivec_highlight_rules" {
    export const ObjectiveCHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-objectivec" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-ocaml_highlight_rules" {
    export const OcamlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-ocaml" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-odin_highlight_rules" {
    export const OdinHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-odin" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-partiql_highlight_rules" {
    export const PartiqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-partiql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-pascal_highlight_rules" {
    export const PascalHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-pascal" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-perl_highlight_rules" {
    export const PerlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-perl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-pgsql_highlight_rules" {
    export const PgsqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-pgsql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-php_completions" {
    export const PhpCompletions: new () => import("ace-builds").Ace.Completion;
}

declare module "ace-builds/src-noconflict/mode-php_highlight_rules" {
    export const PhpHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
    export const PhpLangHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-php_laravel_blade_highlight_rules" {
    export const PHPLaravelBladeHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-php_laravel_blade" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-php" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-pig_highlight_rules" {
    export const PigHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-pig" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-plain_text" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-plsql_highlight_rules" {
    export const plsqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-plsql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-powershell_highlight_rules" {
    export const PowershellHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-powershell" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-praat_highlight_rules" {
    export const PraatHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-praat" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-prisma_highlight_rules" {
    export const PrismaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-prisma" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-prolog_highlight_rules" {
    export const PrologHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-prolog" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-properties_highlight_rules" {
    export const PropertiesHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-properties" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-protobuf_highlight_rules" {
    export const ProtobufHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-protobuf" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-prql_highlight_rules" {
    export const PrqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-prql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-puppet_highlight_rules" {
    export const PuppetHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-puppet" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-python_highlight_rules" {
    export const PythonHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-python" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-qml_highlight_rules" {
    export const QmlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-qml" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-r_highlight_rules" {
    export const RHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-r" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-raku_highlight_rules" {
    export const RakuHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-raku" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-razor_completions" {
    export const RazorCompletions: new () => import("ace-builds").Ace.Completion;
}

declare module "ace-builds/src-noconflict/mode-razor_highlight_rules" {
    export const RazorHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
    export const RazorLangHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-razor" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-rdoc_highlight_rules" {
    export const RDocHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-rdoc" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-red_highlight_rules" {
    export const RedHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-red" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-redshift_highlight_rules" {
    export const RedshiftHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-redshift" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-rhtml_highlight_rules" {
    export const RHtmlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-rhtml" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-robot_highlight_rules" {
    export const RobotHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-robot" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-rst_highlight_rules" {
    export const RSTHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-rst" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-ruby_highlight_rules" {
    export const constantOtherSymbol: { token: string; regex: string; };
    export const qString: { token: string; regex: string; };
    export const qqString: { token: string; regex: string; };
    export const tString: { token: string; regex: string; };
    export const constantNumericHex: { token: string; regex: string; };
    export const constantNumericBinary: { token: string; regex: RegExp; };
    export const constantNumericDecimal: { token: string; regex: RegExp; };
    export const constantNumericOctal: { token: string; regex: RegExp; };
    export const constantNumericRational: { token: string; regex: RegExp; };
    export const constantNumericComplex: { token: string; regex: RegExp; };
    export const constantNumericFloat: { token: string; regex: string; };
    export const instanceVariable: { token: string; regex: string; };
    export const RubyHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-ruby" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-rust_highlight_rules" {
    export const RustHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-rust" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-sac_highlight_rules" {
    export const sacHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-sac" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-sass_highlight_rules" {
    export const SassHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-sass" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-scad_highlight_rules" {
    export const scadHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-scad" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-scala_highlight_rules" {
    export const ScalaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-scala" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-scheme_highlight_rules" {
    export const SchemeHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-scheme" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-scrypt_highlight_rules" {
    export const scryptHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-scrypt" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-scss_highlight_rules" {
    export const ScssHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-scss" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-sh_highlight_rules" {
    export const reservedKeywords: string;
    export const languageConstructs: string;
    export const ShHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-sh" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-sjs_highlight_rules" {
    export const SJSHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-sjs" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-slim_highlight_rules" {
    export const SlimHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-slim" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-smarty_highlight_rules" {
    export const SmartyHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-smarty" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-smithy_highlight_rules" {
    export const SmithyHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-smithy" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-snippets" {
    export const SnippetHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
    export const SnippetGroupHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-soy_template_highlight_rules" {
    export const SoyTemplateHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-soy_template" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-space_highlight_rules" {
    export const SpaceHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-space" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-sparql_highlight_rules" {
    export const SPARQLHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-sparql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-sql_highlight_rules" {
    export const SqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-sql" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-sqlserver_highlight_rules" {
    export const SqlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-sqlserver" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-stylus_highlight_rules" {
    export const StylusHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-stylus" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-svg_highlight_rules" {
    export const SvgHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-svg" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-swift_highlight_rules" {
    export const HighlightRules: new () => import("ace-builds").Ace.HighlightRules;
    export const SwiftHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-swift" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-tcl_highlight_rules" {
    export const TclHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-tcl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-terraform_highlight_rules" {
    export const TerraformHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-terraform" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-tex_highlight_rules" {
    export const TexHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-tex" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-text_highlight_rules" {
    export const TextHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-text" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-textile_highlight_rules" {
    export const TextileHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-textile" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-toml_highlight_rules" {
    export const TomlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-toml" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-tsv_highlight_rules" {
    export const TsvHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-tsv" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-tsx_highlight_rules" {
    export const TsxHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-tsx" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-turtle_highlight_rules" {
    export const TurtleHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-turtle" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-twig_highlight_rules" {
    export const TwigHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-twig" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-typescript_highlight_rules" {
    export const TypeScriptHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-typescript" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-vala_highlight_rules" {
    export const ValaHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-vala" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-vbscript_highlight_rules" {
    export const VBScriptHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-vbscript" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-velocity_highlight_rules" {
    export const VelocityHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-velocity" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-verilog_highlight_rules" {
    export const VerilogHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-verilog" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-vhdl_highlight_rules" {
    export const VHDLHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-vhdl" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-visualforce_highlight_rules" {
    export const VisualforceHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-visualforce" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-vue_highlight_rules" {
    export const VueHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-vue" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-wollok_highlight_rules" {
    export const WollokHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-wollok" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-xml_highlight_rules" {
    export const XmlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-xml" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-yaml_highlight_rules" {
    export const YamlHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-yaml" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-zeek_highlight_rules" {
    export const ZeekHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-zeek" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}

declare module "ace-builds/src-noconflict/mode-zig_highlight_rules" {
    export const ZigHighlightRules: new () => import("ace-builds").Ace.HighlightRules;
}

declare module "ace-builds/src-noconflict/mode-zig" {
    export const Mode: new () => import("ace-builds").Ace.SyntaxMode;
}